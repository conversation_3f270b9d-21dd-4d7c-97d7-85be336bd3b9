/* [project]/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css [client] (css) */
.quoted-tweet-header-module__Cx2l9W__header {
  display: flex;
  padding: .75rem .75rem 0;
  line-height: var(--tweet-header-line-height);
  font-size: var(--tweet-header-font-size);
  white-space: nowrap;
  overflow-wrap: break-word;
  overflow: hidden;
}

.quoted-tweet-header-module__Cx2l9W__avatar {
  position: relative;
  height: 20px;
  width: 20px;
}

.quoted-tweet-header-module__Cx2l9W__avatarSquare {
  border-radius: 4px;
}

.quoted-tweet-header-module__Cx2l9W__author {
  display: flex;
  margin: 0 .5rem;
}

.quoted-tweet-header-module__Cx2l9W__authorText {
  font-weight: 700;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.quoted-tweet-header-module__Cx2l9W__username {
  color: var(--tweet-font-color-secondary);
  text-decoration: none;
  text-overflow: ellipsis;
  margin-left: .125rem;
}

/*# sourceMappingURL=8069e_react-tweet_dist_twitter-theme_quoted-tweet_quoted-tweet-header_module_73511378.css.map*/