/* [project]/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css [client] (css) */
.tweet-actions-module__TCXXfW__actions {
  display: flex;
  align-items: center;
  color: var(--tweet-font-color-secondary);
  padding-top: .25rem;
  margin-top: .25rem;
  border-top: var(--tweet-border);
  overflow-wrap: break-word;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tweet-actions-module__TCXXfW__like, .tweet-actions-module__TCXXfW__reply, .tweet-actions-module__TCXXfW__copy {
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  margin-right: 1.25rem;
}

.tweet-actions-module__TCXXfW__like:hover, .tweet-actions-module__TCXXfW__reply:hover, .tweet-actions-module__TCXXfW__copy:hover {
  background-color: #0000;
}

.tweet-actions-module__TCXXfW__like:hover > .tweet-actions-module__TCXXfW__likeIconWrapper {
  background-color: var(--tweet-color-red-primary-hover);
}

.tweet-actions-module__TCXXfW__like:hover > .tweet-actions-module__TCXXfW__likeCount {
  color: var(--tweet-color-red-primary);
  text-decoration-line: underline;
}

.tweet-actions-module__TCXXfW__likeIconWrapper, .tweet-actions-module__TCXXfW__replyIconWrapper, .tweet-actions-module__TCXXfW__copyIconWrapper {
  width: var(--tweet-actions-icon-wrapper-size);
  height: var(--tweet-actions-icon-wrapper-size);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: -.25rem;
  border-radius: 9999px;
}

.tweet-actions-module__TCXXfW__likeIcon, .tweet-actions-module__TCXXfW__replyIcon, .tweet-actions-module__TCXXfW__copyIcon {
  height: var(--tweet-actions-icon-size);
  fill: currentColor;
  user-select: none;
}

.tweet-actions-module__TCXXfW__likeIcon {
  color: var(--tweet-color-red-primary);
}

.tweet-actions-module__TCXXfW__likeCount, .tweet-actions-module__TCXXfW__replyText, .tweet-actions-module__TCXXfW__copyText {
  font-size: var(--tweet-actions-font-size);
  font-weight: var(--tweet-actions-font-weight);
  line-height: var(--tweet-actions-line-height);
  margin-left: .25rem;
}

.tweet-actions-module__TCXXfW__reply:hover > .tweet-actions-module__TCXXfW__replyIconWrapper {
  background-color: var(--tweet-color-blue-secondary-hover);
}

.tweet-actions-module__TCXXfW__reply:hover > .tweet-actions-module__TCXXfW__replyText {
  color: var(--tweet-color-blue-secondary);
  text-decoration-line: underline;
}

.tweet-actions-module__TCXXfW__replyIcon {
  color: var(--tweet-color-blue-primary);
}

.tweet-actions-module__TCXXfW__copy {
  font: inherit;
  background: none;
  border: none;
  cursor: pointer;
}

.tweet-actions-module__TCXXfW__copy:hover > .tweet-actions-module__TCXXfW__copyIconWrapper {
  background-color: var(--tweet-color-green-primary-hover);
}

.tweet-actions-module__TCXXfW__copy:hover .tweet-actions-module__TCXXfW__copyIcon {
  color: var(--tweet-color-green-primary);
}

.tweet-actions-module__TCXXfW__copy:hover > .tweet-actions-module__TCXXfW__copyText {
  color: var(--tweet-color-green-primary);
  text-decoration-line: underline;
}

/*# sourceMappingURL=node_modules_react-tweet_dist_twitter-theme_tweet-actions_module_73511378.css.map*/