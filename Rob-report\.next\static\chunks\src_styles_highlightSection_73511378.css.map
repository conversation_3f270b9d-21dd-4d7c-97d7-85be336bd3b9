{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/highlightSection.css"], "sourcesContent": [".wrapper_base2 {\r\n  /* padding: 152px 0; */\r\n  font-size: 1vw;\r\n}\r\n.flexbox_wrapper {\r\n  grid-column-gap: 6em;\r\n  grid-row-gap: 6em;\r\n  justify-content: flex-start;\r\n  align-items: flex-start;\r\n  display: flex;\r\n  /* height: 40vh; */\r\n}\r\n.flexbox_wrapper.special_layout {\r\n  grid-column-gap: 0em;\r\n  grid-row-gap: 0em;\r\n}\r\n.l_side_tall {\r\n  width: 44%;\r\n  margin-right: auto;\r\n  padding-right: 3.9em;\r\n  position: relative;\r\n  /* top: 6em; */\r\n}\r\n.headline_blog_home {\r\n  grid-column-gap: 6px;\r\n  grid-row-gap: 6px;\r\n  flex-flow: column;\r\n  margin-bottom: 24px;\r\n  display: flex;\r\n}\r\n.article_headline {\r\n  width: auto;\r\n  font-size: 2.5em;\r\n  line-height: 1.1;\r\n  font-family: rocky, sans-serif;\r\n  font-weight: 600;\r\n}\r\n.b_txt {\r\n  letter-spacing: 0.02em;\r\n  font-size: 1.1rem;\r\n  line-height: 1.2;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.img_blog.r_side {\r\n  width: 55.8%;\r\n  /* height: 52em; */\r\n  margin-left: auto;\r\n  overflow: hidden;\r\n}\r\n.highlightimage {\r\n  object-fit: cover;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.button_base.black {\r\n  transition: all 0.5s ease;\r\n  border: none;\r\n}\r\n.button_base.black:hover {\r\n  background-color: #323440e6;\r\n}\r\n@media screen and (max-width: 1200px) {\r\n  .l_side_tall {\r\n    width: 50%;\r\n  }\r\n  .b_txt {\r\n    font-size: 1.2em;\r\n  }\r\n  .button_base.black {\r\n    font-size: 2vw;\r\n  }\r\n}\r\n@media screen and (max-width: 991px) {\r\n  .l_side_tall {\r\n    width: 50%;\r\n    padding-right: 4em;\r\n  }\r\n  .article_headline {\r\n    font-size: 4em;\r\n  }\r\n  .b_txt {\r\n    letter-spacing: 0.03em;\r\n    font-size: 1.6em;\r\n  }\r\n  .button_base {\r\n    padding-top: 20px;\r\n    padding-bottom: 18px;\r\n    /* font-size: 3vw; */\r\n  }\r\n  .button_base.black {\r\n    font-size: 2vw;\r\n  }\r\n  .img_blog.r_side {\r\n    width: 50%;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .flexbox_wrapper.special_layout {\r\n    flex-flow: column-reverse;\r\n  }\r\n  .l_side_tall {\r\n    width: 100%;\r\n    position: static;\r\n  }\r\n  .article_headline {\r\n    font-size: 6em;\r\n  }\r\n  .b_txt,\r\n  .b_txt.white {\r\n    /* font-size: 2.15em; */\r\n    font-size: var(--desktop-paragraph-font--small-p);\r\n  }\r\n  .button_base.black {\r\n    font-size: 3vw;\r\n  }\r\n  .img_blog.r_side {\r\n    width: 100%;\r\n    /* height: 92em; */\r\n    /* margin-top: 32px; */\r\n  }\r\n}\r\n@media screen and (max-width: 479px) {\r\n  .flexbox_wrapper.special_layout,\r\n  .flexbox_wrapper.partnerships,\r\n  .flexbox_wrapper.spec_part {\r\n    flex-flow: column-reverse;\r\n  }\r\n  .l_side_tall {\r\n    width: 100%;\r\n    padding-right: 0;\r\n    position: static;\r\n  }\r\n  .headline_blog_home {\r\n    margin-bottom: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  .article_headline {\r\n    font-size: 7.1em;\r\n  }\r\n  .b_txt {\r\n    font-size: 4.5em;\r\n    font-size: var(--desktop-paragraph-font--small-p);\r\n  }\r\n  .button_base.black {\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    font-size: 3.6vw;\r\n    line-height: 1.5;\r\n    display: flex;\r\n  }\r\n  .img_blog.r_side {\r\n    width: 100%;\r\n    /* height: 102em; */\r\n    /* margin-top: 42px; */\r\n    margin-left: 0;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;;;AAQA;;;;;AAIA;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;AAMA;;;;;;AAKA;;;;;AAIA;;;;AAGA;EACE;;;;EAGA;;;;EAGA;;;;;AAIF;EACE;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAKA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAKA;;;;EAGA;;;;;AAMF;EACE;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;;;;;EAQA"}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}