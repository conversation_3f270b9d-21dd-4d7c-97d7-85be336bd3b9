{"version": 3, "sources": [], "sections": [{"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/pages/_document.jsx"], "sourcesContent": ["import { Html, Head, Main, NextScript } from \"next/document\";\r\nimport Script from \"next/script\";\r\n\r\nexport default function Document() {\r\n  return (\r\n    <Html lang=\"en\">\r\n      <Head>\r\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/smz6nuo.css\" />\r\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/izt7oyh.css\" />\r\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/gty1suo.css\" />\r\n        <link\r\n          rel=\"preload\"\r\n          href=\"https://securepubads.g.doubleclick.net/tag/js/gpt.js\"\r\n          as=\"script\"\r\n        />\r\n        {/* Google Tag Manager (gtag.js) */}\r\n        <script\r\n          async\r\n          src=\"https://www.googletagmanager.com/gtag/js?id=G-3G2PKDJZCD\"\r\n        ></script>\r\n        <script\r\n          dangerouslySetInnerHTML={{\r\n            __html: `\r\n          window.dataLayer = window.dataLayer || [];\r\n          function gtag(){dataLayer.push(arguments);}\r\n          gtag('js', new Date());\r\n          gtag('config', 'G-3G2PKDJZCD');\r\n          `,\r\n          }}\r\n        />\r\n      </Head>\r\n      <body>\r\n        <Main />\r\n        <Script\r\n          async\r\n          src=\"https://securepubads.g.doubleclick.net/tag/js/gpt.js\"\r\n          strategy=\"beforeInteractive\"\r\n        />\r\n        <NextScript />\r\n      </body>\r\n    </Html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,qKAAC,yHAAA,CAAA,OAAI;QAAC,MAAK;;0BACT,qKAAC,yHAAA,CAAA,OAAI;;kCACH,qKAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,qKAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,qKAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,qKAAC;wBACC,KAAI;wBACJ,MAAK;wBACL,IAAG;;;;;;kCAGL,qKAAC;wBACC,KAAK;wBACL,KAAI;;;;;;kCAEN,qKAAC;wBACC,yBAAyB;4BACvB,QAAQ,CAAC;;;;;UAKX,CAAC;wBACD;;;;;;;;;;;;0BAGJ,qKAAC;;kCACC,qKAAC,yHAAA,CAAA,OAAI;;;;;kCACL,qKAAC,uHAAA,CAAA,UAAM;wBACL,KAAK;wBACL,KAAI;wBACJ,UAAS;;;;;;kCAEX,qKAAC,yHAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/helpers/MenuData.jsx"], "sourcesContent": ["export const menus = [\r\n  {\r\n    name: \"Motoring\",\r\n    link: \"/motoring\",\r\n    submenus: [\r\n      {\r\n        name: \"Cars\",\r\n        link: \"/motoring/cars\",\r\n      },\r\n      {\r\n        name: \"Bikes\",\r\n        link: \"/motoring/bikes\",\r\n      },\r\n      {\r\n        name: \"Vintage & Classics\",\r\n        link: \"/motoring/vintage-and-classics\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Yachting & Aviation\",\r\n    link: \"/yachting-and-aviation\",\r\n    submenus: [\r\n      {\r\n        name: \"Yachting\",\r\n        link: \"/yachting-and-aviation/yachting\",\r\n      },\r\n      {\r\n        name: \"Aviation\",\r\n        link: \"/yachting-and-aviation/aviation\",\r\n      },\r\n      {\r\n        name: \"Cruises & Expeditions\",\r\n        link: \"/yachting-and-aviation/cruises-and-expeditions\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Style\",\r\n    link: \"/style\",\r\n    submenus: [\r\n      {\r\n        name: \"Timepieces\",\r\n        link: \"/style/timepieces\",\r\n      },\r\n\r\n      {\r\n        name: \"Jewellery & Accessories\",\r\n        link: \"/style/jewellery-and-accessories\",\r\n      },\r\n      {\r\n        name: \"Fashion & Beauty\",\r\n        link: \"/style/fashion-and-beauty\",\r\n      },\r\n      {\r\n        name: \"Bespoke\",\r\n        link: \"/style/bespoke\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Home & Design\",\r\n    link: \"/home-and-design\",\r\n    submenus: [\r\n      {\r\n        name: \"Interiors & Architecture\",\r\n        link: \"/home-and-design/interiors-and-architecture\",\r\n      },\r\n      {\r\n        name: \"Real Estate\",\r\n        link: \"/home-and-design/real-estate\",\r\n      },\r\n      {\r\n        name: \"Art\",\r\n        link: \"/home-and-design/art\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Travel\",\r\n    link: \"/travel\",\r\n    submenus: [\r\n      {\r\n        name: \"Wellness & Spas\",\r\n        link: \"/travel/wellness-and-spas\",\r\n      },\r\n      {\r\n        name: \"India\",\r\n        link: \"/travel/india\",\r\n      },\r\n      {\r\n        name: \"International\",\r\n        link: \"/travel/international\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Food & Drink\",\r\n    link: \"/food-and-drink\",\r\n    submenus: [\r\n      {\r\n        name: \"Gastronomy\",\r\n        link: \"/food-and-drink/gastronomy\",\r\n      },\r\n      {\r\n        name: \"Spirits\",\r\n        link: \"/food-and-drink/spirits\",\r\n      },\r\n    ],\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,QAAQ;IACnB;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YAEA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;CACD", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/Menu.jsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport {\r\n  MdOutlineArrowBackIosNew,\r\n  MdOutlineArrowForwardIos,\r\n  MdClose,\r\n} from \"react-icons/md\";\r\nimport { LuFacebook } from \"react-icons/lu\";\r\nimport { GrInstagram } from \"react-icons/gr\";\r\nimport { BsTwitterX } from \"react-icons/bs\";\r\nimport { FaPinterestP, FaSnapchatGhost, FaYoutube } from \"react-icons/fa\";\r\nimport { menus } from \"@/helpers/MenuData\";\r\n\r\nconst Menu = ({ openMenu, setOpenMenu }) => {\r\n  const [openSubMenu, setOpenSubMenu] = useState(null);\r\n\r\n  useEffect(() => {\r\n    document.body.classList.toggle(\"overflow-hidden\", openMenu);\r\n    return () => document.body.classList.remove(\"overflow-hidden\");\r\n  }, [openMenu]);\r\n\r\n  const handleSubMenuToggle = (index) => {\r\n    if (openSubMenu === index) {\r\n      setOpenSubMenu(null); // Close submenu if it's already open\r\n    } else {\r\n      setOpenSubMenu(index); // Open the selected submenu and hide others\r\n    }\r\n  };\r\n\r\n  // Handle the back button click to reset and show all menu items\r\n  const handleBackButtonClick = () => {\r\n    setOpenSubMenu(null); // Reset the submenu and show all menu items\r\n  };\r\n  const onClickHandler = () => {\r\n    setOpenMenu(false); // Show\r\n    setOpenSubMenu(null); // Close submenu if it's already open\r\n  };\r\n  return (\r\n    <div\r\n      className=\"menu-cont\"\r\n      style={{\r\n        transform: openMenu ? \"translateX(0%)\" : \"translateX(100%)\",\r\n      }}\r\n    >\r\n      <div className=\"menu-inner\">\r\n        <div>\r\n          <div className=\"manu-inner-block\">\r\n            <div className=\"menu-top\">\r\n              <div\r\n                className=\"menu-back flex-all \"\r\n                style={{ opacity: openSubMenu !== null ? \"1\" : \"0\" }}\r\n                onClick={handleBackButtonClick}\r\n              >\r\n                <MdOutlineArrowBackIosNew />\r\n              </div>\r\n              <div className=\"menu-search-close\">\r\n                {/* <div className=\"menu-search t3 flex-all\">Search</div> */}\r\n                <div\r\n                  className=\"menu-close flex-all\"\r\n                  onClick={() => setOpenMenu(false)}\r\n                >\r\n                  <MdClose />\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"menu-main\">\r\n              <div className=\"menu-item-wrapper\">\r\n                {menus.map((item, index) => (\r\n                  <div key={`manu-item-${index}`}>\r\n                    {/* Render menu item and submenu */}\r\n                    {openSubMenu === item.name ? (\r\n                      <div className=\"submenu-body d-flex\">\r\n                        <div className=\"submenu-head\">\r\n                          <Link\r\n                            onClick={() => setOpenMenu(false)}\r\n                            href={item.link}\r\n                            className=\"submenu-title\"\r\n                          >\r\n                            {item.name}\r\n                          </Link>\r\n                        </div>\r\n                        <div className=\"submenu-main-body\">\r\n                          {item.submenus.map((submenuItem, submenuIndex) => (\r\n                            <div\r\n                              key={`submenu-item-${submenuIndex}`}\r\n                              className=\"submenu-items\"\r\n                            >\r\n                              <Link\r\n                                href={submenuItem.link}\r\n                                className=\"submenu-name\"\r\n                                onClick={onClickHandler}\r\n                              >\r\n                                {submenuItem.name}\r\n                              </Link>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <div\r\n                        className={`menu-items ${\r\n                          openSubMenu === null || openSubMenu === item.name\r\n                            ? \"\"\r\n                            : \"d-none\"\r\n                        }`}\r\n                      >\r\n                        <Link\r\n                          href={item?.link}\r\n                          onClick={() => setOpenMenu(false)}\r\n                          className=\"menu-name\"\r\n                        >\r\n                          {item.name}\r\n                        </Link>\r\n                        <div\r\n                          onClick={() => handleSubMenuToggle(item.name)}\r\n                          className=\"menu-arrow\"\r\n                        >\r\n                          <MdOutlineArrowForwardIos />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <div className=\"menu-extras\">\r\n                <div className=\"menu-ext-wrapper\">\r\n                  <div className=\"menu-ext\">\r\n                    <Link href=\"/about-us\" rel=\"nofollow\">\r\n                      About us\r\n                    </Link>\r\n                  </div>\r\n                  <div className=\"menu-ext\" rel=\"nofollow\">\r\n                    <Link href=\"/contact-us\" rel=\"nofollow\">\r\n                      Contact us\r\n                    </Link>\r\n                  </div>\r\n                  {/* <div className=\"menu-ext\">\r\n                    <Link href=\"#\"   rel=\"nofollow\">Work with us</Link>\r\n                  </div> */}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"menu-btm\">\r\n              <div className=\"menu-ext\">\r\n                <span className=\"menu-follows-text\">Follow us</span>\r\n                <div className=\"menu-follows\">\r\n                  <div className=\"menu-follows-items\">\r\n                    <Link\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      href=\"https://www.instagram.com/robbreportindia/\"\r\n                    >\r\n                      <GrInstagram />\r\n                    </Link>\r\n                    {/* <Link\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      href=\"https://www.facebook.com/robbreportindia\"\r\n                    >\r\n                      <LuFacebook />\r\n                    </Link>\r\n                    <Link\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      style={{ position: \"relative\" }}\r\n                      href=\"https://x.com/robbreportin\"\r\n                    >\r\n                      <BsTwitterX />\r\n                    </Link>\r\n                    <Link\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      href=\"https://snapchat.com/t/d00XT38z\"\r\n                    >\r\n                      <FaSnapchatGhost />\r\n                    </Link>\r\n                    <Link\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      href=\"https://pin.it/FOcox8LZJ\"\r\n                    >\r\n                      <FaPinterestP />\r\n                    </Link> */}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Menu;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAMA;AAGA;;;;;;;;;;AAEA,MAAM,OAAO,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB;QAClD,OAAO,IAAM,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC9C,GAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB,OAAO;YACzB,eAAe,OAAO,qCAAqC;QAC7D,OAAO;YACL,eAAe,QAAQ,4CAA4C;QACrE;IACF;IAEA,gEAAgE;IAChE,MAAM,wBAAwB;QAC5B,eAAe,OAAO,4CAA4C;IACpE;IACA,MAAM,iBAAiB;QACrB,YAAY,QAAQ,OAAO;QAC3B,eAAe,OAAO,qCAAqC;IAC7D;IACA,qBACE,qKAAC;QACC,WAAU;QACV,OAAO;YACL,WAAW,WAAW,mBAAmB;QAC3C;kBAEA,cAAA,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;0BACC,cAAA,qKAAC;oBAAI,WAAU;;sCACb,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,SAAS,gBAAgB,OAAO,MAAM;oCAAI;oCACnD,SAAS;8CAET,cAAA,qKAAC,uIAAA,CAAA,2BAAwB;;;;;;;;;;8CAE3B,qKAAC;oCAAI,WAAU;8CAEb,cAAA,qKAAC;wCACC,WAAU;wCACV,SAAS,IAAM,YAAY;kDAE3B,cAAA,qKAAC,uIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;sCAId,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;8CACZ,oHAAA,CAAA,QAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChB,qKAAC;sDAEE,gBAAgB,KAAK,IAAI,iBACxB,qKAAC;gDAAI,WAAU;;kEACb,qKAAC;wDAAI,WAAU;kEACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;4DACH,SAAS,IAAM,YAAY;4DAC3B,MAAM,KAAK,IAAI;4DACf,WAAU;sEAET,KAAK,IAAI;;;;;;;;;;;kEAGd,qKAAC;wDAAI,WAAU;kEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,aAAa,6BAC/B,qKAAC;gEAEC,WAAU;0EAEV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oEACH,MAAM,YAAY,IAAI;oEACtB,WAAU;oEACV,SAAS;8EAER,YAAY,IAAI;;;;;;+DARd,CAAC,aAAa,EAAE,cAAc;;;;;;;;;;;;;;;qEAe3C,qKAAC;gDACC,WAAW,CAAC,WAAW,EACrB,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,GAC7C,KACA,UACJ;;kEAEF,qKAAC,qHAAA,CAAA,UAAI;wDACH,MAAM,MAAM;wDACZ,SAAS,IAAM,YAAY;wDAC3B,WAAU;kEAET,KAAK,IAAI;;;;;;kEAEZ,qKAAC;wDACC,SAAS,IAAM,oBAAoB,KAAK,IAAI;wDAC5C,WAAU;kEAEV,cAAA,qKAAC,uIAAA,CAAA,2BAAwB;;;;;;;;;;;;;;;;2CAjDvB,CAAC,UAAU,EAAE,OAAO;;;;;;;;;;8CAwDlC,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC;wCAAI,WAAU;;0DACb,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,KAAI;8DAAW;;;;;;;;;;;0DAIxC,qKAAC;gDAAI,WAAU;gDAAW,KAAI;0DAC5B,cAAA,qKAAC,qHAAA,CAAA,UAAI;oDAAC,MAAK;oDAAc,KAAI;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUhD,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;;kDACb,qKAAC;wCAAK,WAAU;kDAAoB;;;;;;kDACpC,qKAAC;wCAAI,WAAU;kDACb,cAAA,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,QAAO;gDACP,KAAI;gDACJ,MAAK;0DAEL,cAAA,qKAAC,uIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwClC;uCAEe", "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/utils/GetTheme.jsx"], "sourcesContent": ["import { useTheme } from \"next-themes\";\r\nimport React from \"react\";\r\n\r\nconst GetTheme = () => {\r\n  const { theme, setTheme } = useTheme();\r\n\r\n  return { theme, setTheme };\r\n};\r\n\r\nexport default GetTheme;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAEA,MAAM,WAAW;IACf,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEnC,OAAO;QAAE;QAAO;IAAS;AAC3B;uCAEe", "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/TransitionLoader.jsx"], "sourcesContent": ["import GetTheme from \"@/utils/GetTheme\";\r\nimport { useGSAP } from \"@gsap/react\";\r\nimport gsap from \"gsap\";\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\nconst TransitionLoader = () => {\r\n  const { theme, setTheme } = GetTheme();\r\n  const [tl, setTl] = useState(null); // State to hold the GSAP timeline\r\n  // Initialize the GSAP timeline\r\n  useEffect(() => {\r\n    const timeline = gsap.timeline({ paused: true });\r\n    timeline\r\n      .to(\".modes_helpers .a\", {\r\n        duration: 0.5,\r\n        scaleY: 1,\r\n        ease: \"power3.inOut\",\r\n        transformOrigin: \"0 0\",\r\n      })\r\n      .to(\r\n        \".modes_helpers .b\",\r\n        {\r\n          duration: 0.5,\r\n          scaleY: 1,\r\n          ease: \"power3.inOut\",\r\n          transformOrigin: \"0 0\",\r\n        },\r\n        0.3\r\n      )\r\n      .set(\r\n        \".modes_helpers .a\",\r\n        {\r\n          scaleY: 0,\r\n          transformOrigin: \"100% 100%\",\r\n        },\r\n        0.8\r\n      )\r\n      .to(\r\n        \".mode_toggle span i\",\r\n        0.5,\r\n        {\r\n          y: 0,\r\n          autoAlpha: 1,\r\n          ease: \"Power3.easeOut\",\r\n        },\r\n        0.8\r\n      )\r\n      .to(\r\n        \".modes_helpers .b\",\r\n        0.5,\r\n        {\r\n          scaleY: 0,\r\n          ease: \"Power3.easeOut\",\r\n          transformOrigin: \"100% 100%\",\r\n        },\r\n        0.8\r\n      );\r\n    setTl(timeline);\r\n  }, []);\r\n\r\n  // Effect to react to theme changes and play/reverse the timeline\r\n  useEffect(() => {\r\n    if (tl) {\r\n      if (theme === \"dark\") {\r\n        tl.reverse();\r\n      } else {\r\n        tl.play();\r\n      }\r\n    }\r\n  }, [theme, tl]);\r\n\r\n  return (\r\n    <div className=\"modes_helpers\">\r\n      <i className=\"a full_bgTransition\" />\r\n      <i className=\"b full_bgTransition\" />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TransitionLoader;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,mBAAmB;IACvB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,UAAQ,AAAD;IACnC,MAAM,CAAC,IAAI,MAAM,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,kCAAkC;IACtE,+BAA+B;IAC/B,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;YAAE,QAAQ;QAAK;QAC9C,SACG,EAAE,CAAC,qBAAqB;YACvB,UAAU;YACV,QAAQ;YACR,MAAM;YACN,iBAAiB;QACnB,GACC,EAAE,CACD,qBACA;YACE,UAAU;YACV,QAAQ;YACR,MAAM;YACN,iBAAiB;QACnB,GACA,KAED,GAAG,CACF,qBACA;YACE,QAAQ;YACR,iBAAiB;QACnB,GACA,KAED,EAAE,CACD,uBACA,KACA;YACE,GAAG;YACH,WAAW;YACX,MAAM;QACR,GACA,KAED,EAAE,CACD,qBACA,KACA;YACE,QAAQ;YACR,MAAM;YACN,iBAAiB;QACnB,GACA;QAEJ,MAAM;IACR,GAAG,EAAE;IAEL,iEAAiE;IACjE,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,IAAI;YACN,IAAI,UAAU,QAAQ;gBACpB,GAAG,OAAO;YACZ,OAAO;gBACL,GAAG,IAAI;YACT;QACF;IACF,GAAG;QAAC;QAAO;KAAG;IAEd,qBACE,qKAAC;QAAI,WAAU;;0BACb,qKAAC;gBAAE,WAAU;;;;;;0BACb,qKAAC;gBAAE,WAAU;;;;;;;;;;;;AAGnB;uCAEe", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/SearchButton.jsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport React from \"react\";\r\nimport { CiSearch } from \"react-icons/ci\";\r\nconst SearchButton = ({setVisible}) => {\r\n\r\n  return (\r\n    <div onClick={()=>setVisible(true)} className={`search_btn light show dark`}>\r\n      <button className=\"search-btn\" aria-label=\"Search Button\">\r\n        <CiSearch />\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SearchButton;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AACA,MAAM,eAAe,CAAC,EAAC,UAAU,EAAC;IAEhC,qBACE,qKAAC;QAAI,SAAS,IAAI,WAAW;QAAO,WAAW,CAAC,0BAA0B,CAAC;kBACzE,cAAA,qKAAC;YAAO,WAAU;YAAa,cAAW;sBACxC,cAAA,qKAAC,uIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/buttons/Button.jsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport React from \"react\";\r\n\r\nconst Button = ({\r\n  href = \"#\",\r\n  target = null,\r\n  onClick = null,\r\n  className=\"\",\r\n  children,\r\n}) => {\r\n  return (\r\n    <>\r\n      {onClick ? (\r\n        <button\r\n          onClick={onClick}\r\n          className={`button_base black w-inline-block ${className}`}\r\n        >\r\n          {children}\r\n        </button>\r\n      ) : (\r\n        <Link\r\n          href={href}\r\n          target={target}\r\n          className={`button_base black w-inline-block ${className}`}\r\n        >\r\n          {children}\r\n        </Link>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Button;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS,CAAC,EACd,OAAO,GAAG,EACV,SAAS,IAAI,EACb,UAAU,IAAI,EACd,YAAU,EAAE,EACZ,QAAQ,EACT;IACC,qBACE;kBACG,wBACC,qKAAC;YACC,SAAS;YACT,WAAW,CAAC,iCAAiC,EAAE,WAAW;sBAEzD;;;;;iCAGH,qKAAC,qHAAA,CAAA,UAAI;YACH,MAAM;YACN,QAAQ;YACR,WAAW,CAAC,iCAAiC,EAAE,WAAW;sBAEzD;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/buttons/BoxBorderBtn.jsx"], "sourcesContent": ["import Link from 'next/link';\r\nimport React from 'react';\r\n\r\nexport const BoxBorderBtn = ({ onClick = null, href = \"#\",isSearchDrawer,handleClose ,children }) => {\r\n    return (\r\n        <>\r\n            {\r\n                onClick ? (\r\n                    <div className=\"border_btn\">\r\n                        <button onClick={onClick} className=\"border_btn_button\">\r\n                            {children}\r\n                        </button >\r\n                    </div >\r\n                ) : (\r\n                    <div className=\"border_btn\">\r\n                        <Link href={href}>\r\n                            <button onClick={isSearchDrawer ? handleClose : undefined} className=\"border_btn_button\">\r\n                                {children}\r\n                            </button >\r\n                        </Link >\r\n                    </div >\r\n                )}\r\n\r\n        </>\r\n\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,MAAM,eAAe,CAAC,EAAE,UAAU,IAAI,EAAE,OAAO,GAAG,EAAC,cAAc,EAAC,WAAW,EAAE,QAAQ,EAAE;IAC5F,qBACI;kBAEQ,wBACI,qKAAC;YAAI,WAAU;sBACX,cAAA,qKAAC;gBAAO,SAAS;gBAAS,WAAU;0BAC/B;;;;;;;;;;iCAIT,qKAAC;YAAI,WAAU;sBACX,cAAA,qKAAC,qHAAA,CAAA,UAAI;gBAAC,MAAM;0BACR,cAAA,qKAAC;oBAAO,SAAS,iBAAiB,cAAc;oBAAW,WAAU;8BAChE;;;;;;;;;;;;;;;;;AASjC", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/helpers/ChipData.jsx"], "sourcesContent": ["export const chipData = [\r\n  { name: \"Luxury Cars\", slug: \"/result/luxury-cars/1\" },\r\n  { name: \"Private Jets\", slug: \"/result/private-jets/1\" },\r\n  { name: \"Yachts\", slug: \"/result/yachts/1\" },\r\n  { name: \"Timepieces\", slug: \"/result/timepieces/1\" },\r\n  { name: \"Fine Dining\", slug: \"/result/fine-dining/1\" },\r\n  { name: \"Vintage Cars\", slug: \"/result/vintage-cars/1\" },\r\n  { name: \"Whisky\", slug: \"/result/whisky/1\" },\r\n  { name: \"Luxury Watches\", slug: \"/result/luxury-watches/1\" },\r\n  { name: \"Architecture\", slug: \"/result/architecture/1\" },\r\n  { name: \"Interior Design\", slug: \"/result/interior-design/1\" },\r\n  { name: \"Jewellery\", slug: \"/result/jewellery/1\" },\r\n  { name: \"Electric Cars\", slug: \"/result/electric-cars/1\" },\r\n  { name: \"Superbikes\", slug: \"/result/superbikes/1\" },\r\n  { name: \"Luxury Hotels\", slug: \"/result/luxury-hotels/1\" },\r\n  { name: \"Fashion\", slug: \"/result/fashion/1\" },\r\n  { name: \"Art\", slug: \"/result/art/1\" },\r\n  { name: \"Gastronomy\", slug: \"/result/gastronomy/1\" },\r\n  { name: \"Future\", slug: \"/result/future/1\" },\r\n  { name: \"Spirits\", slug: \"/result/spirits/1\" },\r\n];"], "names": [], "mappings": ";;;AAAO,MAAM,WAAW;IACtB;QAAE,MAAM;QAAe,MAAM;IAAwB;IACrD;QAAE,MAAM;QAAgB,MAAM;IAAyB;IACvD;QAAE,MAAM;QAAU,MAAM;IAAmB;IAC3C;QAAE,MAAM;QAAc,MAAM;IAAuB;IACnD;QAAE,MAAM;QAAe,MAAM;IAAwB;IACrD;QAAE,MAAM;QAAgB,MAAM;IAAyB;IACvD;QAAE,MAAM;QAAU,MAAM;IAAmB;IAC3C;QAAE,MAAM;QAAkB,MAAM;IAA2B;IAC3D;QAAE,MAAM;QAAgB,MAAM;IAAyB;IACvD;QAAE,MAAM;QAAmB,MAAM;IAA4B;IAC7D;QAAE,MAAM;QAAa,MAAM;IAAsB;IACjD;QAAE,MAAM;QAAiB,MAAM;IAA0B;IACzD;QAAE,MAAM;QAAc,MAAM;IAAuB;IACnD;QAAE,MAAM;QAAiB,MAAM;IAA0B;IACzD;QAAE,MAAM;QAAW,MAAM;IAAoB;IAC7C;QAAE,MAAM;QAAO,MAAM;IAAgB;IACrC;QAAE,MAAM;QAAc,MAAM;IAAuB;IACnD;QAAE,MAAM;QAAU,MAAM;IAAmB;IAC3C;QAAE,MAAM;QAAW,MAAM;IAAoB;CAC9C", "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1055, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/utils/Constants.jsx"], "sourcesContent": ["export const Const = {\r\n  Token: \"token\",\r\n  Session: \"Session\",\r\n  LoggedInRolePermission: \"Role\",\r\n  User: \"User\",\r\n  LoggedIn: \"LoggedIn\",\r\n  LoggedInUser: \"LoggedInUser\",\r\n  STrue: true,\r\n  SFalse: false,\r\n  Success200: 200,\r\n  Created201: 201,\r\n  Invalid400: 400,\r\n  UnAuth401: 401,\r\n  Forbidden403: 403,\r\n  NotFound404: 404,\r\n  ServerError500: 500,\r\n  BadGateway502: 502,\r\n  ServiceUnavailable503: 503,\r\n  GatewayTimeout504: 504,\r\n  Redirect302: 302,\r\n  Inactive: 0,\r\n  Active: 1,\r\n  Trash: 2,\r\n  Draft: 3,\r\n  Scheduled: 4,\r\n  Limit: 20,\r\n  Offset: 0,\r\n  Brand: \"Robb Report India\",\r\n  Link: process.env.NEXT_PUBLIC_BACKEND_URL,\r\n  ClientLink: process.env.NEXT_PUBLIC_CLIENT_URL,\r\n};\r\n\r\nexport const ProcessAPI = async (res) => {\r\n  if (res.status === Const.Success200 || res.status === Const.Created201) {\r\n    const data = await res.json();\r\n    return data;\r\n  } else if (res.status === Const.Redirect302) {\r\n  } else if (res.status === Const.Invalid400) {\r\n  } else if (res.status === Const.UnAuth401) {\r\n    localStorage.clear();\r\n    window.location.href = \"/signin\";\r\n  } else if (res.status === Const.NotFound404) {\r\n    const data = await res.json();\r\n    return data;\r\n    // return {\r\n    //   notFound: true,\r\n    // };\r\n  } else {\r\n    throw new Error(\"Some error occurred\");\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAO,MAAM,QAAQ;IACnB,OAAO;IACP,SAAS;IACT,wBAAwB;IACxB,MAAM;IACN,UAAU;IACV,cAAc;IACd,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,cAAc;IACd,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,uBAAuB;IACvB,mBAAmB;IACnB,aAAa;IACb,UAAU;IACV,QAAQ;IACR,OAAO;IACP,OAAO;IACP,WAAW;IACX,OAAO;IACP,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,UAAU;AACZ;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI,IAAI,MAAM,KAAK,MAAM,UAAU,IAAI,IAAI,MAAM,KAAK,MAAM,UAAU,EAAE;QACtE,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,OAAO;IACT,OAAO,IAAI,IAAI,MAAM,KAAK,MAAM,WAAW,EAAE,CAC7C,OAAO,IAAI,IAAI,MAAM,KAAK,MAAM,UAAU,EAAE,CAC5C,OAAO,IAAI,IAAI,MAAM,KAAK,MAAM,SAAS,EAAE;QACzC,aAAa,KAAK;QAClB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,OAAO,IAAI,IAAI,MAAM,KAAK,MAAM,WAAW,EAAE;QAC3C,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,OAAO;IACP,WAAW;IACX,oBAAoB;IACpB,KAAK;IACP,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/utils/Util.jsx"], "sourcesContent": ["import parse from \"html-react-parser\";\r\nimport { Const } from \"./Constants\";\r\n\r\nexport const htmlParser = (data) => {\r\n  return parse(data);\r\n};\r\n\r\nexport const dateFormateWithTime = (dateString) => {\r\n  const date = new Date(dateString);\r\n\r\n  const monthNames = [\r\n    \"JANUARY\",\r\n    \"FEBRUARY\",\r\n    \"MARCH\",\r\n    \"APRIL\",\r\n    \"MAY\",\r\n    \"JUN<PERSON>\",\r\n    \"JULY\",\r\n    \"AUGUST\",\r\n    \"SEPTEMBER\",\r\n    \"OCTOBER\",\r\n    \"NOVEMBER\",\r\n    \"DECEMBER\",\r\n  ];\r\n\r\n  const month = monthNames[date.getMonth()];\r\n  const day = date.getDate();\r\n  const year = date.getFullYear();\r\n\r\n  let hours = date.getHours();\r\n  const minutes = date.getMinutes();\r\n  const ampm = hours >= 12 ? \"PM\" : \"AM\";\r\n\r\n  hours = hours % 12 || 12;\r\n\r\n  const minutesStr = minutes.toString().padStart(2, \"0\");\r\n\r\n  return `${month} ${day}, ${year} ${hours}:${minutesStr}${ampm}`;\r\n};\r\n\r\nexport const formateDateShort = (dateString) => {\r\n  if (!dateString) return;\r\n  const date = new Date(dateString);\r\n  const day = String(date.getUTCDate()).padStart(2, \"0\");\r\n  const month = String(date.getUTCMonth() + 1).padStart(2, \"0\");\r\n  const year = String(date.getUTCFullYear()).slice(-2);\r\n\r\n  return `${day}.${month}.${year}`;\r\n};\r\n\r\nexport const dateFormateWithTimeShort = (dateString) => {\r\n  if (!dateString) return;\r\n  const date = new Date(dateString);\r\n\r\n  const monthNames = [\r\n    \"Jan\",\r\n    \"Feb\",\r\n    \"Mar\",\r\n    \"Apr\",\r\n    \"May\",\r\n    \"Jun\",\r\n    \"Jul\",\r\n    \"Aug\",\r\n    \"Sep\",\r\n    \"Oct\",\r\n    \"Nov\",\r\n    \"Dec\",\r\n  ];\r\n\r\n  const month = monthNames[date.getMonth()];\r\n  const day = date.getDate();\r\n  const year = date.getFullYear();\r\n\r\n  let hours = date.getHours();\r\n  const minutes = date.getMinutes();\r\n  const ampm = hours >= 12 ? \"PM\" : \"AM\";\r\n\r\n  hours = hours % 12 || 12;\r\n\r\n  const minutesStr = minutes.toString().padStart(2, \"0\");\r\n\r\n  return `${month} ${day}, ${year}`;\r\n};\r\n\r\nexport const timeFormate = (date) => {\r\n  const dateObj = new Date(date);\r\n  const options = {\r\n    month: \"long\",\r\n    day: \"2-digit\",\r\n    year: \"numeric\",\r\n    hour: \"numeric\",\r\n    minute: \"2-digit\",\r\n    hour12: true,\r\n  };\r\n  const formattedDate = new Intl.DateTimeFormat(\"en-US\", options).format(\r\n    dateObj\r\n  );\r\n  const getTime = formattedDate.split(\" at \")[1];\r\n  return getTime;\r\n};\r\n\r\nexport const formatDateAndTime = (isoString) => {\r\n  const date = new Date(isoString);\r\n  const now = new Date();\r\n\r\n  const months = [\r\n    \"Jan\",\r\n    \"Feb\",\r\n    \"Mar\",\r\n    \"Apr\",\r\n    \"May\",\r\n    \"Jun\",\r\n    \"Jul\",\r\n    \"Aug\",\r\n    \"Sep\",\r\n    \"Oct\",\r\n    \"Nov\",\r\n    \"Dec\",\r\n  ];\r\n  const formattedDate = `${\r\n    months[date.getUTCMonth()]\r\n  } ${date.getUTCDate()}, ${date.getUTCFullYear()}`;\r\n\r\n  const diffInMilliseconds = now - date;\r\n  const diffInMinutes = Math.floor(diffInMilliseconds / (1000 * 60));\r\n  const diffInHours = Math.floor(diffInMinutes / 60);\r\n  const diffInDays = Math.floor(diffInHours / 24);\r\n\r\n  if (diffInDays >= 10) {\r\n    return formattedDate;\r\n  } else {\r\n    let timeDifference;\r\n    if (diffInDays === 1) {\r\n      timeDifference = `${diffInDays} day ago`;\r\n    } else if (diffInDays > 1) {\r\n      timeDifference = `${diffInDays} days ago`;\r\n    } else if (diffInHours >= 1) {\r\n      timeDifference = `${diffInHours} hours ago`;\r\n    } else {\r\n      timeDifference = `${diffInMinutes} minutes ago`;\r\n    }\r\n    return timeDifference;\r\n  }\r\n};\r\n\r\nexport const formatDateTimeHv = (inputDateTime) => {\r\n  const inputDate = new Date(inputDateTime);\r\n  const currentDate = new Date();\r\n\r\n  const diffTime = Math.abs(currentDate - inputDate);\r\n  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\r\n\r\n  const isSameDay =\r\n    inputDate.getDate() === currentDate.getDate() &&\r\n    inputDate.getMonth() === currentDate.getMonth() &&\r\n    inputDate.getFullYear() === currentDate.getFullYear();\r\n\r\n  if (isSameDay) {\r\n    // Ensuring consistent 12-hour format (with AM/PM) across client and server\r\n    return inputDate.toLocaleTimeString(\"en-US\", {\r\n      hour: \"2-digit\",\r\n      minute: \"2-digit\",\r\n      hour12: true,\r\n    });\r\n  }\r\n\r\n  if (diffDays <= 2) {\r\n    return `${diffDays} day${diffDays > 1 ? \"s\" : \"\"} ago`;\r\n  }\r\n\r\n  // Return in \"SEP 19, 2024\" format for dates older than 2 days\r\n  const options = { year: \"numeric\", month: \"short\", day: \"numeric\" };\r\n  return inputDate.toLocaleDateString(\"en-US\", options);\r\n};\r\n\r\nexport const checkPermission = (value, viewIndex) => {\r\n  if (value && typeof value == \"number\" && value > 0) {\r\n    const permissions = Number(value)\r\n      .toString(2)\r\n      .split(\"\")\r\n      .reverse()\r\n      .map((item) => item === \"1\");\r\n    Object.keys(viewIndex).forEach(function (key, value) {\r\n      if (permissions.length > value) {\r\n        viewIndex[key] = permissions[value];\r\n      } else {\r\n        viewIndex[key] = false;\r\n      }\r\n    });\r\n    return viewIndex;\r\n  } else {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const binaryToNumber = (value) => {\r\n  if (value) {\r\n    const binaryToNumber = parseInt(value, 2);\r\n    return binaryToNumber;\r\n  } else {\r\n    return 0;\r\n  }\r\n};\r\n\r\nexport const permissionCount = (value) => {\r\n  if (value && typeof value == \"number\" && value > 0) {\r\n    const permissions = Number(value).toString(2).split(\"\");\r\n    const total = permissions.length;\r\n    const count = permissions.filter((item) => item === \"1\").length;\r\n    return { count, total };\r\n  }\r\n  return { count: 0, total: 0 };\r\n};\r\n\r\nexport const isValidColor = (input) => {\r\n  try {\r\n    const namedColors = [\r\n      \"black\",\r\n      \"silver\",\r\n      \"gray\",\r\n      \"white\",\r\n      \"maroon\",\r\n      \"red\",\r\n      \"purple\",\r\n      \"fuchsia\",\r\n      \"green\",\r\n      \"lime\",\r\n      \"olive\",\r\n      \"yellow\",\r\n      \"navy\",\r\n      \"blue\",\r\n      \"teal\",\r\n      \"aqua\",\r\n      // Add more color names here\r\n    ];\r\n\r\n    // Case-insensitive match against the list of named colors\r\n    const colorRegex = new RegExp(`^(${namedColors.join(\"|\")})$`, \"i\");\r\n    // let regex = new RegExp(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/);\r\n    let regex = new RegExp(\r\n      /^(#?([a-f\\d]{3,4}|[a-f\\d]{6}|[a-f\\d]{8})|rgb\\((0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d)\\)|rgba\\((0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|0?\\.\\d|1(\\.0)?)\\)|hsl\\((0|360|35\\d|3[0-4]\\d|[12]\\d\\d|0?\\d?\\d),(0|100|\\d{1,2})%,(0|100|\\d{1,2})%\\)|hsla\\((0|360|35\\d|3[0-4]\\d|[12]\\d\\d|0?\\d?\\d),(0|100|\\d{1,2})%,(0|100|\\d{1,2})%,(0?\\.\\d|1(\\.0)?)\\))$/\r\n    );\r\n    return regex.test(input) || colorRegex.test(input);\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const ga4FormatDate = (dateString) => {\r\n  // Extract year, month, and day from the string\r\n  const dateStringParam = dateString.split(\"-\");\r\n  const year = dateStringParam[0];\r\n  const month = parseInt(dateStringParam[1]);\r\n  const day = dateStringParam[2];\r\n\r\n  // Define an array for month names\r\n  const monthNames = [\r\n    \"January\",\r\n    \"February\",\r\n    \"March\",\r\n    \"April\",\r\n    \"May\",\r\n    \"June\",\r\n    \"July\",\r\n    \"August\",\r\n    \"September\",\r\n    \"October\",\r\n    \"November\",\r\n    \"December\",\r\n  ];\r\n\r\n  const date = new Date(year, month - 1, day);\r\n  const monthName = monthNames[date.getMonth()];\r\n  const formattedDay = day.padStart(2, \"0\");\r\n  return monthName + \" \" + formattedDay;\r\n};\r\n\r\nexport const generateSlug = (title) => {\r\n  let slug = title\r\n    .toString() // Convert to string\r\n    .toLowerCase() // Convert to lowercase\r\n    .trim() // Trim leading/trailing whitespace\r\n    .replace(/\\s+/g, \"-\") // Replace spaces with hyphens\r\n    .replace(/[^\\w-]+/g, \"\") // Remove all non-word characters\r\n    .replace(/--+/g, \"-\"); // Replace multiple hyphens with a single hyphen\r\n  // Ensure the slug starts with a slash\r\n  if (!slug.startsWith(\"/\")) {\r\n    slug = `/${slug}`;\r\n  }\r\n\r\n  return slug;\r\n};\r\n\r\nexport const statusLabel = (value) => {\r\n  let label = \"\";\r\n  if (value === Const.Inactive) {\r\n    label = \"Unpubilled\";\r\n  } else if (value === Const.Active) {\r\n    label = \"Published\";\r\n  } else if (value === Const.Trash) {\r\n    label = \"Trash\";\r\n  } else if (value === Const.Draft) {\r\n    label = \"Draft\";\r\n  } else if (value === Const.Scheduled) {\r\n    label = \"Scheduled\";\r\n  }\r\n  return label;\r\n};\r\n\r\nexport const hasHtmlTags = (str) => {\r\n  const regex = /<\\/?[a-z][\\s\\S]*>/i;\r\n  return regex.test(str);\r\n};\r\n\r\nexport const getEmbedType = (url) => {\r\n  const youtubeRegex =\r\n    /(?:https?:\\/\\/)?(?:www\\.)?(?:youtube\\.com\\/(?:[^\\/\\n\\s]+\\/\\S+\\/|(?:v|e(?:mbed)?)\\/|\\S*?[?&]v=)|youtu\\.be\\/)([a-zA-Z0-9_-]{11})/;\r\n\r\n  const instagramRegex =\r\n    /(?:https?:\\/\\/)?(?:www\\.)?instagram\\.com\\/(?:p|tv|reel)\\/([A-Za-z0-9_-]+)/;\r\n\r\n  const twitterRegex =\r\n    /(?:https?:\\/\\/)?(?:(?:www\\.|platform\\.)?(?:twitter|x)\\.com\\/(?:(?:\\w+\\/status\\/[0-9]+)|(?:embed\\/Tweet\\.html\\?id=[0-9]+)))/;\r\n\r\n  const facebookPostOrVideoRegex =\r\n    /(?:https?:\\/\\/)?(?:www\\.)?facebook\\.com\\/(?:[^\\/\\n\\s]+\\/posts\\/|(?:video\\.php\\?v=|watch\\/))([0-9]+)/;\r\n\r\n  if (youtubeRegex.test(url)) {\r\n    return \"youtube\";\r\n  } else if (instagramRegex.test(url)) {\r\n    return \"instagram\";\r\n  } else if (twitterRegex.test(url)) {\r\n    return \"twitter\";\r\n  } else if (facebookPostOrVideoRegex.test(url)) {\r\n    return \"facebook\";\r\n  }\r\n};\r\n\r\nexport const extractTwitterId = (embedUrl) => {\r\n  const match = embedUrl.split(\"id=\")[1];\r\n  return match;\r\n};\r\n\r\nexport const getTwitterUrl = (embedUrl) => {\r\n  const tweetId = new URL(embedUrl).searchParams.get(\"id\");\r\n  const tweetUrl = `https://twitter.com/i/web/status/${tweetId}`;\r\n  return tweetUrl;\r\n};\r\n\r\nexport const getAuthorText = (prefix = \"By\", author = [], contributor = []) => {\r\n  const list = author.length ? author : contributor;\r\n  const name = list[0]?.name || list[0] || \"\";\r\n  const count = list.length - 1;\r\n\r\n  return name ? `${prefix} ${name}${count ? ` +${count} More` : \"\"}` : \"\";\r\n};\r\n\r\nexport const extractTextFromDoc = (doc) => {\r\n  const output = [];\r\n\r\n  function extractTextFromContent(contentArray) {\r\n    return (\r\n      contentArray\r\n        ?.map((node) => {\r\n          if (node.type === \"text\") return node.text || \"\";\r\n          if (node.content) return extractTextFromContent(node.content);\r\n          return \"\";\r\n        })\r\n        .join(\"\") || \"\"\r\n    );\r\n  }\r\n\r\n  if (Array.isArray(doc.content)) {\r\n    for (const node of doc.content) {\r\n      if (node.type === \"paragraph\" || node.type === \"heading\") {\r\n        const text = extractTextFromContent(node.content || []);\r\n        if (text.trim()) output.push(text.trim());\r\n      }\r\n    }\r\n  }\r\n\r\n  return output.join(\" \");\r\n};\r\n\r\nexport const convertToISTISOString = (utcISOString) => {\r\n  if (!utcISOString) return \"\";\r\n  const date = new Date(utcISOString);\r\n\r\n  const istOffsetMs = 5.5 * 60 * 60 * 1000;\r\n  const istDate = new Date(date.getTime() + istOffsetMs);\r\n\r\n  return istDate.toISOString().replace(\"Z\", \"+05:30\");\r\n};\r\n\r\nexport const escapeXml = (unsafe) => {\r\n  return unsafe\r\n    .replace(/&/g, \"&amp;\")\r\n    .replace(/</g, \"&lt;\")\r\n    .replace(/>/g, \"&gt;\")\r\n    .replace(/\"/g, \"&quot;\")\r\n    .replace(/'/g, \"&apos;\");\r\n};\r\n\r\nexport const convertSlugOrTitle = (input = \"\", toTitle = true) => {\r\n  if (!input) return \"\"\r\n  const trimmedText = input.trim();\r\n  if (toTitle) {\r\n    return trimmedText.split(\"-\").join(\" \");\r\n  } else {\r\n    return trimmedText.toLowerCase().split(\" \").filter(Boolean).join(\"-\");\r\n  }\r\n};\r\n\r\nexport const capitalizeFirstLetter = (str) => {\r\n  if (!str) return '';\r\n  return str.replace(/\\b\\w/g, char => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;;;;;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,8IAAA,CAAA,UAAK,AAAD,EAAE;AACf;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ,UAAU,CAAC,KAAK,QAAQ,GAAG;IACzC,MAAM,MAAM,KAAK,OAAO;IACxB,MAAM,OAAO,KAAK,WAAW;IAE7B,IAAI,QAAQ,KAAK,QAAQ;IACzB,MAAM,UAAU,KAAK,UAAU;IAC/B,MAAM,OAAO,SAAS,KAAK,OAAO;IAElC,QAAQ,QAAQ,MAAM;IAEtB,MAAM,aAAa,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAElD,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,aAAa,MAAM;AACjE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,YAAY;IACjB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG;IAClD,MAAM,QAAQ,OAAO,KAAK,WAAW,KAAK,GAAG,QAAQ,CAAC,GAAG;IACzD,MAAM,OAAO,OAAO,KAAK,cAAc,IAAI,KAAK,CAAC,CAAC;IAElD,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AAClC;AAEO,MAAM,2BAA2B,CAAC;IACvC,IAAI,CAAC,YAAY;IACjB,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ,UAAU,CAAC,KAAK,QAAQ,GAAG;IACzC,MAAM,MAAM,KAAK,OAAO;IACxB,MAAM,OAAO,KAAK,WAAW;IAE7B,IAAI,QAAQ,KAAK,QAAQ;IACzB,MAAM,UAAU,KAAK,UAAU;IAC/B,MAAM,OAAO,SAAS,KAAK,OAAO;IAElC,QAAQ,QAAQ,MAAM;IAEtB,MAAM,aAAa,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAElD,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;AACnC;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,UAAU,IAAI,KAAK;IACzB,MAAM,UAAU;QACd,OAAO;QACP,KAAK;QACL,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA,MAAM,gBAAgB,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CACpE;IAEF,MAAM,UAAU,cAAc,KAAK,CAAC,OAAO,CAAC,EAAE;IAC9C,OAAO;AACT;AAEO,MAAM,oBAAoB,CAAC;IAChC,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAEhB,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,gBAAgB,GACpB,MAAM,CAAC,KAAK,WAAW,GAAG,CAC3B,CAAC,EAAE,KAAK,UAAU,GAAG,EAAE,EAAE,KAAK,cAAc,IAAI;IAEjD,MAAM,qBAAqB,MAAM;IACjC,MAAM,gBAAgB,KAAK,KAAK,CAAC,qBAAqB,CAAC,OAAO,EAAE;IAChE,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAE5C,IAAI,cAAc,IAAI;QACpB,OAAO;IACT,OAAO;QACL,IAAI;QACJ,IAAI,eAAe,GAAG;YACpB,iBAAiB,GAAG,WAAW,QAAQ,CAAC;QAC1C,OAAO,IAAI,aAAa,GAAG;YACzB,iBAAiB,GAAG,WAAW,SAAS,CAAC;QAC3C,OAAO,IAAI,eAAe,GAAG;YAC3B,iBAAiB,GAAG,YAAY,UAAU,CAAC;QAC7C,OAAO;YACL,iBAAiB,GAAG,cAAc,YAAY,CAAC;QACjD;QACA,OAAO;IACT;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,cAAc,IAAI;IAExB,MAAM,WAAW,KAAK,GAAG,CAAC,cAAc;IACxC,MAAM,WAAW,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE3D,MAAM,YACJ,UAAU,OAAO,OAAO,YAAY,OAAO,MAC3C,UAAU,QAAQ,OAAO,YAAY,QAAQ,MAC7C,UAAU,WAAW,OAAO,YAAY,WAAW;IAErD,IAAI,WAAW;QACb,2EAA2E;QAC3E,OAAO,UAAU,kBAAkB,CAAC,SAAS;YAC3C,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,IAAI,YAAY,GAAG;QACjB,OAAO,GAAG,SAAS,IAAI,EAAE,WAAW,IAAI,MAAM,GAAG,IAAI,CAAC;IACxD;IAEA,8DAA8D;IAC9D,MAAM,UAAU;QAAE,MAAM;QAAW,OAAO;QAAS,KAAK;IAAU;IAClE,OAAO,UAAU,kBAAkB,CAAC,SAAS;AAC/C;AAEO,MAAM,kBAAkB,CAAC,OAAO;IACrC,IAAI,SAAS,OAAO,SAAS,YAAY,QAAQ,GAAG;QAClD,MAAM,cAAc,OAAO,OACxB,QAAQ,CAAC,GACT,KAAK,CAAC,IACN,OAAO,GACP,GAAG,CAAC,CAAC,OAAS,SAAS;QAC1B,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,SAAU,GAAG,EAAE,KAAK;YACjD,IAAI,YAAY,MAAM,GAAG,OAAO;gBAC9B,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC,MAAM;YACrC,OAAO;gBACL,SAAS,CAAC,IAAI,GAAG;YACnB;QACF;QACA,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,OAAO;QACT,MAAM,iBAAiB,SAAS,OAAO;QACvC,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,SAAS,OAAO,SAAS,YAAY,QAAQ,GAAG;QAClD,MAAM,cAAc,OAAO,OAAO,QAAQ,CAAC,GAAG,KAAK,CAAC;QACpD,MAAM,QAAQ,YAAY,MAAM;QAChC,MAAM,QAAQ,YAAY,MAAM,CAAC,CAAC,OAAS,SAAS,KAAK,MAAM;QAC/D,OAAO;YAAE;YAAO;QAAM;IACxB;IACA,OAAO;QAAE,OAAO;QAAG,OAAO;IAAE;AAC9B;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAI;QACF,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SAED;QAED,0DAA0D;QAC1D,MAAM,aAAa,IAAI,OAAO,CAAC,EAAE,EAAE,YAAY,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE;QAC9D,gEAAgE;QAChE,IAAI,QAAQ,IAAI,OACd;QAEF,OAAO,MAAM,IAAI,CAAC,UAAU,WAAW,IAAI,CAAC;IAC9C,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,+CAA+C;IAC/C,MAAM,kBAAkB,WAAW,KAAK,CAAC;IACzC,MAAM,OAAO,eAAe,CAAC,EAAE;IAC/B,MAAM,QAAQ,SAAS,eAAe,CAAC,EAAE;IACzC,MAAM,MAAM,eAAe,CAAC,EAAE;IAE9B,kCAAkC;IAClC,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,OAAO,IAAI,KAAK,MAAM,QAAQ,GAAG;IACvC,MAAM,YAAY,UAAU,CAAC,KAAK,QAAQ,GAAG;IAC7C,MAAM,eAAe,IAAI,QAAQ,CAAC,GAAG;IACrC,OAAO,YAAY,MAAM;AAC3B;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAI,OAAO,MACR,QAAQ,GAAG,oBAAoB;KAC/B,WAAW,GAAG,uBAAuB;KACrC,IAAI,GAAG,mCAAmC;KAC1C,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,iCAAiC;KACzD,OAAO,CAAC,QAAQ,MAAM,gDAAgD;IACzE,sCAAsC;IACtC,IAAI,CAAC,KAAK,UAAU,CAAC,MAAM;QACzB,OAAO,CAAC,CAAC,EAAE,MAAM;IACnB;IAEA,OAAO;AACT;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,QAAQ;IACZ,IAAI,UAAU,mHAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;QAC5B,QAAQ;IACV,OAAO,IAAI,UAAU,mHAAA,CAAA,QAAK,CAAC,MAAM,EAAE;QACjC,QAAQ;IACV,OAAO,IAAI,UAAU,mHAAA,CAAA,QAAK,CAAC,KAAK,EAAE;QAChC,QAAQ;IACV,OAAO,IAAI,UAAU,mHAAA,CAAA,QAAK,CAAC,KAAK,EAAE;QAChC,QAAQ;IACV,OAAO,IAAI,UAAU,mHAAA,CAAA,QAAK,CAAC,SAAS,EAAE;QACpC,QAAQ;IACV;IACA,OAAO;AACT;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC;AACpB;AAEO,MAAM,eAAe,CAAC;IAC3B,MAAM,eACJ;IAEF,MAAM,iBACJ;IAEF,MAAM,eACJ;IAEF,MAAM,2BACJ;IAEF,IAAI,aAAa,IAAI,CAAC,MAAM;QAC1B,OAAO;IACT,OAAO,IAAI,eAAe,IAAI,CAAC,MAAM;QACnC,OAAO;IACT,OAAO,IAAI,aAAa,IAAI,CAAC,MAAM;QACjC,OAAO;IACT,OAAO,IAAI,yBAAyB,IAAI,CAAC,MAAM;QAC7C,OAAO;IACT;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,QAAQ,SAAS,KAAK,CAAC,MAAM,CAAC,EAAE;IACtC,OAAO;AACT;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,UAAU,IAAI,IAAI,UAAU,YAAY,CAAC,GAAG,CAAC;IACnD,MAAM,WAAW,CAAC,iCAAiC,EAAE,SAAS;IAC9D,OAAO;AACT;AAEO,MAAM,gBAAgB,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,EAAE,cAAc,EAAE;IACxE,MAAM,OAAO,OAAO,MAAM,GAAG,SAAS;IACtC,MAAM,OAAO,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI;IACzC,MAAM,QAAQ,KAAK,MAAM,GAAG;IAE5B,OAAO,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG;AACvE;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,SAAS,EAAE;IAEjB,SAAS,uBAAuB,YAAY;QAC1C,OACE,cACI,IAAI,CAAC;YACL,IAAI,KAAK,IAAI,KAAK,QAAQ,OAAO,KAAK,IAAI,IAAI;YAC9C,IAAI,KAAK,OAAO,EAAE,OAAO,uBAAuB,KAAK,OAAO;YAC5D,OAAO;QACT,GACC,KAAK,OAAO;IAEnB;IAEA,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO,GAAG;QAC9B,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAE;YAC9B,IAAI,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,WAAW;gBACxD,MAAM,OAAO,uBAAuB,KAAK,OAAO,IAAI,EAAE;gBACtD,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,IAAI;YACxC;QACF;IACF;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,cAAc,OAAO;IAC1B,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAM,cAAc,MAAM,KAAK,KAAK;IACpC,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO,KAAK;IAE1C,OAAO,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;AAC5C;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,OACJ,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM;AACnB;AAEO,MAAM,qBAAqB,CAAC,QAAQ,EAAE,EAAE,UAAU,IAAI;IAC3D,IAAI,CAAC,OAAO,OAAO;IACnB,MAAM,cAAc,MAAM,IAAI;IAC9B,IAAI,SAAS;QACX,OAAO,YAAY,KAAK,CAAC,KAAK,IAAI,CAAC;IACrC,OAAO;QACL,OAAO,YAAY,WAAW,GAAG,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS,IAAI,CAAC;IACnE;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IAAI,OAAO,CAAC,SAAS,CAAA,OAAQ,KAAK,WAAW;AACtD", "debugId": null}}, {"offset": {"line": 1485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1492, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/SearchDrawer.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useMemo } from \"react\";\r\nimport { IoIosArrowDown, IoIosClose } from \"react-icons/io\";\r\nimport { gsap } from \"gsap\";\r\nimport Button from \"../buttons/Button\";\r\nimport { CiSearch } from \"react-icons/ci\";\r\nimport { BoxBorderBtn } from \"../buttons/BoxBorderBtn\";\r\nimport { useRouter } from \"next/router\";\r\nimport { chipData } from \"@/helpers/ChipData\";\r\nimport { convertSlugOrTitle } from \"@/utils/Util\";\r\n\r\nconst defaultFilter = {\r\n  sortBy: -1,\r\n  dateRange: 0,\r\n  subcategoryIds: [],\r\n  tagIds: [],\r\n  writerIds: [],\r\n};\r\n\r\nconst SearchDrawer = ({\r\n  visible,\r\n  filters = {},\r\n  selectedFilter,\r\n  setVisible,\r\n  setSelectedFilter,\r\n}) => {\r\n  const drawerRef = useRef(null);\r\n  const [openAccordions, setOpenAccordions] = useState({});\r\n  const [currentFilter, setCurrentFilter] = useState({});\r\n\r\n  useEffect(() => {\r\n    document.body.classList.toggle(\"overflow-hidden\", visible);\r\n    setCurrentFilter(selectedFilter);\r\n    return () => document.body.classList.remove(\"overflow-hidden\");\r\n  }, [visible]);\r\n\r\n  const toggleAccordion = (key) =>\r\n    setOpenAccordions({ [key]: !openAccordions[key] });\r\n\r\n  const handleClose = () => setVisible(false);\r\n\r\n  const handleFilter = (value, key, isRadio = false) => {\r\n    setCurrentFilter((prev) => {\r\n      let updated = { ...prev };\r\n\r\n      if (isRadio) {\r\n        if (updated[key] === value) return updated;\r\n        updated[key] = value;\r\n      } else {\r\n        const values = new Set(updated[key] || []);\r\n        values.has(value) ? values.delete(value) : values.add(value);\r\n        updated[key] = Array.from(values);\r\n      }\r\n\r\n      return updated;\r\n    });\r\n  };\r\n\r\n  const handleClearFilter = (key) => {\r\n    const clearedValue = { [key]: defaultFilter[key] };\r\n    setCurrentFilter((prev) => ({ ...prev, ...clearedValue }));\r\n  };\r\n\r\n  const handleSaveFilter = () => {\r\n    setSelectedFilter(currentFilter);\r\n    handleClose();\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    if (!drawerRef.current) return;\r\n\r\n    const container = drawerRef.current.querySelector(\"#list_container\");\r\n    const duration = 0.5;\r\n\r\n    if (visible) {\r\n      gsap.set(drawerRef.current, { pointerEvents: \"auto\" });\r\n      gsap\r\n        .timeline()\r\n        .to(drawerRef.current, {\r\n          opacity: 1,\r\n          duration,\r\n          ease: \"power2.out\",\r\n        })\r\n        .to(container, {\r\n          right: \"0%\",\r\n          duration,\r\n          ease: \"power2.out\",\r\n          delay: -0.3,\r\n        });\r\n    } else {\r\n      gsap\r\n        .timeline()\r\n        .to(container, {\r\n          right: \"-100%\",\r\n          duration: duration + 0.2,\r\n          ease: \"power2.inOut\",\r\n        })\r\n        .to(drawerRef.current, {\r\n          opacity: 0,\r\n          duration,\r\n          ease: \"power2.inOut\",\r\n          delay: -0.3,\r\n          onComplete: () =>\r\n            gsap.set(drawerRef.current, { pointerEvents: \"none\" }),\r\n        });\r\n    }\r\n  }, [visible]);\r\n\r\n  const [text, setText] = useState(\"\");\r\n  const router = useRouter()\r\n\r\nconst handleSearch = () => {\r\n  const queryText = convertSlugOrTitle(text, false);\r\n  setVisible(false);\r\n  router.push(`/result/${queryText}/1`);\r\n  setText(\"\");\r\n};\r\n\r\n  const inputSearchRef = useRef()\r\n\r\n  useEffect(() => {\r\n  if (inputSearchRef.current) {\r\n    if (visible) {\r\n      inputSearchRef.current.focus();\r\n    } else {\r\n      inputSearchRef.current.blur();\r\n    }\r\n  }\r\n}, [visible]);\r\n\r\n  return (\r\n    <div\r\n      id=\"drawer_container\"\r\n      className=\"Search_drawer\"\r\n      ref={drawerRef}\r\n      onClick={handleClose}\r\n    >\r\n      <div id=\"list_container\" onClick={(e) => e.stopPropagation()}>\r\n        <div className=\"search_drawer filter_title_row\">\r\n          <span className=\"close_icon\" onClick={handleClose}>\r\n            <IoIosClose />\r\n          </span>\r\n        </div>\r\n        <div className=\"drawer_body drawer_body_search\" data-lenis-prevent>\r\n          <form\r\n            onSubmit={(e) => {\r\n              e.preventDefault();\r\n              handleSearch();\r\n            }}\r\n            id=\"search_drawer\"\r\n          >\r\n            <input\r\n            ref={inputSearchRef}\r\n              type=\"text\"\r\n              name=\"query\"\r\n              placeholder=\"Search\"\r\n              value={text || \"\"}\r\n              onChange={(e) => setText(e.target.value)}\r\n            />\r\n            <CiSearch onClick={()=> handleSearch()} />\r\n          </form>\r\n          {chipData.length > 0 && (\r\n            <div className=\"chipContainer search_chipContainer\">\r\n              {chipData?.map((data, index) => (\r\n                <BoxBorderBtn key={`Search-${index}`} isSearchDrawer={true} handleClose={handleClose} href={data?.slug || \"#\"}>\r\n                  {data?.name || \"\"}\r\n                </BoxBorderBtn>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SearchDrawer;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEA,MAAM,gBAAgB;IACpB,QAAQ,CAAC;IACT,WAAW;IACX,gBAAgB,EAAE;IAClB,QAAQ,EAAE;IACV,WAAW,EAAE;AACf;AAEA,MAAM,eAAe,CAAC,EACpB,OAAO,EACP,UAAU,CAAC,CAAC,EACZ,cAAc,EACd,UAAU,EACV,iBAAiB,EAClB;IACC,MAAM,YAAY,CAAA,GAAA,mGAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAEpD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB;QAClD,iBAAiB;QACjB,OAAO,IAAM,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC9C,GAAG;QAAC;KAAQ;IAEZ,MAAM,kBAAkB,CAAC,MACvB,kBAAkB;YAAE,CAAC,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI;QAAC;IAElD,MAAM,cAAc,IAAM,WAAW;IAErC,MAAM,eAAe,CAAC,OAAO,KAAK,UAAU,KAAK;QAC/C,iBAAiB,CAAC;YAChB,IAAI,UAAU;gBAAE,GAAG,IAAI;YAAC;YAExB,IAAI,SAAS;gBACX,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,OAAO;gBACnC,OAAO,CAAC,IAAI,GAAG;YACjB,OAAO;gBACL,MAAM,SAAS,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,EAAE;gBACzC,OAAO,GAAG,CAAC,SAAS,OAAO,MAAM,CAAC,SAAS,OAAO,GAAG,CAAC;gBACtD,OAAO,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC;YAC5B;YAEA,OAAO;QACT;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,eAAe;YAAE,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI;QAAC;QACjD,iBAAiB,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,YAAY;YAAC,CAAC;IAC1D;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;QAClB;IACF;IAGA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,YAAY,UAAU,OAAO,CAAC,aAAa,CAAC;QAClD,MAAM,WAAW;QAEjB,IAAI,SAAS;YACX,iGAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE;gBAAE,eAAe;YAAO;YACpD,iGAAA,CAAA,OAAI,CACD,QAAQ,GACR,EAAE,CAAC,UAAU,OAAO,EAAE;gBACrB,SAAS;gBACT;gBACA,MAAM;YACR,GACC,EAAE,CAAC,WAAW;gBACb,OAAO;gBACP;gBACA,MAAM;gBACN,OAAO,CAAC;YACV;QACJ,OAAO;YACL,iGAAA,CAAA,OAAI,CACD,QAAQ,GACR,EAAE,CAAC,WAAW;gBACb,OAAO;gBACP,UAAU,WAAW;gBACrB,MAAM;YACR,GACC,EAAE,CAAC,UAAU,OAAO,EAAE;gBACrB,SAAS;gBACT;gBACA,MAAM;gBACN,OAAO,CAAC;gBACR,YAAY,IACV,iGAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE;wBAAE,eAAe;oBAAO;YACxD;QACJ;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEzB,MAAM,eAAe;QACnB,MAAM,YAAY,CAAA,GAAA,8GAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;QAC3C,WAAW;QACX,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC;QACpC,QAAQ;IACV;IAEE,MAAM,iBAAiB,CAAA,GAAA,mGAAA,CAAA,SAAM,AAAD;IAE5B,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACV,IAAI,eAAe,OAAO,EAAE;YAC1B,IAAI,SAAS;gBACX,eAAe,OAAO,CAAC,KAAK;YAC9B,OAAO;gBACL,eAAe,OAAO,CAAC,IAAI;YAC7B;QACF;IACF,GAAG;QAAC;KAAQ;IAEV,qBACE,qKAAC;QACC,IAAG;QACH,WAAU;QACV,KAAK;QACL,SAAS;kBAET,cAAA,qKAAC;YAAI,IAAG;YAAiB,SAAS,CAAC,IAAM,EAAE,eAAe;;8BACxD,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAK,WAAU;wBAAa,SAAS;kCACpC,cAAA,qKAAC,uIAAA,CAAA,aAAU;;;;;;;;;;;;;;;8BAGf,qKAAC;oBAAI,WAAU;oBAAiC,oBAAkB;;sCAChE,qKAAC;4BACC,UAAU,CAAC;gCACT,EAAE,cAAc;gCAChB;4BACF;4BACA,IAAG;;8CAEH,qKAAC;oCACD,KAAK;oCACH,MAAK;oCACL,MAAK;oCACL,aAAY;oCACZ,OAAO,QAAQ;oCACf,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;8CAEzC,qKAAC,uIAAA,CAAA,WAAQ;oCAAC,SAAS,IAAK;;;;;;;;;;;;wBAEzB,oHAAA,CAAA,WAAQ,CAAC,MAAM,GAAG,mBACjB,qKAAC;4BAAI,WAAU;sCACZ,oHAAA,CAAA,WAAQ,EAAE,IAAI,CAAC,MAAM,sBACpB,qKAAC,sIAAA,CAAA,eAAY;oCAAyB,gBAAgB;oCAAM,aAAa;oCAAa,MAAM,MAAM,QAAQ;8CACvG,MAAM,QAAQ;mCADE,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpD;uCAEe", "debugId": null}}, {"offset": {"line": 1727, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1734, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/Header.jsx"], "sourcesContent": ["import { menus } from \"@/helpers/MenuData\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Menu from \"./Menu\";\r\nimport { RxHamburgerMenu } from \"react-icons/rx\";\r\nimport { IoSunnyOutline } from \"react-icons/io5\";\r\nimport { IoMoonOutline } from \"react-icons/io5\";\r\nimport { useTheme } from \"next-themes\";\r\nimport TransitionLoader from \"./TransitionLoader\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport GetTheme from \"@/utils/GetTheme\";\r\nimport SearchButton from \"./SearchButton\";\r\nimport SearchDrawer from \"./SearchDrawer\";\r\n\r\nconst Header = () => {\r\n  const pathname = usePathname();\r\n  const [openMenu, setOpenMenu] = useState(false);\r\n  const [showFilter, setShowFilter] = useState(false);\r\n  const { theme, setTheme } = GetTheme();\r\n  const [isHydrated, setIsHydrated] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsHydrated(true); // Ensures that we only render after the first render on the client\r\n  }, []);\r\n\r\n  // Render nothing until hydration is complete to prevent mismatch\r\n  if (!isHydrated) return null;\r\n  return (\r\n    <>\r\n      <header className=\"nav _bgCntr\">\r\n        <div className=\"nav-content\">\r\n          <Link href=\"/\" className=\"brand\">\r\n            <Image\r\n              src={\"/assets/images/logo/RR final logo.png\"}\r\n              width={1000}\r\n              height={1000}\r\n              alt=\"Robb Report India\"\r\n            />\r\n          </Link>\r\n          <div className=\"links nav-items\">\r\n            <ul className=\"menu-linksul\">\r\n              {menus.map((item, index) => {\r\n                return (\r\n                  <li key={`nav-item-${index}`} className=\"menu-item\">\r\n                    <Link\r\n                      className={\r\n                        pathname.startsWith(item.link) ? \"nav_active\" : \"\"\r\n                      }\r\n                      href={item?.link || \"#\"}\r\n                    >\r\n                      {item.name}\r\n                    </Link>\r\n                  </li>\r\n                );\r\n              })}\r\n            </ul>\r\n          </div>\r\n\r\n          {/* <div className=\"login\">\r\n            <span>Login</span>\r\n          </div> */}\r\n          {/* <div className=\"controller-row\">\r\n            <label className=\"switch\">\r\n              <input id=\"toggler\" type=\"checkbox\" />\r\n              <span className=\"slider round\"></span>\r\n            </label>\r\n          </div> */}\r\n          <SearchButton setVisible={setShowFilter} />\r\n         \r\n          <div\r\n            className=\"material-icons menu\"\r\n            onClick={() => {\r\n              setOpenMenu(!openMenu);\r\n            }}\r\n          >\r\n            <RxHamburgerMenu />\r\n          </div>\r\n        </div>\r\n         <SearchDrawer\r\n        visible={showFilter}\r\n        setVisible={setShowFilter}\r\n        />\r\n      </header>\r\n      <Menu openMenu={openMenu} setOpenMenu={setOpenMenu} />\r\n     \r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,SAAS;IACb,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,UAAQ,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,OAAO,mEAAmE;IAC1F,GAAG,EAAE;IAEL,iEAAiE;IACjE,IAAI,CAAC,YAAY,OAAO;IACxB,qBACE;;0BACE,qKAAC;gBAAO,WAAU;;kCAChB,qKAAC;wBAAI,WAAU;;0CACb,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,qKAAC,sHAAA,CAAA,UAAK;oCACJ,KAAK;oCACL,OAAO;oCACP,QAAQ;oCACR,KAAI;;;;;;;;;;;0CAGR,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAG,WAAU;8CACX,oHAAA,CAAA,QAAK,CAAC,GAAG,CAAC,CAAC,MAAM;wCAChB,qBACE,qKAAC;4CAA6B,WAAU;sDACtC,cAAA,qKAAC,qHAAA,CAAA,UAAI;gDACH,WACE,SAAS,UAAU,CAAC,KAAK,IAAI,IAAI,eAAe;gDAElD,MAAM,MAAM,QAAQ;0DAEnB,KAAK,IAAI;;;;;;2CAPL,CAAC,SAAS,EAAE,OAAO;;;;;oCAWhC;;;;;;;;;;;0CAaJ,qKAAC,qIAAA,CAAA,UAAY;gCAAC,YAAY;;;;;;0CAE1B,qKAAC;gCACC,WAAU;gCACV,SAAS;oCACP,YAAY,CAAC;gCACf;0CAEA,cAAA,qKAAC,uIAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;kCAGnB,qKAAC,qIAAA,CAAA,UAAY;wBACd,SAAS;wBACT,YAAY;;;;;;;;;;;;0BAGd,qKAAC,6HAAA,CAAA,UAAI;gBAAC,UAAU;gBAAU,aAAa;;;;;;;;AAI7C;uCAEe", "debugId": null}}, {"offset": {"line": 1895, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1902, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/Footer.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <>\r\n      <div className=\"footer\">\r\n        <div className=\"containerWrapper\">\r\n          <div className=\"wrapper_footer\">\r\n            {/* <div className=\"dest_news\">\r\n              <div className=\"dest_ft\">\r\n                <div className=\"dt_nw_wrapper\">\r\n                  <div className=\"btn_wrapper expobtn\">\r\n                    <Link href=\"\" className=\"footer_button w-inline-block\">\r\n                      Magazine Subscription\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"news_ft\">\r\n                <div className=\"dt_nw_wrapper\">\r\n                  <div className=\"btn_wrapper subbtn\">\r\n                    <div className=\"\">\r\n                      <div className=\"form-block w-form\">\r\n                        <form\r\n                          id=\"wf-form-Subscribe-Form\"\r\n                          name=\"wf-form-Subscribe-Form\"\r\n                          data-name=\"Subscribe Form\"\r\n                          method=\"get\"\r\n                          className=\"subscribe_flex\"\r\n                          data-wf-page-id=\"6713971095c4a7cab5d924fd\"\r\n                          data-wf-element-id=\"ba614d1a-6499-4c2c-764e-b376071a5795\"\r\n                          aria-label=\"Subscribe Form\"\r\n                        >\r\n                          <input\r\n                            className=\"field_prefooter w-input\"\r\n                            maxLength=\"256\"\r\n                            name=\"email-2\"\r\n                            data-name=\"Email 2\"\r\n                            placeholder=\"Enter your e-mail address\"\r\n                            type=\"email\"\r\n                            id=\"email-2\"\r\n                            required=\"\"\r\n                          />\r\n                          <input\r\n                            type=\"submit\"\r\n                            data-wait=\"Please wait...\"\r\n                            className=\"footer_button w-button\"\r\n                            value=\"Subscribe\"\r\n                          />\r\n                        </form>\r\n                        <div\r\n                          className=\"subscribe_thanks w-form-done\"\r\n                          role=\"region\"\r\n                          aria-label=\"Subscribe Form success\"\r\n                        >\r\n                          <div className=\"thanks_txt\">\r\n                            Thanks for subscribing! You'll be the first to know\r\n                            about the launch.\r\n                          </div>\r\n                        </div>\r\n                        <div\r\n                          className=\"error_state w-form-fail\"\r\n                          role=\"region\"\r\n                          aria-label=\"Subscribe Form failure\"\r\n                        >\r\n                          <div>\r\n                            Oops! Something went wrong while submitting the\r\n                            form.\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div> */}\r\n            <div className=\"footer_links\">\r\n              <div className=\"wrapper_footer_links\">\r\n                <div className=\"flexbox_footer\">\r\n                  <div className=\"footer-left-block\">\r\n                    <div className=\"div-block-2\">\r\n                      <div className=\"footer-brand\">\r\n                        <Image\r\n                          src={\"/assets/images/logo/RR final logo.png\"}\r\n                          fill\r\n                          alt=\"Robb Report India\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"footer-right-block\">\r\n                    <div className=\"div-block-3\">\r\n                      <div className=\"title_footer\">Robb Report India</div>\r\n                      <div className=\"links_flex\">\r\n                        <Link\r\n                          href=\"/about-us\"\r\n                          className=\"footer_link w-inline-block \"\r\n                          rel=\"nofollow\"\r\n                        >\r\n                          About Us\r\n                        </Link>\r\n                        <Link\r\n                          href=\"/contact-us\"\r\n                          className=\"footer_link w-inline-block \"\r\n                          rel=\"nofollow\"\r\n                        >\r\n                          Contact Us\r\n                        </Link>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"div-block-3\">\r\n                      <div className=\"title_footer\">Support</div>\r\n                      <div className=\"links_flex\">\r\n                        <Link\r\n                          href=\"/privacy-policy\"\r\n                          className=\"footer_link w-inline-block\"\r\n                        >\r\n                          Privacy Policy\r\n                        </Link>\r\n                        <Link\r\n                          href=\"/terms-of-use\"\r\n                          className=\"footer_link w-inline-block\"\r\n                        >\r\n                          Terms Of Use\r\n                        </Link>\r\n                        <Link\r\n                          href=\"/disclaimer\"\r\n                          className=\"footer_link w-inline-block\"\r\n                        >\r\n                          Disclaimer\r\n                        </Link>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"div-block-3\">\r\n                      <div className=\"title_footer\">Follow Us</div>\r\n                      <div className=\"links_flex\">\r\n                        <Link\r\n                          href=\"https://www.instagram.com/robbreportindia/\"\r\n                          target=\"_blank\"\r\n                          rel=\"noopener noreferrer\"\r\n                          className=\"footer_link w-inline-block\"\r\n                        >\r\n                          Instagram\r\n                        </Link>\r\n                        {/* <Link\r\n                          href=\"https://www.facebook.com/robbreportindia\"\r\n                          target=\"_blank\"\r\n                          rel=\"noopener noreferrer\"\r\n                          className=\"footer_link w-inline-block\"\r\n                        >\r\n                          Facebook\r\n                        </Link>\r\n                        <Link\r\n                          href=\"\"\r\n                          target=\"_blank\"\r\n                          rel=\"noopener noreferrer\"\r\n                          className=\"footer_link w-inline-block\"\r\n                        >\r\n                          LinkedIn\r\n                        </Link> */}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"sub-footer\">\r\n        <div className=\"containerWrapper\">\r\n          <div className=\"wrapper_footer pb-0\">\r\n            <div className=\"flexbox_sub-footer\">\r\n              <div className=\"sub-footer-img-block\">\r\n                <Image\r\n                  src={\"/assets/images/logo/RPSG_Group_Logo.webp\"}\r\n                  fill\r\n                  alt=\"RP - Sanjiv Goenka Group Logo\"\r\n                />\r\n              </div>\r\n              <span className=\"footer-vr-line\" />\r\n              <p className=\"footer-tag-line\">\r\n                Robb Report India is published by RP - Sanjiv Goenka Group under\r\n                license from Robb Report Media, LLC, a subsidiary of Penske\r\n                Media Corporation.\r\n              </p>\r\n              <span className=\"footer-vr-line\" />\r\n              <div className=\"sub-footer-img-block-2\">\r\n                <Image\r\n                  src={\"/assets/images/logo/RPSG_Media_Logo.png\"}\r\n                  fill\r\n                  alt=\"RPSG - Media Logo\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,SAAS;IACb,qBACE;;0BACE,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;kCAqEb,cAAA,qKAAC;4BAAI,WAAU;sCACb,cAAA,qKAAC;gCAAI,WAAU;0CACb,cAAA,qKAAC;oCAAI,WAAU;;sDACb,qKAAC;4CAAI,WAAU;sDACb,cAAA,qKAAC;gDAAI,WAAU;0DACb,cAAA,qKAAC;oDAAI,WAAU;8DACb,cAAA,qKAAC,sHAAA,CAAA,UAAK;wDACJ,KAAK;wDACL,IAAI;wDACJ,KAAI;;;;;;;;;;;;;;;;;;;;;sDAKZ,qKAAC;4CAAI,WAAU;;8DACb,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;sEAAe;;;;;;sEAC9B,qKAAC;4DAAI,WAAU;;8EACb,qKAAC,qHAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;oEACV,KAAI;8EACL;;;;;;8EAGD,qKAAC,qHAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;oEACV,KAAI;8EACL;;;;;;;;;;;;;;;;;;8DAKL,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;sEAAe;;;;;;sEAC9B,qKAAC;4DAAI,WAAU;;8EACb,qKAAC,qHAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;8EACX;;;;;;8EAGD,qKAAC,qHAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;8EACX;;;;;;8EAGD,qKAAC,qHAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;8EACX;;;;;;;;;;;;;;;;;;8DAKL,qKAAC;oDAAI,WAAU;;sEACb,qKAAC;4DAAI,WAAU;sEAAe;;;;;;sEAC9B,qKAAC;4DAAI,WAAU;sEACb,cAAA,qKAAC,qHAAA,CAAA,UAAI;gEACH,MAAK;gEACL,QAAO;gEACP,KAAI;gEACJ,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4BnB,qKAAC;gBAAI,WAAU;0BACb,cAAA,qKAAC;oBAAI,WAAU;8BACb,cAAA,qKAAC;wBAAI,WAAU;kCACb,cAAA,qKAAC;4BAAI,WAAU;;8CACb,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC,sHAAA,CAAA,UAAK;wCACJ,KAAK;wCACL,IAAI;wCACJ,KAAI;;;;;;;;;;;8CAGR,qKAAC;oCAAK,WAAU;;;;;;8CAChB,qKAAC;oCAAE,WAAU;8CAAkB;;;;;;8CAK/B,qKAAC;oCAAK,WAAU;;;;;;8CAChB,qKAAC;oCAAI,WAAU;8CACb,cAAA,qKAAC,sHAAA,CAAA,UAAK;wCACJ,KAAK;wCACL,IAAI;wCACJ,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;uCAEe", "debugId": null}}, {"offset": {"line": 2221, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2227, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/SideBtnWrap.jsx"], "sourcesContent": ["import GetTheme from \"@/utils/GetTheme\";\r\nimport dynamic from \"next/dynamic\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { IoMoonOutline, IoSunnyOutline } from \"react-icons/io5\";\r\n\r\nconst BackToTop = dynamic(() => import(\"@/components/common/BacktoTop\"), {\r\n  ssr: false,\r\n});\r\n\r\nconst SideBtnWrap = () => {\r\n  const { theme, setTheme } = GetTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) return null;\r\n\r\n  return (\r\n    <div id=\"sideBtn_container\">\r\n      <BackToTop />\r\n      <div\r\n        className=\"toggle_cntr\"\r\n        onClick={() => setTheme(theme === \"dark\" ? \"light\" : \"dark\")}\r\n      >\r\n        <div className={`icon ${theme === \"dark\" ? \"scale-1\" : \"scale-0\"}`}>\r\n          <IoSunnyOutline />\r\n        </div>\r\n        <div className={`icon ${theme === \"dark\" ? \"scale-0\" : \"scale-1\"}`}>\r\n          <IoMoonOutline />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SideBtnWrap;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACxB,KAAK;;AAGP,MAAM,cAAc;IAClB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,UAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,qKAAC;QAAI,IAAG;;0BACN,qKAAC;;;;;0BACD,qKAAC;gBACC,WAAU;gBACV,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;;kCAErD,qKAAC;wBAAI,WAAW,CAAC,KAAK,EAAE,UAAU,SAAS,YAAY,WAAW;kCAChE,cAAA,qKAAC,wIAAA,CAAA,iBAAc;;;;;;;;;;kCAEjB,qKAAC;wBAAI,WAAW,CAAC,KAAK,EAAE,UAAU,SAAS,YAAY,WAAW;kCAChE,cAAA,qKAAC,wIAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;;AAKxB;uCAEe", "debugId": null}}, {"offset": {"line": 2310, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2317, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/layouts/Layout.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Header from \"@/components/common/Header\";\r\nimport Footer from \"@/components/common/Footer\";\r\nimport SideBtnWrap from \"../common/SideBtnWrap\";\r\n\r\n\r\nexport default function Layout({ menu, children }) {\r\n  return (\r\n    <>\r\n      <Header />\r\n      {children}\r\n      <Footer />\r\n      <SideBtnWrap/>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;;;;;AAGe,SAAS,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC/C,qBACE;;0BACE,qKAAC,+HAAA,CAAA,UAAM;;;;;YACN;0BACD,qKAAC,+HAAA,CAAA,UAAM;;;;;0BACP,qKAAC,oIAAA,CAAA,UAAW;;;;;;;AAGlB", "debugId": null}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2364, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/Loader.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst Loader = ({ className = \"loader-cont\" }) => {\r\n  return (\r\n    <div className={className}>\r\n      <div className=\"MuiBox-root css-0\">\r\n        <span\r\n          className=\"MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-indeterminate css-lrbo11-MuiLinearProgress-root\"\r\n          role=\"progressbar\"\r\n        >\r\n          <span className=\"MuiLinearProgress-bar1\"></span>\r\n          <span className=\"MuiLinearProgress-bar2\"></span>\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loader;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,SAAS,CAAC,EAAE,YAAY,aAAa,EAAE;IAC3C,qBACE,qKAAC;QAAI,WAAW;kBACd,cAAA,qKAAC;YAAI,WAAU;sBACb,cAAA,qKAAC;gBACC,WAAU;gBACV,MAAK;;kCAEL,qKAAC;wBAAK,WAAU;;;;;;kCAChB,qKAAC;wBAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK1B;uCAEe", "debugId": null}}, {"offset": {"line": 2412, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2438, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/smoothScroll/SmoothScroller.jsx"], "sourcesContent": ["import React, { useEffect, useLayoutEffect, useRef } from 'react';\r\nimport Tempus from '@studio-freight/tempus';\r\nimport Lenis from '@studio-freight/lenis';\r\nimport { usePathname, useSearchParams } from 'next/navigation';\r\n\r\nexport default function SmoothScroller() {\r\n  const lenis = useRef(null);\r\n  const pathname = usePathname();\r\n  const searchParams = useSearchParams();\r\n\r\n  useEffect(() => {\r\n    if (lenis.current) lenis.current.scrollTo(0, { immediate: true });\r\n  }, [pathname, searchParams, lenis]);\r\n\r\n  useLayoutEffect(() => {\r\n    lenis.current = new Lenis({\r\n      smoothWheel: true,\r\n      // Customize other instance settings here\r\n    });\r\n\r\n    const resize = setInterval(() => {\r\n      lenis.current.resize();\r\n    }, 150);\r\n\r\n    function onFrame(time) {\r\n      lenis.current.raf(time);\r\n    }\r\n\r\n    const unsubscribe = Tempus.add(onFrame);\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      clearInterval(resize);\r\n      lenis.current.destroy();\r\n      lenis.current = null;\r\n    };\r\n  }, []);\r\n\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,MAAM,QAAQ,CAAA,GAAA,mGAAA,CAAA,SAAM,AAAD,EAAE;IACrB,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD;IAEnC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,OAAO,EAAE,MAAM,OAAO,CAAC,QAAQ,CAAC,GAAG;YAAE,WAAW;QAAK;IACjE,GAAG;QAAC;QAAU;QAAc;KAAM;IAElC,CAAA,GAAA,mGAAA,CAAA,kBAAe,AAAD,EAAE;QACd,MAAM,OAAO,GAAG,IAAI,wJAAA,CAAA,UAAK,CAAC;YACxB,aAAa;QAEf;QAEA,MAAM,SAAS,YAAY;YACzB,MAAM,OAAO,CAAC,MAAM;QACtB,GAAG;QAEH,SAAS,QAAQ,IAAI;YACnB,MAAM,OAAO,CAAC,GAAG,CAAC;QACpB;QAEA,MAAM,cAAc,0JAAA,CAAA,UAAM,CAAC,GAAG,CAAC;QAE/B,OAAO;YACL;YACA,cAAc;YACd,MAAM,OAAO,CAAC,OAAO;YACrB,MAAM,OAAO,GAAG;QAClB;IACF,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2487, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/pages/_app.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useRouter } from \"next/router\";\r\nimport { ThemeProvider } from \"next-themes\";\r\nimport Layout from \"@/components/layouts/Layout\";\r\nimport TransitionLoader from \"@/components/common/TransitionLoader\";\r\nimport Loader from \"@/components/common/Loader\";\r\nimport \"@/styles/globals.css\";\r\nimport \"@/styles/homeHero.css\";\r\nimport \"@/styles/animatedParagraph.css\";\r\nimport \"@/styles/cardGridSection.css\";\r\nimport \"@/styles/highlightSection.css\";\r\nimport \"@/styles/swiperCardSection.css\";\r\nimport \"@/styles/categoryPage.css\";\r\nimport \"@/styles/stories.css\";\r\nimport \"@/styles/navbar.css\";\r\nimport \"@/styles/footer.css\";\r\nimport \"@/styles/mobileMenu.css\";\r\nimport \"@/styles/herosectionbanner.css\";\r\nimport \"@/styles/threecardsection.css\";\r\nimport \"@/styles/tworiversection.css\";\r\nimport \"@/styles/author.css\";\r\nimport \"@/styles/error.css\";\r\nimport \"@/styles/about.css\";\r\nimport \"@/styles/filterDrawer.css\";\r\nimport SmoothScroller from \"@/components/smoothScroll/SmoothScroller\";\r\n\r\nexport default function App({ Component, pageProps }) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const handleStart = (url, { shallow }) => {\r\n      if (!shallow) {\r\n        setIsLoading(true);\r\n      }\r\n    };\r\n\r\n    const handleStop = () => setIsLoading(false);\r\n\r\n    router.events.on(\"routeChangeStart\", handleStart);\r\n    router.events.on(\"routeChangeComplete\", handleStop);\r\n    router.events.on(\"routeChangeError\", handleStop);\r\n\r\n    return () => {\r\n      router.events.off(\"routeChangeStart\", handleStart);\r\n      router.events.off(\"routeChangeComplete\", handleStop);\r\n      router.events.off(\"routeChangeError\", handleStop);\r\n    };\r\n  }, []);\r\n  return (\r\n    <>\r\n    \r\n        <ThemeProvider defaultTheme=\"dark\">\r\n          <TransitionLoader />\r\n          <SmoothScroller/>\r\n          <Layout>\r\n              {isLoading ? <Loader /> : <Component {...pageProps} />}\r\n          </Layout>\r\n        </ThemeProvider>\r\n     \r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAmBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEe,SAAS,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE;YACnC,IAAI,CAAC,SAAS;gBACZ,aAAa;YACf;QACF;QAEA,MAAM,aAAa,IAAM,aAAa;QAEtC,OAAO,MAAM,CAAC,EAAE,CAAC,oBAAoB;QACrC,OAAO,MAAM,CAAC,EAAE,CAAC,uBAAuB;QACxC,OAAO,MAAM,CAAC,EAAE,CAAC,oBAAoB;QAErC,OAAO;YACL,OAAO,MAAM,CAAC,GAAG,CAAC,oBAAoB;YACtC,OAAO,MAAM,CAAC,GAAG,CAAC,uBAAuB;YACzC,OAAO,MAAM,CAAC,GAAG,CAAC,oBAAoB;QACxC;IACF,GAAG,EAAE;IACL,qBACE;kBAEI,cAAA,qKAAC,4HAAA,CAAA,gBAAa;YAAC,cAAa;;8BAC1B,qKAAC,yIAAA,CAAA,UAAgB;;;;;8BACjB,qKAAC,6IAAA,CAAA,UAAc;;;;;8BACf,qKAAC,gIAAA,CAAA,UAAM;8BACF,0BAAY,qKAAC,+HAAA,CAAA,UAAM;;;;6CAAM,qKAAC;wBAAW,GAAG,SAAS;;;;;;;;;;;;;;;;;;AAMhE", "debugId": null}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2619, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/WebPageSchema.jsx"], "sourcesContent": ["const WebPageSchema = ({ name, description, url }) => {\r\n  const schemaData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"WebPage\",\r\n    name: name,\r\n    description: description,\r\n    speakable: {\r\n      \"@type\": \"SpeakableSpecification\",\r\n      xpath: [\"//title\", \"//meta[@name='description']/@content\"],\r\n    },\r\n    url: url,\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}\r\n    ></script>\r\n  );\r\n};\r\n\r\nexport default WebPageSchema;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;IAC/C,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,aAAa;QACb,WAAW;YACT,SAAS;YACT,OAAO;gBAAC;gBAAW;aAAuC;QAC5D;QACA,KAAK;IACP;IAEA,qBACE,qKAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAY;;;;;;AAGpE;uCAEe", "debugId": null}}, {"offset": {"line": 2651, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2657, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/NewsMediaOrganizationSchema.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst NewsMediaOrganizationSchema = ({\r\n  name,\r\n  clientLink,\r\n  logoUrl,\r\n  address,\r\n  contact,\r\n  sameAs,\r\n}) => {\r\n  const schemaData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"NewsMediaOrganization\",\r\n    name: name,\r\n    url: clientLink,\r\n    logo: {\r\n      \"@type\": \"ImageObject\",\r\n      url: logoUrl,\r\n    },\r\n    address: {\r\n      \"@type\": \"PostalAddress\",\r\n      streetAddress: address?.streetAddress,\r\n      addressLocality: address?.addressLocality,\r\n      addressRegion: address?.addressRegion,\r\n      postalCode: address?.postalCode,\r\n    },\r\n    contactPoint: {\r\n      \"@type\": \"ContactPoint\",\r\n      telephone: contact?.telephone,\r\n      contactType: contact?.contactType,\r\n      areaServed: contact?.areaServed,\r\n      availableLanguage: contact?.availableLanguage,\r\n      hoursAvailable: {\r\n        opens: contact?.hoursAvailable?.opens,\r\n        closes: contact?.hoursAvailable?.closes,\r\n      },\r\n    },\r\n    sameAs: sameAs,\r\n  };\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}\r\n    ></script>\r\n  );\r\n};\r\n\r\nexport default NewsMediaOrganizationSchema;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,8BAA8B,CAAC,EACnC,IAAI,EACJ,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACP;IACC,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,KAAK;QACL,MAAM;YACJ,SAAS;YACT,KAAK;QACP;QACA,SAAS;YACP,SAAS;YACT,eAAe,SAAS;YACxB,iBAAiB,SAAS;YAC1B,eAAe,SAAS;YACxB,YAAY,SAAS;QACvB;QACA,cAAc;YACZ,SAAS;YACT,WAAW,SAAS;YACpB,aAAa,SAAS;YACtB,YAAY,SAAS;YACrB,mBAAmB,SAAS;YAC5B,gBAAgB;gBACd,OAAO,SAAS,gBAAgB;gBAChC,QAAQ,SAAS,gBAAgB;YACnC;QACF;QACA,QAAQ;IACV;IACA,qBACE,qKAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAY;;;;;;AAGpE;uCAEe", "debugId": null}}, {"offset": {"line": 2706, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2712, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/SiteNavigationSchema.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { menus } from \"@/helpers/MenuData\";\r\nimport { Const } from \"@/utils/Constants\";\r\n\r\nconst SiteNavigationSchema = () => {\r\n  const schemaData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"SiteNavigationElement\",\r\n    name: [],\r\n    url: [],\r\n  };\r\n  menus.forEach((menu) => {\r\n    schemaData.name.push(menu.name);\r\n    schemaData.url.push(`${Const.ClientLink}${menu.link}`);\r\n    if (menu && menu.submenus && menu.submenus.length > 0) {\r\n      menu.submenus.forEach((submenu) => {\r\n        if (submenu.name !== \"All\") {\r\n          schemaData.name.push(submenu.name);\r\n          schemaData.url.push(`${Const.ClientLink}${submenu.link}`);\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}\r\n    ></script>\r\n  );\r\n};\r\n\r\nexport default SiteNavigationSchema;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAuB;IAC3B,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,MAAM,EAAE;QACR,KAAK,EAAE;IACT;IACA,oHAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC;QACb,WAAW,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;QAC9B,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,mHAAA,CAAA,QAAK,CAAC,UAAU,GAAG,KAAK,IAAI,EAAE;QACrD,IAAI,QAAQ,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YACrD,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACrB,IAAI,QAAQ,IAAI,KAAK,OAAO;oBAC1B,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI;oBACjC,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,mHAAA,CAAA,QAAK,CAAC,UAAU,GAAG,QAAQ,IAAI,EAAE;gBAC1D;YACF;QACF;IACF;IAEA,qBACE,qKAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAY;;;;;;AAGpE;uCAEe", "debugId": null}}, {"offset": {"line": 2754, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2760, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/SeoHeader.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Head from \"next/head\";\r\nimport { useRouter } from \"next/router\";\r\nimport { Const } from \"@/utils/Constants\";\r\nimport WebPageSchema from \"@/components/seo/WebPageSchema\";\r\nimport NewsMediaOrganizationSchema from \"@/components/seo/NewsMediaOrganizationSchema\";\r\nimport SiteNavigationSchema from \"@/components/seo/SiteNavigationSchema\";\r\n\r\nconst SeoHeader = ({ meta = {}, type = \"website\" }) => {\r\n  const router = useRouter();\r\n  const defaultImage = `${Const.ClientLink}/favicon/favicon-192x192.png`;\r\n  const canonical = `${Const.ClientLink}/${router.asPath?.slice(1)}`;\r\n  return (\r\n    <Head>\r\n      <title>{meta?.title || \"\"}</title>\r\n      <meta name=\"description\" content={meta?.description || \"\"} />\r\n      <meta name=\"keywords\" content={meta?.keywords || \"\"} />\r\n      {meta?.author && <meta name=\"author\" content={meta?.author || \"\"} />}\r\n      <meta name=\"publisher\" content={Const.Brand} />\r\n      <meta\r\n        name=\"robots\"\r\n        content={\r\n          `${meta?.robots}, max-image-preview:large` ||\r\n          \"noindex,nofollow, max-image-preview:large\"\r\n        }\r\n      />\r\n      <link rel=\"canonical\" href={meta?.canonical || canonical} />\r\n      {/* OG Tags */}\r\n      <meta property=\"fb:app_id\" content=\"446498535209610\" />\r\n      <meta property=\"og:locale\" content=\"en_IN\" />\r\n      <meta property=\"og:type\" content={type} />\r\n      <meta property=\"og:title\" content={meta?.og?.title || \"\"} />\r\n      <meta property=\"og:description\" content={meta?.og?.description || \"\"} />\r\n      <meta property=\"og:url\" content={canonical} />\r\n      <meta property=\"og:site_name\" content={Const.Brand} />\r\n      <meta property=\"og:image\" content={meta?.og?.image || defaultImage} />\r\n      <meta property=\"og:image:width\" content=\"1200\" />\r\n      <meta property=\"og:image:height\" content=\"630\" />\r\n      {/* Twitter Tag */}\r\n      <meta\r\n        name=\"twitter:card\"\r\n        content={meta?.twitter?.card || \"summary_large_image\"}\r\n      />\r\n      <meta\r\n        name=\"twitter:title\"\r\n        content={meta?.twitter?.title || meta?.title}\r\n      />\r\n      <meta\r\n        name=\"twitter:description\"\r\n        content={meta?.twitter?.description || meta?.description}\r\n      />\r\n      <meta name=\"twitter:site\" content={\"@robbreportindia\"} />\r\n      <meta\r\n        name=\"twitter:image\"\r\n        content={meta?.twitter?.image || defaultImage}\r\n      />\r\n      <meta name=\"twitter:creator\" content={\"@robbreportindia\"} />\r\n      <meta charSet=\"UTF-8\" />\r\n      <meta httpEquiv=\"Content-Type\" content=\"text/html;charset=UTF-8\" />\r\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n      <link\r\n        rel=\"icon\"\r\n        type=\"image/png\"\r\n        sizes=\"16x16\"\r\n        href=\"/favicon/favicon-16x16.png\"\r\n      />\r\n      <link\r\n        rel=\"icon\"\r\n        type=\"image/png\"\r\n        sizes=\"32x32\"\r\n        href=\"/favicon/favicon-32x32.png\"\r\n      />\r\n      <link\r\n        rel=\"icon\"\r\n        type=\"image/png\"\r\n        sizes=\"192x192\"\r\n        href=\"/favicon/favicon-192x192.png\"\r\n      />\r\n      <link rel=\"apple-touch-icon\" href=\"/favicon/apple-touch-icon.png\" />\r\n      <link\r\n        rel=\"alternate\"\r\n        hrefLang=\"en-in\"\r\n        href={meta?.canonical || canonical}\r\n      />\r\n      <WebPageSchema\r\n        name={meta?.title || \"\"}\r\n        description={meta?.description || \"\"}\r\n        url={meta?.canonical || canonical}\r\n      />\r\n      <NewsMediaOrganizationSchema\r\n        name={Const.Brand}\r\n        clientLink={`${Const.ClientLink}/`}\r\n        logoUrl={`${Const.ClientLink}/RR final logo.png`}\r\n        address={{\r\n          streetAddress:\r\n            \"RPSG Lifestyle Media, Thapar House, 3rd floor, Janpath Lane\",\r\n          addressLocality: \"New Delhi\",\r\n          addressRegion: \"India\",\r\n          postalCode: \"110 001\",\r\n        }}\r\n        contact={{\r\n          telephone: \"+91–11–23486700\",\r\n          contactType: \"Customer Service\",\r\n          areaServed: \"IN\",\r\n          availableLanguage: \"English\",\r\n          hoursAvailable: {\r\n            opens: \"09:00\",\r\n            closes: \"19:00\",\r\n          },\r\n        }}\r\n        sameAs={[\r\n          \"https://www.facebook.com/robbreporterindia\",\r\n          \"https://www.instagram.com/robbreporterindia/\",\r\n          \"https://twitter.com/robbreportindia\",\r\n          \"https://www.youtube.com/@robbreportIndia\",\r\n        ]}\r\n      />\r\n      <SiteNavigationSchema />\r\n    </Head>\r\n  );\r\n};\r\n\r\nexport default SeoHeader;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,YAAY,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE;IAChD,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,GAAG,mHAAA,CAAA,QAAK,CAAC,UAAU,CAAC,4BAA4B,CAAC;IACtE,MAAM,YAAY,GAAG,mHAAA,CAAA,QAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,MAAM,IAAI;IAClE,qBACE,qKAAC,6HAAA,CAAA,UAAI;;0BACH,qKAAC;0BAAO,MAAM,SAAS;;;;;;0BACvB,qKAAC;gBAAK,MAAK;gBAAc,SAAS,MAAM,eAAe;;;;;;0BACvD,qKAAC;gBAAK,MAAK;gBAAW,SAAS,MAAM,YAAY;;;;;;YAChD,MAAM,wBAAU,qKAAC;gBAAK,MAAK;gBAAS,SAAS,MAAM,UAAU;;;;;;0BAC9D,qKAAC;gBAAK,MAAK;gBAAY,SAAS,mHAAA,CAAA,QAAK,CAAC,KAAK;;;;;;0BAC3C,qKAAC;gBACC,MAAK;gBACL,SACE,GAAG,MAAM,OAAO,yBAAyB,CAAC,IAC1C;;;;;;0BAGJ,qKAAC;gBAAK,KAAI;gBAAY,MAAM,MAAM,aAAa;;;;;;0BAE/C,qKAAC;gBAAK,UAAS;gBAAY,SAAQ;;;;;;0BACnC,qKAAC;gBAAK,UAAS;gBAAY,SAAQ;;;;;;0BACnC,qKAAC;gBAAK,UAAS;gBAAU,SAAS;;;;;;0BAClC,qKAAC;gBAAK,UAAS;gBAAW,SAAS,MAAM,IAAI,SAAS;;;;;;0BACtD,qKAAC;gBAAK,UAAS;gBAAiB,SAAS,MAAM,IAAI,eAAe;;;;;;0BAClE,qKAAC;gBAAK,UAAS;gBAAS,SAAS;;;;;;0BACjC,qKAAC;gBAAK,UAAS;gBAAe,SAAS,mHAAA,CAAA,QAAK,CAAC,KAAK;;;;;;0BAClD,qKAAC;gBAAK,UAAS;gBAAW,SAAS,MAAM,IAAI,SAAS;;;;;;0BACtD,qKAAC;gBAAK,UAAS;gBAAiB,SAAQ;;;;;;0BACxC,qKAAC;gBAAK,UAAS;gBAAkB,SAAQ;;;;;;0BAEzC,qKAAC;gBACC,MAAK;gBACL,SAAS,MAAM,SAAS,QAAQ;;;;;;0BAElC,qKAAC;gBACC,MAAK;gBACL,SAAS,MAAM,SAAS,SAAS,MAAM;;;;;;0BAEzC,qKAAC;gBACC,MAAK;gBACL,SAAS,MAAM,SAAS,eAAe,MAAM;;;;;;0BAE/C,qKAAC;gBAAK,MAAK;gBAAe,SAAS;;;;;;0BACnC,qKAAC;gBACC,MAAK;gBACL,SAAS,MAAM,SAAS,SAAS;;;;;;0BAEnC,qKAAC;gBAAK,MAAK;gBAAkB,SAAS;;;;;;0BACtC,qKAAC;gBAAK,SAAQ;;;;;;0BACd,qKAAC;gBAAK,WAAU;gBAAe,SAAQ;;;;;;0BACvC,qKAAC;gBAAK,MAAK;gBAAW,SAAQ;;;;;;0BAC9B,qKAAC;gBACC,KAAI;gBACJ,MAAK;gBACL,OAAM;gBACN,MAAK;;;;;;0BAEP,qKAAC;gBACC,KAAI;gBACJ,MAAK;gBACL,OAAM;gBACN,MAAK;;;;;;0BAEP,qKAAC;gBACC,KAAI;gBACJ,MAAK;gBACL,OAAM;gBACN,MAAK;;;;;;0BAEP,qKAAC;gBAAK,KAAI;gBAAmB,MAAK;;;;;;0BAClC,qKAAC;gBACC,KAAI;gBACJ,UAAS;gBACT,MAAM,MAAM,aAAa;;;;;;0BAE3B,qKAAC,mIAAA,CAAA,UAAa;gBACZ,MAAM,MAAM,SAAS;gBACrB,aAAa,MAAM,eAAe;gBAClC,KAAK,MAAM,aAAa;;;;;;0BAE1B,qKAAC,iJAAA,CAAA,UAA2B;gBAC1B,MAAM,mHAAA,CAAA,QAAK,CAAC,KAAK;gBACjB,YAAY,GAAG,mHAAA,CAAA,QAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBAClC,SAAS,GAAG,mHAAA,CAAA,QAAK,CAAC,UAAU,CAAC,kBAAkB,CAAC;gBAChD,SAAS;oBACP,eACE;oBACF,iBAAiB;oBACjB,eAAe;oBACf,YAAY;gBACd;gBACA,SAAS;oBACP,WAAW;oBACX,aAAa;oBACb,YAAY;oBACZ,mBAAmB;oBACnB,gBAAgB;wBACd,OAAO;wBACP,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN;oBACA;oBACA;oBACA;iBACD;;;;;;0BAEH,qKAAC,0IAAA,CAAA,UAAoB;;;;;;;;;;;AAG3B;uCAEe", "debugId": null}}, {"offset": {"line": 3091, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3097, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/BreadcrumbSchema.jsx"], "sourcesContent": ["import { Const } from \"@/utils/Constants\";\r\nimport Head from \"next/head\";\r\n\r\nconst BreadcrumbSchema = ({ itemList }) => {\r\n  const breadcrumb = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    itemListElement: [\r\n      {\r\n        \"@type\": \"ListItem\",\r\n        position: 1,\r\n        item: {\r\n          \"@id\": Const.ClientLink,\r\n          name: \"Home\",\r\n        },\r\n      },\r\n    ],\r\n  };\r\n\r\n  if (itemList && itemList.length > 0) {\r\n    itemList.forEach((item, index) => {\r\n      breadcrumb.itemListElement.push({\r\n        \"@type\": \"ListItem\",\r\n        position: index + 2,\r\n        item: {\r\n          \"@id\": Const.ClientLink + item.slug,\r\n          name: item.name,\r\n        },\r\n      });\r\n    });\r\n  }\r\n\r\n  return (\r\n    <Head>\r\n      <script\r\n        type=\"application/ld+json\"\r\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumb) }}\r\n      ></script>\r\n    </Head>\r\n  );\r\n};\r\n\r\nexport default BreadcrumbSchema;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE;IACpC,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf;gBACE,SAAS;gBACT,UAAU;gBACV,MAAM;oBACJ,OAAO,mHAAA,CAAA,QAAK,CAAC,UAAU;oBACvB,MAAM;gBACR;YACF;SACD;IACH;IAEA,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;QACnC,SAAS,OAAO,CAAC,CAAC,MAAM;YACtB,WAAW,eAAe,CAAC,IAAI,CAAC;gBAC9B,SAAS;gBACT,UAAU,QAAQ;gBAClB,MAAM;oBACJ,OAAO,mHAAA,CAAA,QAAK,CAAC,UAAU,GAAG,KAAK,IAAI;oBACnC,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;IACF;IAEA,qBACE,qKAAC,6HAAA,CAAA,UAAI;kBACH,cAAA,qKAAC;YACC,MAAK;YACL,yBAAyB;gBAAE,QAAQ,KAAK,SAAS,CAAC;YAAY;;;;;;;;;;;AAItE;uCAEe", "debugId": null}}, {"offset": {"line": 3151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3157, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/ImageGallerySchema.jsx"], "sourcesContent": ["import Head from \"next/head\";\n\nconst ImageGallerySchema = ({\n  title,\n  description,\n  url,\n  datePublished,\n  data,\n}) => {\n  const images = data?.map((item) => ({\n    \"@type\": \"ImageObject\",\n    url: item?.image || \"\",\n  }));\n  const schemaData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"ImageGallery\",\n    url: url,\n    datePublished: datePublished,\n    mainEntityOfPage: {\n      \"@type\": \"WebPage\",\n      \"@id\": url,\n      headline: title,\n      description: description,\n    },\n    image: images,\n  };\n\n  return (\n    <Head>\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}\n      ></script>\n    </Head>\n  );\n};\n\nexport default ImageGallerySchema;\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,qBAAqB,CAAC,EAC1B,KAAK,EACL,WAAW,EACX,GAAG,EACH,aAAa,EACb,IAAI,EACL;IACC,MAAM,SAAS,MAAM,IAAI,CAAC,OAAS,CAAC;YAClC,SAAS;YACT,KAAK,MAAM,SAAS;QACtB,CAAC;IACD,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,KAAK;QACL,eAAe;QACf,kBAAkB;YAChB,SAAS;YACT,OAAO;YACP,UAAU;YACV,aAAa;QACf;QACA,OAAO;IACT;IAEA,qBACE,qKAAC,6HAAA,CAAA,UAAI;kBACH,cAAA,qKAAC;YACC,MAAK;YACL,yBAAyB;gBAAE,QAAQ,KAAK,SAAS,CAAC;YAAY;;;;;;;;;;;AAItE;uCAEe", "debugId": null}}, {"offset": {"line": 3200, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3206, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/MediaGallerySchema.jsx"], "sourcesContent": ["import Head from \"next/head\";\n\nconst MediaGallerySchema = ({ title, description, data }) => {\n  const associatedMedia = data?.map((item) => ({\n    \"@type\": \"ImageObject\",\n    name: item?.title || \"\",\n    thumbnailUrl: item?.image || \"\",\n  }));\n  const schemaData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"MediaGallery\",\n    headline: title,\n    description: description,\n    mainEntityOfPage: {\n      \"@type\": \"ImageGallery\",\n      associatedMedia: associatedMedia,\n    },\n  };\n\n  return (\n    <Head>\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}\n      ></script>\n    </Head>\n  );\n};\n\nexport default MediaGallerySchema;\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE;IACtD,MAAM,kBAAkB,MAAM,IAAI,CAAC,OAAS,CAAC;YAC3C,SAAS;YACT,MAAM,MAAM,SAAS;YACrB,cAAc,MAAM,SAAS;QAC/B,CAAC;IACD,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,UAAU;QACV,aAAa;QACb,kBAAkB;YAChB,SAAS;YACT,iBAAiB;QACnB;IACF;IAEA,qBACE,qKAAC,6HAAA,CAAA,UAAI;kBACH,cAAA,qKAAC;YACC,MAAK;YACL,yBAAyB;gBAAE,QAAQ,KAAK,SAAS,CAAC;YAAY;;;;;;;;;;;AAItE;uCAEe", "debugId": null}}, {"offset": {"line": 3247, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3253, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/amp/ampCss.js"], "sourcesContent": ["export const ampCSS = `\n.story-top {\n    padding: 2rem 0 0;\n    position: sticky;\n    top: 50px;\n    display: flex;\n    justify-content: center;\n    flex-direction: column;\n    z-index: 9;\n}\n.container {\n    padding-right: 20px;\n    padding-left: 20px;\n}\n.story-top h1 {\n    font-size: 30px;\n    line-height: 1;\n    margin-bottom: 0;\n    font-weight: 400;\n    display: block;\n    color: #000;\n    font-style: normal;\n}\n`;\n\nexport const ampNavbarCSS = `\n    .nav-wrapper {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 1rem;\n      background: white;\n      position: relative;\n    }\n    .logo {\n      font-size: 1.5rem;\n      font-weight: bold;\n      text-decoration: none;\n      color: #000;\n    }\n    .hamburger {\n      display: flex;\n      flex-direction: column;\n      cursor: pointer;\n      padding: 0.5rem;\n    }\n    .hamburger .line {\n      width: 25px;\n      height: 3px;\n      background-color: #000;\n      margin: 3px 0;\n      transition: 0.3s;\n    }\n    .hamburger.is-active .line:nth-child(1) {\n      transform: rotate(-45deg) translate(-5px, 6px);\n    }\n    .hamburger.is-active .line:nth-child(2) {\n      opacity: 0;\n    }\n    .hamburger.is-active .line:nth-child(3) {\n      transform: rotate(45deg) translate(-5px, -6px);\n    }\n    .mob-menu {\n      background: white;\n      padding: 1rem;\n      width: 300px;\n    }\n    .mob-menu ul {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n    .mob-menu li {\n      margin: 0.5rem 0;\n    }\n    .mob-menu a {\n      text-decoration: none;\n      color: #000;\n      font-size: 1rem;\n    }\n`;\n\nexport const webStoryDetailCSS = `\n\t\t\t\t/* AMP Web Stories Font Configuration */\n\t\t\t\t@font-face {\n\t\t\t\t\tfont-family: \"NeueHaasDisplayBold\";\n\t\t\t\t\tsrc: url(/Assets/NeueHaasDisplayBold.ttf);\n\t\t\t\t\tfont-display: swap;\n\t\t\t\t}\n\t\t\t\t@font-face {\n\t\t\t\t\tfont-family: \"Bitter\";\n\t\t\t\t\tsrc: url(/Assets/Bitter-VariableFont_wght.ttf);\n\t\t\t\t\tfont-display: swap;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.brand-logo {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 15px;\n\t\t\t\t\tleft: 15px;\n\t\t\t\t\tcolor: white;\n\t\t\t\t\ttext-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\n\t\t\t\t\tz-index: 10;\n\t\t\t\t}\n\n\t\t\t\t.back-button {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 20px;\n\t\t\t\t\tright: 20px;\n\t\t\t\t\tz-index: 10;\n\t\t\t\t}\n\n\t\t\t\t.back-link {\n\t\t\t\t\tcolor: white;\n\t\t\t\t\ttext-decoration: none;\n\t\t\t\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\t\t\t\tpadding: 8px 12px;\n\t\t\t\t\tborder-radius: 20px;\n\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\ttext-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);\n\t\t\t\t\ttransition: background 0.3s ease;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t\tfont-family: \"Bitter\", serif;\n\t\t\t\t}\n        .next-story-preview{\n          padding-bottom: 4rem;\n        }\n        .next-story-preview h2{\n          text-align: center;\n          font-size: 24px;\n          letter-spacing: 1px;\n\t\t\t\t\tfont-family: \"NeueHaasDisplayBold\", sans-serif;\n\t\t\t\t\tfont-weight: 700;\n        }\n\t\t\t\t.back-link:hover {\n\t\t\t\t\tbackground: rgba(0, 0, 0, 0.7);\n\t\t\t\t}\n\t\t\t\t.story-content {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tpadding: 15px;\n\t\t\t\t\tbackground: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.8) 100%);\n\t\t\t\t}\n\t\t\t\t.story-text {\n\t\t\t\t\tcolor: white;\n\t\t\t\t\ttext-align: left;\n\t\t\t\t}\n\t\t\t\t/* Headlines use primary font (NeueHaasDisplayBold) */\n\t\t\t\t.story-text h1 {\n\t\t\t\t\tfont-size: 24px;\n\t\t\t\t\tfont-family: \"NeueHaasDisplayBold\", sans-serif;\n\t\t\t\t\tmargin-bottom: 10px;\n\t\t\t\t\tfont-weight: 700;\n          letter-spacing: 1px;\n\t\t\t\t\tline-height: 1.1;\n\t\t\t\t}\n\t\t\t\t/* Body text uses secondary font (Bitter) */\n\t\t\t\t.story-text div {\n\t\t\t\t\tfont-family: \"Bitter\", serif;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\tline-height: 1.3;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t}\n\t\t\t\t.story-text p {\n\t\t\t\t\tfont-family: \"Bitter\", serif;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\tline-height: 1.3;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t}\n        .story-text p a{\n        color: #fff;\n        }\n\t\t\t\t.story-text small {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin-top: 8px;\n\t\t\t\t\topacity: 0.8;\n\t\t\t\t\tfont-family: \"Bitter\", serif;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tfont-weight: 300;\n\t\t\t\t}\n                [template=vertical]{\n                align-content: end;\n                }\n\n        .next-story-preview {\n          color: #fff\n        }\n\t\t\t\t/* Force show AMP story navigation buttons on mobile */\n\t\t\t\t:global(.amphtml-story-button-container) {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tvisibility: visible;\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\n\t\t\t\t:global(.amphtml-story-button-move) {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tvisibility: visible;\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\n\t\t\t\t@media (max-width: 768px) {\n\t\t\t\t\t:global(.amphtml-story-button-container) {\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tvisibility: visible;\n\t\t\t\t\t\topacity: 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\n\t\t\t`;\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuBvB,CAAC;AAEM,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuD7B,CAAC;AAEM,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgI/B,CAAC", "debugId": null}}, {"offset": {"line": 3467, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3473, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/pages/webstories/%5Bcategory%5D/%5Bslug%5D/index.jsx"], "sourcesContent": ["import React from \"react\";\n// import { getWebStories } from \"@/pages/api/WebStoriesApi\"; // TODO: Uncomment when API is ready\nimport Head from \"next/head\";\n\nimport { dateFormateWithTimeShort } from \"@/utils/Util\";\nimport SeoHeader from \"@/components/seo/SeoHeader\";\nimport BreadcrumbSchema from \"@/components/seo/BreadcrumbSchema\";\nimport ImageGallerySchema from \"@/components/seo/ImageGallerySchema\";\nimport MediaGallerySchema from \"@/components/seo/MediaGallerySchema\";\nimport { Const } from \"@/utils/Constants\";\nimport { webStoryDetailCSS } from \"@/components/amp/ampCss\";\n\nexport const config = { amp: true };\n\n// DUMMY DATA - TODO: Remove when API is ready\nconst DUMMY_WEB_STORIES = {\n\t\"luxury-cars-2024\": {\n\t\ttitle: \"Top 5 Luxury Cars of 2024\",\n\t\tcoverImg: \"https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop\",\n\t\taltName: \"Luxury Cars 2024\",\n\t\ttimestamp: \"2024-01-15T10:00:00Z\",\n\t\tslides: [\n\t\t\t{\n\t\t\t\ttitle: \"Ferrari SF90 Stradale\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The pinnacle of Ferrari engineering, combining hybrid technology with pure performance.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Ferrari SF90 Stradale\",\n\t\t\t\tcontributor: [\"Ferrari Press\"],\n\t\t\t\ttimestamp: \"2024-01-15T10:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Lamborghini Revuelto\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The new flagship from Lamborghini featuring a revolutionary V12 hybrid powertrain.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Lamborghini Revuelto\",\n\t\t\t\tcontributor: [\"Lamborghini Media\"],\n\t\t\t\ttimestamp: \"2024-01-15T10:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Rolls-Royce Spectre\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The first fully electric Rolls-Royce, maintaining the brand's legendary luxury.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Rolls-Royce Spectre\",\n\t\t\t\tcontributor: [\"Rolls-Royce Press\"],\n\t\t\t\ttimestamp: \"2024-01-15T10:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Bentley Continental GT Speed\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The most powerful Continental GT ever, with unmatched grand touring capabilities.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Bentley Continental GT Speed\",\n\t\t\t\tcontributor: [\"Bentley Motors\"],\n\t\t\t\ttimestamp: \"2024-01-15T10:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"McLaren 750S\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The latest in McLaren's Super Series, delivering track-focused performance for the road.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"McLaren 750S\",\n\t\t\t\tcontributor: [\"McLaren Automotive\"],\n\t\t\t\ttimestamp: \"2024-01-15T10:00:00Z\",\n\t\t\t},\n\t\t],\n\t},\n\t\"luxury-watches-2024\": {\n\t\ttitle: \"Exquisite Timepieces of 2024\",\n\t\tcoverImg: \"https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop\",\n\t\taltName: \"Luxury Watches 2024\",\n\t\ttimestamp: \"2024-01-20T14:00:00Z\",\n\t\tslides: [\n\t\t\t{\n\t\t\t\ttitle: \"Patek Philippe Nautilus\",\n\t\t\t\tdescription: \"<p>The iconic sports watch that defines luxury horology excellence.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Patek Philippe Nautilus\",\n\t\t\t\tcontributor: [\"Patek Philippe\"],\n\t\t\t\ttimestamp: \"2024-01-20T14:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Rolex Daytona\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The legendary chronograph that has become synonymous with racing heritage.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Rolex Daytona\",\n\t\t\t\tcontributor: [\"Rolex SA\"],\n\t\t\t\ttimestamp: \"2024-01-20T14:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Audemars Piguet Royal Oak\",\n\t\t\t\tdescription: \"<p>The revolutionary design that changed luxury sports watches forever.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Audemars Piguet Royal Oak\",\n\t\t\t\tcontributor: [\"Audemars Piguet\"],\n\t\t\t\ttimestamp: \"2024-01-20T14:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Vacheron Constantin Overseas\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>Swiss craftsmanship meets contemporary design in this exceptional timepiece.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Vacheron Constantin Overseas\",\n\t\t\t\tcontributor: [\"Vacheron Constantin\"],\n\t\t\t\ttimestamp: \"2024-01-20T14:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Richard Mille RM 11-03\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>Cutting-edge materials and innovative design define this modern masterpiece.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Richard Mille RM 11-03\",\n\t\t\t\tcontributor: [\"Richard Mille\"],\n\t\t\t\ttimestamp: \"2024-01-20T14:00:00Z\",\n\t\t\t},\n\t\t],\n\t},\n\t\"luxury-yachts-2024\": {\n\t\ttitle: \"Magnificent Yachts of 2024\",\n\t\tcoverImg: \"https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop\",\n\t\taltName: \"Luxury Yachts 2024\",\n\t\ttimestamp: \"2024-01-25T16:00:00Z\",\n\t\tslides: [\n\t\t\t{\n\t\t\t\ttitle: \"Azzam Superyacht\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The world's largest private yacht, a floating palace of unprecedented luxury.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Azzam Superyacht\",\n\t\t\t\tcontributor: [\"Lürssen Yachts\"],\n\t\t\t\ttimestamp: \"2024-01-25T16:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Eclipse Yacht\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>A masterpiece of naval architecture with unparalleled amenities and security.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Eclipse Yacht\",\n\t\t\t\tcontributor: [\"Blohm+Voss\"],\n\t\t\t\ttimestamp: \"2024-01-25T16:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Dilbar Superyacht\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The largest yacht by gross tonnage, featuring extraordinary interior volume.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Dilbar Superyacht\",\n\t\t\t\tcontributor: [\"Lürssen Yachts\"],\n\t\t\t\ttimestamp: \"2024-01-25T16:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Sailing Yacht A\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The world's largest sailing yacht, combining traditional sailing with modern luxury.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Sailing Yacht A\",\n\t\t\t\tcontributor: [\"Nobiskrug\"],\n\t\t\t\ttimestamp: \"2024-01-25T16:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Octopus Yacht\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>An explorer yacht designed for adventure and luxury in the world's most remote locations.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Octopus Yacht\",\n\t\t\t\tcontributor: [\"Lürssen Yachts\"],\n\t\t\t\ttimestamp: \"2024-01-25T16:00:00Z\",\n\t\t\t},\n\t\t],\n\t},\n\t\"luxury-real-estate-2024\": {\n\t\ttitle: \"Extraordinary Properties of 2024\",\n\t\tcoverImg: \"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop\",\n\t\taltName: \"Luxury Real Estate 2024\",\n\t\ttimestamp: \"2024-02-01T12:00:00Z\",\n\t\tslides: [\n\t\t\t{\n\t\t\t\ttitle: \"Penthouse in Manhattan\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>A sky-high sanctuary offering breathtaking views of the New York City skyline.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Manhattan Penthouse\",\n\t\t\t\tcontributor: [\"Sotheby's Realty\"],\n\t\t\t\ttimestamp: \"2024-02-01T12:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Villa in French Riviera\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>Mediterranean elegance meets modern luxury in this stunning coastal estate.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"French Riviera Villa\",\n\t\t\t\tcontributor: [\"Christie's Real Estate\"],\n\t\t\t\ttimestamp: \"2024-02-01T12:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Estate in Aspen\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>A mountain retreat offering world-class skiing and year-round luxury amenities.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Aspen Estate\",\n\t\t\t\tcontributor: [\"Douglas Elliman\"],\n\t\t\t\ttimestamp: \"2024-02-01T12:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Mansion in Beverly Hills\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>Hollywood glamour and contemporary design converge in this iconic estate.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Beverly Hills Mansion\",\n\t\t\t\tcontributor: [\"The Agency\"],\n\t\t\t\ttimestamp: \"2024-02-01T12:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Castle in Scotland\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>Historic grandeur preserved with modern amenities in the Scottish Highlands.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Scottish Castle\",\n\t\t\t\tcontributor: [\"Savills\"],\n\t\t\t\ttimestamp: \"2024-02-01T12:00:00Z\",\n\t\t\t},\n\t\t],\n\t},\n\t\"luxury-jets-2024\": {\n\t\ttitle: \"Private Jets Redefining Luxury\",\n\t\tcoverImg: \"https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop\",\n\t\taltName: \"Luxury Private Jets 2024\",\n\t\ttimestamp: \"2024-02-05T18:00:00Z\",\n\t\tslides: [\n\t\t\t{\n\t\t\t\ttitle: \"Gulfstream G700\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The flagship of business aviation, offering unmatched range and luxury.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Gulfstream G700\",\n\t\t\t\tcontributor: [\"Gulfstream Aerospace\"],\n\t\t\t\ttimestamp: \"2024-02-05T18:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Bombardier Global 7500\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>The world's largest and longest-range business jet with four living spaces.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Bombardier Global 7500\",\n\t\t\t\tcontributor: [\"Bombardier\"],\n\t\t\t\ttimestamp: \"2024-02-05T18:00:00Z\",\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: \"Dassault Falcon 10X\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"<p>French engineering excellence with the tallest and widest cabin in business aviation.</p>\",\n\t\t\t\timage: \"https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop\",\n\t\t\t\taltName: \"Dassault Falcon 10X\",\n\t\t\t\tcontributor: [\"Dassault Aviation\"],\n\t\t\t\ttimestamp: \"2024-02-05T18:00:00Z\",\n\t\t\t},\n\t\t],\n\t},\n};\n\nconst WebStoryDetail = ({ data, nextData, breadcrumbs, meta, pathname }) => {\n\t// AMP-compliant CSS without i-amphtml- prefixes\n\n\treturn (\n\t\t<>\n\t\t\t<style jsx>{webStoryDetailCSS}</style>\n\t\t\t<Head>\n\t\t\t\t<meta name=\"amp-to-amp-navigation\" content=\"AMP-Redirect\" />\n\t\t\t\t<script\n\t\t\t\t\tasync\n\t\t\t\t\tcustom-element=\"amp-story\"\n\t\t\t\t\tsrc=\"https://cdn.ampproject.org/v0/amp-story-1.0.js\"\n\t\t\t\t></script>\n\t\t\t\t<script\n\t\t\t\t\tasync\n\t\t\t\t\tcustom-element=\"amp-story-auto-ads\"\n\t\t\t\t\tsrc=\"https://cdn.ampproject.org/v0/amp-story-auto-ads-0.1.js\"\n\t\t\t\t></script>\n\t\t\t\t{/* <script\n\t\t\t\t\tasync\n\t\t\t\t\tcustom-element=\"amp-analytics\"\n\t\t\t\t\tsrc=\"https://cdn.ampproject.org/v0/amp-analytics-0.1.js\"\n\t\t\t\t></script> */}\n\t\t\t\t<SeoHeader meta={meta} pathname={pathname} />\n\t\t\t\t<BreadcrumbSchema itemList={breadcrumbs} />\n\t\t\t\t<ImageGallerySchema\n\t\t\t\t\ttitle={data?.slides?.[0]?.title || \"\"}\n\t\t\t\t\tdescription={data?.slides?.[0]?.description || \"\"}\n\t\t\t\t\turl={Const.ClientLink + pathname}\n\t\t\t\t\tdatePublished={data?.timestamp || \"\"}\n\t\t\t\t\tdata={data?.slides || []}\n\t\t\t\t/>\n\t\t\t\t<MediaGallerySchema\n\t\t\t\t\ttitle={data?.slides?.[0]?.title || \"\"}\n\t\t\t\t\tdescription={data?.slides?.[0]?.description || \"\"}\n\t\t\t\t\tdata={data?.slides || []}\n\t\t\t\t/>\n\t\t\t</Head>\n\n\t\t\t<amp-story\n\t\t\t\tstandalone=\"\"\n\t\t\t\ttitle={data?.title || \"\"}\n\t\t\t\tpublisher=\"Robb Report India\"\n\t\t\t\tpublisher-logo-src=\"/RR final logo.png\"\n\t\t\t\tposter-portrait-src={data?.coverImg || \"\"}\n\t\t\t>\n\t\t\t\t{data?.slides?.map((slide, index) => {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<React.Fragment key={index}>\n\t\t\t\t\t\t\t<amp-story-page id={`page-${index}`}>\n\t\t\t\t\t\t\t\t<amp-story-grid-layer template=\"fill\">\n\t\t\t\t\t\t\t\t\t<amp-img\n\t\t\t\t\t\t\t\t\t\tsrc={slide?.image || \"\"}\n\t\t\t\t\t\t\t\t\t\twidth=\"720\"\n\t\t\t\t\t\t\t\t\t\theight=\"1280\"\n\t\t\t\t\t\t\t\t\t\tlayout=\"fill\"\n\t\t\t\t\t\t\t\t\t\talt={slide?.altName || \"\"}\n\t\t\t\t\t\t\t\t\t></amp-img>\n\t\t\t\t\t\t\t\t</amp-story-grid-layer>\n\n\t\t\t\t\t\t\t\t{index === 0 && (\n\t\t\t\t\t\t\t\t\t<amp-story-grid-layer template=\"vertical\">\n\t\t\t\t\t\t\t\t\t\t<div className=\"brand-logo\">\n\t\t\t\t\t\t\t\t\t\t\t<amp-img\n\t\t\t\t\t\t\t\t\t\t\t\tsrc=\"/RR final logo.png\"\n\t\t\t\t\t\t\t\t\t\t\t\twidth=\"200\"\n\t\t\t\t\t\t\t\t\t\t\t\theight=\"80\"\n\t\t\t\t\t\t\t\t\t\t\t\tlayout=\"fixed\"\n\t\t\t\t\t\t\t\t\t\t\t\talt=\"Robb Report India Logo\"\n\t\t\t\t\t\t\t\t\t\t\t></amp-img>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</amp-story-grid-layer>\n\t\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t\t<amp-story-grid-layer template=\"vertical\" className=\"story-content\">\n\t\t\t\t\t\t\t\t\t<div className=\"story-text\">\n\t\t\t\t\t\t\t\t\t\t<h1>{slide?.title || \"\"}</h1>\n\t\t\t\t\t\t\t\t\t\t{slide?.description && (\n\t\t\t\t\t\t\t\t\t\t\t<div dangerouslySetInnerHTML={{ __html: slide.description }} />\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t{slide?.contributor?.length > 0 && (\n\t\t\t\t\t\t\t\t\t\t\t<small>Photo Credit: {slide.contributor.join(\", \")}</small>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t{index === 0 && slide?.timestamp && (\n\t\t\t\t\t\t\t\t\t\t\t<small>Published: {dateFormateWithTimeShort(slide.timestamp)}</small>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</amp-story-grid-layer>\n\t\t\t\t\t\t\t</amp-story-page>\n\t\t\t\t\t\t</React.Fragment>\n\t\t\t\t\t);\n\t\t\t\t})}\n\n\t\t\t\t{/* Next Story Preview Page */}\n\t\t\t\t{nextData?.slug && nextData?.coverImg && (\n\t\t\t\t\t<amp-story-page id=\"next-story-preview\">\n\t\t\t\t\t\t<amp-story-grid-layer template=\"fill\">\n\t\t\t\t\t\t\t<amp-img\n\t\t\t\t\t\t\t\tsrc={nextData.coverImg}\n\t\t\t\t\t\t\t\twidth=\"720\"\n\t\t\t\t\t\t\t\theight=\"1280\"\n\t\t\t\t\t\t\t\tlayout=\"fill\"\n\t\t\t\t\t\t\t\talt={nextData.altName || \"Next Story\"}\n\t\t\t\t\t\t\t></amp-img>\n\t\t\t\t\t\t</amp-story-grid-layer>\n\n\t\t\t\t\t\t<amp-story-grid-layer template=\"vertical\" className=\"next-story-overlay story-content\">\n\t\t\t\t\t\t\t<div className=\"next-story-preview\">\n\t\t\t\t\t\t\t\t{/* <div className=\"preview-label\">Next Story</div> */}\n\t\t\t\t\t\t\t\t<h2>{nextData.title}</h2>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</amp-story-grid-layer>\n\n\t\t\t\t\t\t<amp-story-cta-layer>\n\t\t\t\t\t\t\t<a href={nextData.slug} className=\"next-story-btn\">\n\t\t\t\t\t\t\t\tRead Next Story\n\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t</amp-story-cta-layer>\n\t\t\t\t\t</amp-story-page>\n\t\t\t\t)}\n\n\t\t\t\t<amp-story-auto-ads>\n\t\t\t\t\t<script\n\t\t\t\t\t\ttype=\"application/json\"\n\t\t\t\t\t\tdangerouslySetInnerHTML={{\n\t\t\t\t\t\t\t__html: JSON.stringify({\n\t\t\t\t\t\t\t\t\"ad-attributes\": {\n\t\t\t\t\t\t\t\t\ttype: \"doubleclick\",\n\t\t\t\t\t\t\t\t\t\"data-slot\": \"/23290324739/RobbReport-AMP-Stories\",\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t}),\n\t\t\t\t\t\t}}\n\t\t\t\t\t/>\n\t\t\t\t</amp-story-auto-ads>\n\n\t\t\t\t{/* TODO: Uncomment when Google Analytics is configured\n\t\t\t\t<amp-analytics type=\"gtag\" data-credentials=\"include\">\n\t\t\t\t\t<script\n\t\t\t\t\t\ttype=\"application/json\"\n\t\t\t\t\t\tdangerouslySetInnerHTML={{\n\t\t\t\t\t\t\t__html: JSON.stringify({\n\t\t\t\t\t\t\t\tvars: {\n\t\t\t\t\t\t\t\t\tgtag_id: \"G-YOUR_GA_ID\",\n\t\t\t\t\t\t\t\t\tconfig: {\n\t\t\t\t\t\t\t\t\t\t\"G-YOUR_GA_ID\": {\n\t\t\t\t\t\t\t\t\t\t\tpage_title: data?.title || \"\",\n\t\t\t\t\t\t\t\t\t\t\tpage_location: Const.ClientLink + pathname\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\ttriggers: {\n\t\t\t\t\t\t\t\t\tstoryPageView: {\n\t\t\t\t\t\t\t\t\t\ton: \"story-page-visible\",\n\t\t\t\t\t\t\t\t\t\trequest: \"event\",\n\t\t\t\t\t\t\t\t\t\tvars: {\n\t\t\t\t\t\t\t\t\t\t\tevent_name: \"story_page_view\",\n\t\t\t\t\t\t\t\t\t\t\tstory_page_id: \"${storyPageId}\",\n\t\t\t\t\t\t\t\t\t\t\tstory_page_index: \"${storyPageIndex}\"\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tstoryComplete: {\n\t\t\t\t\t\t\t\t\t\ton: \"story-last-page-visible\",\n\t\t\t\t\t\t\t\t\t\trequest: \"event\",\n\t\t\t\t\t\t\t\t\t\tvars: {\n\t\t\t\t\t\t\t\t\t\t\tevent_name: \"story_complete\"\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}}\n\t\t\t\t\t/>\n\t\t\t\t</amp-analytics> */}\n\t\t\t</amp-story>\n\t\t</>\n\t);\n};\n\nexport default WebStoryDetail;\nWebStoryDetail.config = { amp: true };\n\nexport async function getServerSideProps(context) {\n\tconst { slug } = context.params;\n\n\t// TODO: Replace with actual API call when ready\n\t// const url = `/${slug}`;\n\t// try {\n\t//   const storiesRes = await getWebStories(url);\n\t//   if (!storiesRes || Object.keys(storiesRes.data).length === 0) {\n\t//     return { notFound: true };\n\t//   }\n\t//   const storyData = storiesRes.data.current.data;\n\t//   // Add cover slide as first slide\n\t//   const newObject = {\n\t//     title: storyData.title,\n\t//     description: \"\",\n\t//     image: storyData.coverImg,\n\t//     altName: storyData.altName,\n\t//     sequence: -1,\n\t//     contributor: [],\n\t//     timestamp: storyData.timestamp,\n\t//   };\n\t//   if (Array.isArray(storyData.slides)) {\n\t//     storyData.slides.unshift(newObject);\n\t//   }\n\t//   return {\n\t//     props: {\n\t//       data: storyData ?? {},\n\t//       previousData: storiesRes.data.previous ?? {},\n\t//       nextData: storiesRes.data.next ?? {},\n\t//       breadcrumbs: storiesRes.data.current.breadcrumbs ?? [],\n\t//       tag: storiesRes.data.current.tag ?? [],\n\t//       meta: storiesRes.data.current.meta ?? {},\n\t//       pathname: context.resolvedUrl || context.req.url || \"\",\n\t//     },\n\t//   };\n\t// } catch (error) {\n\t//   console.error(\"Error fetching data:\", error.message);\n\t//   return { notFound: true };\n\t// }\n\n\t// DUMMY DATA IMPLEMENTATION - Remove when API is ready\n\ttry {\n\t\tconst storyData = DUMMY_WEB_STORIES[slug];\n\n\t\tif (!storyData) {\n\t\t\treturn {\n\t\t\t\tnotFound: true,\n\t\t\t};\n\t\t}\n\n\t\t// Add cover slide as first slide\n\t\tconst newObject = {\n\t\t\ttitle: storyData.title,\n\t\t\tdescription: \"\",\n\t\t\timage: storyData.coverImg,\n\t\t\taltName: storyData.altName,\n\t\t\tsequence: -1,\n\t\t\tcontributor: [],\n\t\t\ttimestamp: storyData.timestamp,\n\t\t};\n\n\t\tconst slidesWithCover = [newObject, ...storyData.slides];\n\n\t\t// Get next story for preview (simple logic for demo)\n\t\tconst storyKeys = Object.keys(DUMMY_WEB_STORIES);\n\t\tconst currentIndex = storyKeys.indexOf(slug);\n\t\tconst nextIndex = (currentIndex + 1) % storyKeys.length;\n\t\tconst nextStoryKey = storyKeys[nextIndex];\n\t\tconst nextStory = DUMMY_WEB_STORIES[nextStoryKey];\n\n\t\treturn {\n\t\t\tprops: {\n\t\t\t\tdata: {\n\t\t\t\t\t...storyData,\n\t\t\t\t\tslides: slidesWithCover,\n\t\t\t\t},\n\t\t\t\tnextData: {\n\t\t\t\t\ttitle: nextStory.title,\n\t\t\t\t\tslug: `/webstories/all/${nextStoryKey}`,\n\t\t\t\t\tcoverImg: nextStory.coverImg,\n\t\t\t\t\taltName: nextStory.altName,\n\t\t\t\t},\n\t\t\t\tbreadcrumbs: [\n\t\t\t\t\t{ name: \"Web Stories\", slug: \"/webstories\" },\n\t\t\t\t\t{ name: \"All\", slug: \"/webstories/all\" },\n\t\t\t\t],\n\t\t\t\ttag: [\"luxury\", \"lifestyle\"],\n\t\t\t\tmeta: {\n\t\t\t\t\ttitle: storyData.title + \" | Robb Report India\",\n\t\t\t\t\tdescription: storyData.slides[0]?.description?.replace(/<[^>]*>/g, \"\") || storyData.title,\n\t\t\t\t\tkeywords: [\"luxury\", \"lifestyle\", \"robb report\"],\n\t\t\t\t\trobots: \"index,follow\",\n\t\t\t\t},\n\t\t\t\tpathname: context.resolvedUrl || context.req.url || \"\",\n\t\t\t},\n\t\t};\n\t} catch (error) {\n\t\tconsole.error(\"Error with dummy data:\", error.message);\n\t\treturn {\n\t\t\tnotFound: true,\n\t\t};\n\t}\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA,kGAAkG;AAClG;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAEO,MAAM,SAAS;IAAE,KAAK;AAAK;AAElC,8CAA8C;AAC9C,MAAM,oBAAoB;IACzB,oBAAoB;QACnB,OAAO;QACP,UAAU;QACV,SAAS;QACT,WAAW;QACX,QAAQ;YACP;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAgB;gBAC9B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAoB;gBAClC,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAoB;gBAClC,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAiB;gBAC/B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAqB;gBACnC,WAAW;YACZ;SACA;IACF;IACA,uBAAuB;QACtB,OAAO;QACP,UAAU;QACV,SAAS;QACT,WAAW;QACX,QAAQ;YACP;gBACC,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAiB;gBAC/B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAW;gBACzB,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAkB;gBAChC,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAsB;gBACpC,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAgB;gBAC9B,WAAW;YACZ;SACA;IACF;IACA,sBAAsB;QACrB,OAAO;QACP,UAAU;QACV,SAAS;QACT,WAAW;QACX,QAAQ;YACP;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAiB;gBAC/B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAa;gBAC3B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAiB;gBAC/B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAY;gBAC1B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAiB;gBAC/B,WAAW;YACZ;SACA;IACF;IACA,2BAA2B;QAC1B,OAAO;QACP,UAAU;QACV,SAAS;QACT,WAAW;QACX,QAAQ;YACP;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAmB;gBACjC,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAyB;gBACvC,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAkB;gBAChC,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAa;gBAC3B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAU;gBACxB,WAAW;YACZ;SACA;IACF;IACA,oBAAoB;QACnB,OAAO;QACP,UAAU;QACV,SAAS;QACT,WAAW;QACX,QAAQ;YACP;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAuB;gBACrC,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAa;gBAC3B,WAAW;YACZ;YACA;gBACC,OAAO;gBACP,aACC;gBACD,OAAO;gBACP,SAAS;gBACT,aAAa;oBAAC;iBAAoB;gBAClC,WAAW;YACZ;SACA;IACF;AACD;AAEA,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE;IACtE,gDAAgD;IAEhD,qBACC;;;oBACa,2HAAA,CAAA,oBAAiB;0BAAjB,2HAAA,CAAA,oBAAiB;;0BAC7B,qKAAC,6HAAA,CAAA,UAAI;;kCACJ,qKAAC;wBAAK,MAAK;wBAAwB,SAAQ;0CAFhC,2HAAA,CAAA,oBAAiB;;;;;;kCAG5B,qKAAC;wBACA,KAAK;wBACL,kBAAe;wBACf,KAAI;0CANM,2HAAA,CAAA,oBAAiB;;;;;;kCAQ5B,qKAAC;wBACA,KAAK;wBACL,kBAAe;wBACf,KAAI;0CAXM,2HAAA,CAAA,oBAAiB;;;;;;kCAkB5B,qKAAC,+HAAA,CAAA,UAAS;wBAAC,MAAM;wBAAM,UAAU;;;;;;kCACjC,qKAAC,sIAAA,CAAA,UAAgB;wBAAC,UAAU;;;;;;kCAC5B,qKAAC,wIAAA,CAAA,UAAkB;wBAClB,OAAO,MAAM,QAAQ,CAAC,EAAE,EAAE,SAAS;wBACnC,aAAa,MAAM,QAAQ,CAAC,EAAE,EAAE,eAAe;wBAC/C,KAAK,mHAAA,CAAA,QAAK,CAAC,UAAU,GAAG;wBACxB,eAAe,MAAM,aAAa;wBAClC,MAAM,MAAM,UAAU,EAAE;;;;;;kCAEzB,qKAAC,wIAAA,CAAA,UAAkB;wBAClB,OAAO,MAAM,QAAQ,CAAC,EAAE,EAAE,SAAS;wBACnC,aAAa,MAAM,QAAQ,CAAC,EAAE,EAAE,eAAe;wBAC/C,MAAM,MAAM,UAAU,EAAE;;;;;;;;;;;;0BAI1B,qKAAC;gBACA,YAAW;gBACX,OAAO,MAAM,SAAS;gBACtB,WAAU;gBACV,sBAAmB;gBACnB,uBAAqB,MAAM,YAAY;kCAvC5B,2HAAA,CAAA,oBAAiB;;oBAyC3B,MAAM,QAAQ,IAAI,CAAC,OAAO;wBAC1B,qBACC,qKAAC,mGAAA,CAAA,UAAK,CAAC,QAAQ;sCACd,cAAA,qKAAC;gCAAe,IAAI,CAAC,KAAK,EAAE,OAAO;kDA5C3B,2HAAA,CAAA,oBAAiB;;kDA6CxB,qKAAC;wCAAqB,UAAS;0DA7CxB,2HAAA,CAAA,oBAAiB;kDA8CvB,cAAA,qKAAC;4CACA,KAAK,OAAO,SAAS;4CACrB,OAAM;4CACN,QAAO;4CACP,QAAO;4CACP,KAAK,OAAO,WAAW;8DAnDlB,2HAAA,CAAA,oBAAiB;;;;;;;;;;;oCAuDvB,UAAU,mBACV,qKAAC;wCAAqB,UAAS;0DAxDzB,2HAAA,CAAA,oBAAiB;kDAyDtB,cAAA,qKAAC;8DAzDI,2HAAA,CAAA,oBAAiB,kBAyDP;sDACd,cAAA,qKAAC;gDACA,KAAI;gDACJ,OAAM;gDACN,QAAO;gDACP,QAAO;gDACP,KAAI;kEA/DD,2HAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;kDAqExB,qKAAC;wCAAqB,UAAS;0DArExB,2HAAA,CAAA,oBAAiB,kBAqE4B;kDACnD,cAAA,qKAAC;8DAtEK,2HAAA,CAAA,oBAAiB,kBAsER;;8DACd,qKAAC;sEAvEI,2HAAA,CAAA,oBAAiB;8DAuEjB,OAAO,SAAS;;;;;;gDACpB,OAAO,6BACP,qKAAC;oDAAI,yBAAyB;wDAAE,QAAQ,MAAM,WAAW;oDAAC;sEAzEtD,2HAAA,CAAA,oBAAiB;;;;;;gDA2ErB,OAAO,aAAa,SAAS,mBAC7B,qKAAC;sEA5EG,2HAAA,CAAA,oBAAiB;;wDA4Ed;wDAAe,MAAM,WAAW,CAAC,IAAI,CAAC;;;;;;;gDAE7C,UAAU,KAAK,OAAO,2BACtB,qKAAC;sEA/EG,2HAAA,CAAA,oBAAiB;;wDA+Ed;wDAAY,CAAA,GAAA,8GAAA,CAAA,2BAAwB,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;;2BApC3C;;;;;oBA2CvB;oBAGC,UAAU,QAAQ,UAAU,0BAC5B,qKAAC;wBAAe,IAAG;0CA1FT,2HAAA,CAAA,oBAAiB;;0CA2F1B,qKAAC;gCAAqB,UAAS;kDA3FtB,2HAAA,CAAA,oBAAiB;0CA4FzB,cAAA,qKAAC;oCACA,KAAK,SAAS,QAAQ;oCACtB,OAAM;oCACN,QAAO;oCACP,QAAO;oCACP,KAAK,SAAS,OAAO,IAAI;sDAjGlB,2HAAA,CAAA,oBAAiB;;;;;;;;;;;0CAqG1B,qKAAC;gCAAqB,UAAS;kDArGtB,2HAAA,CAAA,oBAAiB,kBAqG0B;0CACnD,cAAA,qKAAC;sDAtGO,2HAAA,CAAA,oBAAiB,kBAsGV;8CAEd,cAAA,qKAAC;0DAxGM,2HAAA,CAAA,oBAAiB;kDAwGnB,SAAS,KAAK;;;;;;;;;;;;;;;;0CAIrB,qKAAC;kDA5GQ,2HAAA,CAAA,oBAAiB;0CA6GzB,cAAA,qKAAC;oCAAE,MAAM,SAAS,IAAI;sDA7Gd,2HAAA,CAAA,oBAAiB,kBA6GS;8CAAiB;;;;;;;;;;;;;;;;;kCAOtD,qKAAC;0CApHU,2HAAA,CAAA,oBAAiB;kCAqH3B,cAAA,qKAAC;4BACA,MAAK;4BACL,yBAAyB;gCACxB,QAAQ,KAAK,SAAS,CAAC;oCACtB,iBAAiB;wCAChB,MAAM;wCACN,aAAa;oCACd;gCACD;4BACD;8CA9HS,2HAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;AA0KhC;uCAEe;AACf,eAAe,MAAM,GAAG;IAAE,KAAK;AAAK;AAE7B,eAAe,mBAAmB,OAAO;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,MAAM;IAE/B,gDAAgD;IAChD,0BAA0B;IAC1B,QAAQ;IACR,iDAAiD;IACjD,oEAAoE;IACpE,iCAAiC;IACjC,MAAM;IACN,oDAAoD;IACpD,sCAAsC;IACtC,wBAAwB;IACxB,8BAA8B;IAC9B,uBAAuB;IACvB,iCAAiC;IACjC,kCAAkC;IAClC,oBAAoB;IACpB,uBAAuB;IACvB,sCAAsC;IACtC,OAAO;IACP,2CAA2C;IAC3C,2CAA2C;IAC3C,MAAM;IACN,aAAa;IACb,eAAe;IACf,+BAA+B;IAC/B,sDAAsD;IACtD,8CAA8C;IAC9C,gEAAgE;IAChE,gDAAgD;IAChD,kDAAkD;IAClD,gEAAgE;IAChE,SAAS;IACT,OAAO;IACP,oBAAoB;IACpB,0DAA0D;IAC1D,+BAA+B;IAC/B,IAAI;IAEJ,uDAAuD;IACvD,IAAI;QACH,MAAM,YAAY,iBAAiB,CAAC,KAAK;QAEzC,IAAI,CAAC,WAAW;YACf,OAAO;gBACN,UAAU;YACX;QACD;QAEA,iCAAiC;QACjC,MAAM,YAAY;YACjB,OAAO,UAAU,KAAK;YACtB,aAAa;YACb,OAAO,UAAU,QAAQ;YACzB,SAAS,UAAU,OAAO;YAC1B,UAAU,CAAC;YACX,aAAa,EAAE;YACf,WAAW,UAAU,SAAS;QAC/B;QAEA,MAAM,kBAAkB;YAAC;eAAc,UAAU,MAAM;SAAC;QAExD,qDAAqD;QACrD,MAAM,YAAY,OAAO,IAAI,CAAC;QAC9B,MAAM,eAAe,UAAU,OAAO,CAAC;QACvC,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,UAAU,MAAM;QACvD,MAAM,eAAe,SAAS,CAAC,UAAU;QACzC,MAAM,YAAY,iBAAiB,CAAC,aAAa;QAEjD,OAAO;YACN,OAAO;gBACN,MAAM;oBACL,GAAG,SAAS;oBACZ,QAAQ;gBACT;gBACA,UAAU;oBACT,OAAO,UAAU,KAAK;oBACtB,MAAM,CAAC,gBAAgB,EAAE,cAAc;oBACvC,UAAU,UAAU,QAAQ;oBAC5B,SAAS,UAAU,OAAO;gBAC3B;gBACA,aAAa;oBACZ;wBAAE,MAAM;wBAAe,MAAM;oBAAc;oBAC3C;wBAAE,MAAM;wBAAO,MAAM;oBAAkB;iBACvC;gBACD,KAAK;oBAAC;oBAAU;iBAAY;gBAC5B,MAAM;oBACL,OAAO,UAAU,KAAK,GAAG;oBACzB,aAAa,UAAU,MAAM,CAAC,EAAE,EAAE,aAAa,QAAQ,YAAY,OAAO,UAAU,KAAK;oBACzF,UAAU;wBAAC;wBAAU;wBAAa;qBAAc;oBAChD,QAAQ;gBACT;gBACA,UAAU,QAAQ,WAAW,IAAI,QAAQ,GAAG,CAAC,GAAG,IAAI;YACrD;QACD;IACD,EAAE,OAAO,OAAO;QACf,QAAQ,KAAK,CAAC,0BAA0B,MAAM,OAAO;QACrD,OAAO;YACN,UAAU;QACX;IACD;AACD", "debugId": null}}, {"offset": {"line": 4208, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}