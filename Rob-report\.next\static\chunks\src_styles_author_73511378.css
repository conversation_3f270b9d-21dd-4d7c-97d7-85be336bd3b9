/* [project]/src/styles/author.css [client] (css) */
#author_header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0 40px;
}

#profile_author {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
}

#profile_author img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

#author_header h1 {
  line-height: 6vw;
  color: var(--text-color);
}

#author_header h4 {
  font-weight: 500;
  font-size: 1.5rem;
  font-family: rocky, sans-serif;
  margin-bottom: 20px;
  margin-top: 10px;
  color: var(--text-color);
}

#author-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 20px;
}

#author_header p {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: .37px;
  font-family: Georgia, sans-serif;
  color: var(--text-color);
}

@media (width <= 768px) {
  #author_header h4 {
    margin-top: 18px;
  }

  #author_header h1 {
    margin-top: 8px;
  }
}

/*# sourceMappingURL=src_styles_author_73511378.css.map*/