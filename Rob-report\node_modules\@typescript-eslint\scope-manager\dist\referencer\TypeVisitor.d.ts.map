{"version": 3, "file": "TypeVisitor.d.ts", "sourceRoot": "", "sources": ["../../src/referencer/TypeVisitor.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAKzD,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAI/C,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEpC,qBAAa,WAAY,SAAQ,OAAO;;gBAG1B,UAAU,EAAE,UAAU;IAKlC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI;IAS/D,SAAS,CAAC,iBAAiB,CACzB,IAAI,EACA,QAAQ,CAAC,0BAA0B,GACnC,QAAQ,CAAC,iBAAiB,GAC1B,QAAQ,CAAC,+BAA+B,GACxC,QAAQ,CAAC,cAAc,GACvB,QAAQ,CAAC,iBAAiB,GAC7B,IAAI;IAiCP,SAAS,CAAC,gBAAgB,CACxB,IAAI,EAAE,QAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAC,mBAAmB,GAC9D,IAAI;IAYP,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI;IAIrD,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAKjE,SAAS,CAAC,0BAA0B,CAClC,IAAI,EAAE,QAAQ,CAAC,0BAA0B,GACxC,IAAI;IAIP,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI;IAanE,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI;IAInE,SAAS,CAAC,+BAA+B,CACvC,IAAI,EAAE,QAAQ,CAAC,+BAA+B,GAC7C,IAAI;IAIP,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,GAAG,IAAI;IAI7D,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,GAAG,IAAI;IAMzD,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IASjE,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI;IAwCvD,SAAS,CAAC,sBAAsB,CAC9B,IAAI,EAAE,QAAQ,CAAC,sBAAsB,GACpC,IAAI;IAmBP,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,GAAG,IAAI;IAYzD,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI;IAKnE,SAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,kBAAkB,GAAG,IAAI;IAKrE,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,mBAAmB,GAAG,IAAI;IAKvE,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,eAAe,GAAG,IAAI;IAK/D,SAAS,CAAC,sBAAsB,CAC9B,IAAI,EAAE,QAAQ,CAAC,sBAAsB,GACpC,IAAI;IAkBP,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,eAAe,GAAG,IAAI;IAS/D,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,eAAe,GAAG,IAAI;IAQ/D,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAKjE,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI;CAwBxD"}