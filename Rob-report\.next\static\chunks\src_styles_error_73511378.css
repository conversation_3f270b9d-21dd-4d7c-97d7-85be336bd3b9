/* [project]/src/styles/error.css [client] (css) */
#error_page {
  width: 100%;
  height: 88vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--body-bg-color);
}

#error_page h2 {
  font-size: 6rem;
  line-height: 1.1;
  font-family: rocky, sans-serif;
  font-weight: 300;
}

#error_page h4 {
  font-size: 4rem;
  line-height: 1.1;
  font-family: rocky, sans-serif;
  font-weight: 300;
  text-transform: capitalize;
}

#error_page p {
  width: 60%;
  text-align: center;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: .37px;
  font-family: Georgia, sans-serif;
  margin-top: 30px;
}

@media screen and (width <= 767px) {
  #error_page {
    padding: 1rem;
  }

  #error_page p {
    width: 100%;
  }
}

@media screen and (width <= 960px) and (width >= 767px) {
  #error_page p {
    width: 85%;
  }
}

@media screen and (width <= 1300px) and (width >= 960px) {
  #error_page p {
    width: 75%;
  }
}

/*# sourceMappingURL=src_styles_error_73511378.css.map*/