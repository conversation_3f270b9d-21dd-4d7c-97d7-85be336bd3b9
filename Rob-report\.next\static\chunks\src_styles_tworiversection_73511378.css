/* [project]/src/styles/tworiversection.css [client] (css) */
.two-rivers-wrapper {
  font-size: 1.15rem;
}

.two-rivers-wrapper .sectioner {
  margin-bottom: 0;
}

.sectioner--latest-stories {
  width: 100%;
  padding: 75px 0;
  background-color: var(--body-bg-color);
}

.sectioner--latest-stories .entry {
  margin-left: 10px;
}

.latest-story.latest-story--primary .entry {
  margin-left: 0;
}

.sectioner--latest-stories .entry__category {
  font-size: 11px;
  line-height: 15px;
}

.latest-story--primary {
  flex-direction: column;
}

.sectioner--latest-stories .latest-story {
  margin-bottom: 1.875rem;
  display: flex;
  text-align: left;
  border-bottom: .0625rem solid #dddee4;
  padding-bottom: 1.25rem;
}

.sectioner--latest-stories .latest-story:last-of-type {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.sectioner--latest-stories .latest-story--primary {
  border-bottom: .0625rem solid #dddee4;
  padding-bottom: 1.25rem;
  text-align: center;
}

.sectioner--latest-stories .latest-story--primary .entry__excerpt {
  font-size: 19px;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 26px;
  letter-spacing: normal;
  font-weight: 400;
  margin-bottom: .75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: var(--text-color);
}

.latest-story--primary .entry {
  align-items: center;
  text-align: center;
  min-height: 160px;
}

.latest-story .entry__heading {
  font-family: rocky, sans-serif;
  font-size: 29px;
  line-height: 29px;
  letter-spacing: normal;
  color: var(--text-color);
}

.sectioner--latest-stories .latest-story--primary .entry__heading {
  font-size: 26px;
  line-height: 32px;
  letter-spacing: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: var(--text-color);
}

.sectioner--latest-stories .section-header {
  display: block;
  border-bottom: none;
  border-left: none;
  text-align: center;
  padding: 0;
  margin-bottom: 2rem;
}

.sectioner--latest-stories .section-header .section-header__heading {
  font-size: 1.875rem;
  line-height: 24px;
  letter-spacing: 1px;
  font-family: sweet-sans-pro, sans-serif;
  color: var(--text-color);
}

.latest-story:not(.latest-story--primary) .featured-image {
  display: inline-block;
  height: auto;
  max-width: 350px;
  overflow: hidden;
}

.latest-story:not(.latest-story--primary) .featured-image a {
  height: auto;
}

.latest-story--primary .featured-image {
  margin-bottom: .9375rem;
}

@media only screen and (width >= 41.75rem) {
  .latest-story .featured-image a {
    width: calc(50% - .625rem);
  }

  .latest-story:not(.latest-story--primary) .featured-image {
    display: inline-table;
    max-width: 350px;
    min-width: 50%;
    overflow: hidden;
  }

  .latest-story {
    display: flex;
    text-align: left;
  }

  .latest-story .entry {
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .latest-story .entry__heading {
    font-size: 23px;
  }
}

.latest-story--primary .entry, .latest-story--primary .featured-image {
  width: 100%;
}

@media only screen and (width >= 61.25rem) {
  .two-rivers-wrapper {
    display: flex;
    justify-content: space-between;
    border-top: .5px solid #d4d4d4;
  }

  .sectioner--latest-stories {
    padding: 75px 1.625rem;
  }

  .sectioner--latest-stories .latest-story--primary {
    font-size: 2.25rem;
    justify-content: flex-start;
  }

  .latest-story:not(.latest-story--primary) .featured-image {
    display: inline-table;
    max-width: 200px;
    min-width: 35%;
    overflow: hidden;
  }

  .latest-story .entry__heading {
    font-size: 20px;
  }

  .latest-story .entry__heading {
    font-size: 17px;
    line-height: 22px;
  }
}

@media only screen and (width >= 70.625rem) {
  .sectioner--latest-stories {
    padding: 75px 1.625rem;
  }

  .latest-story:not(.latest-story--primary) .featured-image {
    display: inline-table;
    max-width: 250px;
    min-width: 35%;
    overflow: hidden;
  }

  .latest-story .entry__heading {
    font-size: 22px;
    line-height: 28px;
  }

  .two-rivers-wrapper .sectioner--latest-stories .featured-image {
    position: relative;
    margin-bottom: 1.5625rem;
  }

  .two-rivers-wrapper .sectioner--latest-stories .featured-image:before {
    display: block;
    content: " ";
    width: 100%;
    padding-top: 56.25%;
  }

  .two-rivers-wrapper .sectioner--latest-stories .featured-image .image-wrapper {
    display: block;
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .two-rivers-wrapper .entry__category {
    font-size: 15px;
    line-height: 22px;
  }
}

@media only screen and (width <= 41.6875rem) {
  .sectioner--latest-stories .entry {
    display: flex;
    flex-direction: column;
  }

  .sectioner--latest-stories .entry .entry__category {
    order: 1;
    margin-bottom: 5px;
  }

  .sectioner--latest-stories .entry .entry__heading {
    order: 2;
    margin-bottom: 5px;
  }

  .sectioner--latest-stories .entry .post-meta {
    order: 3;
  }

  .sectioner--latest-stories .entry .post-meta .post-meta__author {
    font-size: 12px;
  }

  .sectioner--latest-stories .entry .post-meta .post-meta__timestamp {
    display: none;
  }

  .sectioner--latest-stories .latest-story .entry__heading {
    font-size: 17px;
    line-height: 22px;
  }

  .latest-story:not(.latest-story--primary) .featured-image {
    max-width: 400px;
    min-width: 40%;
  }

  .sectioner--latest-stories {
    padding: 25px 0;
  }

  .latest-story.latest-story--primary .entry {
    min-height: auto;
  }

  .sectioner--latest-stories .latest-story--primary .entry__excerpt {
    display: none;
  }

  .sectioner--latest-stories .section-header .section-header__heading {
    font-size: 1.375rem;
    letter-spacing: 1px;
  }
}

@media only screen and (width <= 375px) {
  .latest-story:not(.latest-story--primary) .featured-image {
    max-height: 90px;
  }
}

/*# sourceMappingURL=src_styles_tworiversection_73511378.css.map*/