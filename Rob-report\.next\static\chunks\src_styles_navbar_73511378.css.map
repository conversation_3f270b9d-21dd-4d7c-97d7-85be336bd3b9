{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/navbar.css"], "sourcesContent": [".nav {\r\n  position: relative;\r\n  padding: 2rem 3rem;\r\n  /* background-color: var(--black); */\r\n  color: var(--text-color);\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s, color 0.45s ease-in 0.5s;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 999;\r\n}\r\n.brand {\r\n  cursor: pointer;\r\n  justify-self: left;\r\n  font-size: 2vw;\r\n  font-family: rocky, sans-serif;\r\n  display: flex;\r\n  /* margin-left: 15px; */\r\n}\r\n.brand img {\r\n  /* aspect-ratio: 1; */\r\n  width: 200px;\r\n  height: auto;\r\n  filter: var(--filterblack);\r\n  transition: filter 0.45s ease-in 0.5s;\r\n\r\n  /* fill: var(--text-color); */\r\n}\r\n.nav-content {\r\n  /* display: grid; */\r\n  /* grid-template-rows: minmax(min-content, 60px); */\r\n\r\n  /* grid-template-columns: 1fr 3fr 1fr; */\r\n  align-items: center;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  height: 2.5rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 1;\r\n  overflow: hidden;\r\n}\r\n.links.nav-items {\r\n  /* display: flex;\r\n  justify-content: space-between; */\r\n  cursor: pointer;\r\n  /* font-weight: 400; */\r\n  /* font-size: 1.2rem; */\r\n  text-transform: uppercase;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in;\r\n  display: none;\r\n}\r\n.links.nav-items ul {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n.links.nav-items .menu-linksul {\r\n  display: flex;\r\n  text-transform: uppercase;\r\n  width: 100%;\r\n  flex-direction: column;\r\n  padding: 0.625rem;\r\n}\r\n.links.nav-items li:not(:last-of-type) {\r\n  margin-bottom: 0.625rem;\r\n}\r\n.links.nav-items .menu-linksul a {\r\n  font-size: 1vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 23px;\r\n  letter-spacing: 0.08rem;\r\n}\r\n\r\n.menu {\r\n  display: none;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  transition: color 0.45s ease-in;\r\n}\r\n.toggle_cntr {\r\n  position: relative;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n.toggle_cntr:hover{\r\n  scale: 0.92;\r\n}\r\n/* For the icons */\r\n.toggle_cntr svg {\r\n  transition: all 0.3s ease;\r\n  /* mix-blend-mode: difference;\r\n  color: var(--white); */\r\n}\r\n.icon {\r\n  display: none;\r\n  transition: transform 0.45s ease; /* Smooth scaling effect */\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  padding: 8px;\r\n  border: 1px solid black;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  background-color: white;\r\n  color: black;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute;\r\n}\r\n\r\n.toggle_cntr .icon:hover svg {\r\n  rotate: 25deg;\r\n}\r\n\r\n.icon.scale-0 {\r\n  transform: scale(0);\r\n}\r\n\r\n.icon.scale-1 {\r\n  transform: scale(1);\r\n}\r\n/* Ensure the menu item is positioned correctly */\r\n.menu-item {\r\n  position: relative; /* For the pseudo-element positioning */\r\n}\r\n\r\n/* Active state: when the link is active */\r\n.nav_active {\r\n  position: relative; /* Ensure relative positioning */\r\n}\r\n\r\n/* Create the pseudo-element for the border */\r\n.menu-item a::after {\r\n  content: \"\"; /* Required for the pseudo-element */\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 0%; /* Always 100% for active link */\r\n  height: 1px; /* Height of the border */\r\n  background-color: red; /* Border color */\r\n  transition: width 0.45s ease; /* Smooth transition of width */\r\n}\r\n.menu-item a.nav_active::after {\r\n  width: 100% !important;\r\n}\r\n.menu-item a:hover::after {\r\n  width: 100%;\r\n}\r\n\r\n@media only screen and (min-width: 61.25rem) {\r\n  .nav {\r\n    padding: 2rem 2rem;\r\n    border-bottom: none;\r\n  }\r\n  .links.nav-items {\r\n    display: flex;\r\n    flex-basis: 80%;\r\n  }\r\n  .links.nav-items .menu-linksul {\r\n    align-items: center;\r\n    /* justify-content: space-between; */\r\n    justify-content: center;\r\n    flex-direction: row;\r\n    padding: 0;\r\n    gap: 1.5rem;\r\n  }\r\n  .links.nav-items li:not(:last-of-type) {\r\n    margin: 0;\r\n  }\r\n  .brand img {\r\n    width: 200px;\r\n    height: auto;\r\n  }\r\n\r\n  .toggle_cntr .icon svg {\r\n    font-size: 25px;\r\n  }\r\n  .toggle_cntr .icon:nth-child(1) svg {\r\n    font-size: 30px;\r\n  }\r\n}\r\n@media only screen and (max-width: 61.1875rem) {\r\n  .nav {\r\n    padding: 1rem 1rem;\r\n  }\r\n  .menu {\r\n    display: flex;\r\n  }\r\n  .brand img {\r\n    width: 160px;\r\n    height: auto;\r\n  }\r\n  .brand {\r\n    order: 2;\r\n  }\r\n  .material-icons.menu {\r\n    font-size: 25px;\r\n    cursor: pointer;\r\n    line-height: 1;\r\n    order: 3;\r\n  }\r\n  .toggle_cntr svg {\r\n    font-size: 25px;\r\n  }\r\n  .toggle_cntr {\r\n    order: 1;\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n@media screen and (max-width: 1200px) {\r\n  .links.nav-items .menu-linksul a {\r\n    font-size: 1.2vw;\r\n  }\r\n  /* .login {\r\n    font-size: 1.25vw;\r\n  } */\r\n}\r\n@media only screen and (max-width: 768px) {\r\n  /* .nav {\r\n    padding: 1rem 1rem;\r\n  } */\r\n  /* .nav-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  } */\r\n  /* .nav-content .links {\r\n    display: none;\r\n  }\r\n  .menu {\r\n    display: initial;\r\n  } */\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;AASA;;;;;;;;;;AAaA;;;;;;;;AAWA;;;;;;AAKA;;;;;;;;AAOA;;;;AAGA;;;;;;;AAOA;;;;;;AAKA;;;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAKA;;;;;;;;;;;AAUA;;;;AAGA;;;;AAIA;EACE;;;;;EAIA;;;;;EAIA;;;;;;;;EAQA;;;;EAGA;;;;;EAKA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;AAKF;EACE;;;;;AAOF"}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}