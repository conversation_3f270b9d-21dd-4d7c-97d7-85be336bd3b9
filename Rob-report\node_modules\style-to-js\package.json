{"name": "style-to-js", "version": "1.1.16", "description": "Parses CSS inline style to JavaScript object (camelCased).", "author": "<PERSON> <<EMAIL>>", "main": "cjs/index.js", "scripts": {"build": "npm run build:cjs && npm run build:umd", "build:cjs": "tsc --declaration --outDir cjs", "build:umd": "rollup --config --failAfterWarnings", "clean": "rm -rf cjs umd", "lint": "eslint .", "lint:tsc": "tsc --noEmit", "lint:fix": "npm run lint -- --fix", "prepare": "husky", "prepublishOnly": "npm run lint && npm run lint:tsc && npm run test:ci && npm run clean && npm run build", "test": "jest", "test:ci": "CI=true jest --ci --colors --coverage", "test:watch": "jest --watch"}, "repository": {"type": "git", "url": "https://github.com/remarkablemark/style-to-js"}, "bugs": {"url": "https://github.com/remarkablemark/style-to-js/issues"}, "keywords": ["style-to-js", "css", "style", "javascript", "object", "pojo"], "dependencies": {"style-to-object": "1.0.8"}, "devDependencies": {"@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@eslint/compat": "1.2.0", "@eslint/eslintrc": "3.1.0", "@eslint/js": "9.12.0", "@rollup/plugin-commonjs": "28.0.0", "@rollup/plugin-node-resolve": "15.3.0", "@rollup/plugin-terser": "0.4.4", "@types/jest": "29.5.13", "@typescript-eslint/eslint-plugin": "8.8.0", "@typescript-eslint/parser": "8.8.0", "eslint": "9.12.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-simple-import-sort": "12.1.1", "globals": "15.10.0", "husky": "9.1.6", "jest": "29.7.0", "lint-staged": "15.2.10", "prettier": "3.3.3", "rollup": "4.24.0", "ts-jest": "29.2.5", "typescript": "5.5.4"}, "files": ["cjs/", "src/", "umd/"], "license": "MIT"}