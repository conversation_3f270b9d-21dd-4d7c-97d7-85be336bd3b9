{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/categoryPage.css"], "sourcesContent": [".categoryWrapper {\r\n  color: var(--text-color);\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n  min-height: 60vh;\r\n}\r\n.categoryWrapper .featured-category__story .entry__heading {\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n}\r\n.categoryWrapper\r\n  .sectioner--featured-category\r\n  .section-header\r\n  .section-header__heading:before {\r\n  border-top: 0.1px solid var(--body-bg-colorblackwhite);\r\n  transition: border-top 0.45s ease-in 0.5s;\r\n}\r\n.categoryWrapper\r\n  .sectioner--featured-category\r\n  .section-header\r\n  .section-header__heading\r\n  a,\r\n.categoryWrapper\r\n  .sectioner--featured-category\r\n  .section-header\r\n  .section-header__heading\r\n  span {\r\n  background-color: var(--body-bg-color);\r\n  color: var(--text-color);\r\n  transition: background-color 0.45s ease-in 0.5s, color 0.45s ease-in 0.5s;\r\n}\r\nsection.tab_section {\r\n  position: relative;\r\n  padding: 40px 0;\r\n  scroll-margin-top: 120px;\r\n  /* height: 100vh; */\r\n}\r\nsection.tab_section.noPadding{\r\n  padding: 0;\r\n}\r\n.page_head_set {\r\n  text-align: center;\r\n  padding: 1vw 0 1.5vw 0;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  font-size: 1.25vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n}\r\n.editionTitle {\r\n  font-size: 3vw;\r\n  line-height: 10vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n/* .bar_input input::-webkit-input-placeholder {\r\n  color: red;\r\n  background: transparent;\r\n} */\r\n\r\n.page_breadcrumb {\r\n  padding-bottom: 2rem;\r\n}\r\n.page_breadcrumb ol li a {\r\n  font-size: 16px;\r\n}\r\n.page_breadcrumb ol {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n  list-style: none;\r\n  font-size: 20px;\r\n  padding: 0;\r\n}\r\n.page_breadcrumb ol li::after {\r\n  content: \">\";\r\n  display: inline-block;\r\n  position: relative;\r\n  font-weight: 300;\r\n  font-size: 14px;\r\n  margin: 0 5px;\r\n}\r\n.page_breadcrumb ol li:last-child::after {\r\n  content: \"\";\r\n}\r\n.search_bar {\r\n  padding: 12px 30px;\r\n  justify-content: space-between;\r\n  background-color: var(--body-bg-colorblackwhite);\r\n  color: var(--text-colorwhite);\r\n  transition: background-color 0.45s ease-in 0.5s, color 0.45s ease-in 0.5s;\r\n}\r\n/* optional for filter */\r\n.search_bar,\r\n.search_bar_side,\r\n.filters_toggle {\r\n  align-items: center;\r\n}\r\n.bar_input {\r\n  width: 100%;\r\n}\r\n.flexCntr {\r\n  display: flex;\r\n}\r\n\r\n.search_bar_side {\r\n  mix-blend-mode: exclusion;\r\n}\r\n.bar_icon {\r\n  position: relative;\r\n  padding-right: 20px;\r\n  border-right: 1px solid rgb(255 255 255 / 30%);\r\n}\r\n.bar_icon,\r\n.col_image {\r\n  margin-right: 20px;\r\n}\r\n.bar_input input {\r\n  background: transparent;\r\n  border: none;\r\n  outline: none;\r\n  appearance: none;\r\n  color: #fff;\r\n  width: 100%;\r\n  min-width: 232px;\r\n  font-size: 1rem;\r\n}\r\ninput::selection {\r\n  background: transparent;\r\n}\r\n.bar_input input:focus {\r\n  outline: none;\r\n  border: none;\r\n  box-shadow: none;\r\n  background-color: transparent !important;\r\n}\r\n\r\ninput:-webkit-autofill,\r\ninput:-webkit-autofill:hover, \r\ninput:-webkit-autofill:focus{\r\n  background-color: transparent !important;\r\n  border: 1px solid transparent;\r\n  -webkit-text-fill-color: #F1F1F1;\r\n  -webkit-box-shadow: 0 0 0px 1000px transparent inset;\r\n  transition: background-color 5000s ease-in-out 0s;\r\n}\r\n\r\n.f_lable {\r\n  font-size: 16px;\r\n  text-transform: uppercase;\r\n  font-weight: 500;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n}\r\n.filters_circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  background: #fff;\r\n  border-radius: 50%;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n.filters_circle,\r\n.tab span a {\r\n  margin-left: 20px;\r\n}\r\n.filters_circle i {\r\n  width: 4px;\r\n  height: 4px;\r\n  background: #fff;\r\n  mix-blend-mode: exclusion;\r\n  margin: 1px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.search_bar,\r\n.search_bar_side,\r\n.filters_toggle {\r\n  align-items: center;\r\n}\r\n\r\n/* fixed  */\r\n.fixed-bar {\r\n  position: sticky;\r\n  top: 6.5rem;\r\n  z-index: 99;\r\n  width: 100%;\r\n}\r\n@media only screen and (width <= 61.1875rem) {\r\n  /* fixed  */\r\n  .fixed-bar {\r\n    top: 4.5rem;\r\n  }\r\n}\r\n.fixed_item {\r\n  z-index: 599;\r\n  position: relative;\r\n\r\n  /* top: -1px; */\r\n  transition: margin 0.2s cubic-bezier(0.22, 0.61, 0.36, 1);\r\n}\r\n.page_bar {\r\n  margin: 0 42px;\r\n}\r\n\r\n.tabs_bar {\r\n  width: 100%;\r\n  /* margin: 0 auto; */\r\n  display: flex;\r\n  align-items: center;\r\n  /* position: sticky;\r\n  top: 0%; */\r\n  /* overflow: hidden; */\r\n  color: #fff;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  background: #2a2a2a;\r\n  height: 60px;\r\n  overflow-x: hidden;\r\n  /* touch-action: pan-y; */\r\n}\r\n.tabs_bar::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n.tabs_bar .tab {\r\n  width: calc(100% / 6);\r\n  height: 100%;\r\n  /* background-color: #ff000082; */\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  /* flex-wrap: wrap; */\r\n  flex-shrink: 0;\r\n  cursor: pointer;\r\n}\r\n.tabs_bar .tab:before {\r\n  position: absolute;\r\n  content: \"\";\r\n  width: 1px;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(255, 255, 255, 0.406);\r\n  z-index: 99;\r\n}\r\n.full_bg {\r\n  height: 100%;\r\n  width: 0%;\r\n  /* background-color: gray; */\r\n  background-color: #3f3f3f;\r\n  transform-origin: left center;\r\n  position: absolute;\r\n  left: 0;\r\n}\r\n.tab_content {\r\n  position: relative;\r\n  opacity: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  padding: 20px 25px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.tabs_bar .tab_content span {\r\n  text-overflow: ellipsis;\r\n  /* overflow: hidden; */\r\n  width: 100%;\r\n  text-align: center;\r\n  letter-spacing: 1px;\r\n}\r\n.tabs_bar .tab_content .f_16 {\r\n  font-size: 16px;\r\n  line-height: 20px;\r\n}\r\n.tabs_bar .tab_content .f_40 {\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.tab span:not(.flex) {\r\n  display: block;\r\n}\r\n.bar_icon svg {\r\n  max-width: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .tabs_bar {\r\n    width: 100%;\r\n  }\r\n  /* .tabs_bar .tab {\r\n    width: calc(100% / 6);\r\n  } */\r\n  .editionTitle {\r\n    font-size: 4vw;\r\n  }\r\n  .tabs_bar .tab_content span {\r\n    font-size: 13px;\r\n  }\r\n}\r\n@media screen and (max-width: 960px) {\r\n  /* .tabs_bar {\r\n    width: 100%;\r\n  }\r\n  .tabs_bar .tab {\r\n    width: calc(100% / 6);\r\n  } */\r\n}\r\n@media screen and (max-width: 800px) {\r\n  /* .tabs_bar .tab {\r\n    width: calc(100% / 4);\r\n  } */\r\n  .tab_content {\r\n    padding: 10px 0;\r\n  }\r\n  .page_bar {\r\n    margin: 0 10px;\r\n  }\r\n  .tabs_bar .tab_content span {\r\n    font-size: 13px;\r\n  }\r\n  .page_head_set {\r\n    margin: 0;\r\n    font-size: 3vw;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .tabs_bar .tab {\r\n    width: 100%;\r\n  }\r\n  .editionTitle,\r\n  .search_input input {\r\n    font-size: 6vw;\r\n    line-height: 19vw;\r\n  }\r\n  .line-height-10 {\r\n    line-height: 10vw!important;\r\n  }\r\n  section.tab_section {\r\n    padding: 2rem 0;\r\n  }\r\n  .page_head_set {\r\n    margin: 0;\r\n    font-size: 3vw;\r\n  }\r\n  .page_breadcrumb ol li a {\r\n    font-size: 12px;\r\n  }\r\n  .categoryWrapper .featured-category__story .entry__heading {\r\n    display: -webkit-box;\r\n    max-width: 100%;\r\n    -webkit-line-clamp: 3;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n  }\r\n}\r\n@media screen and (max-width: 640px) {\r\n  .search_bar {\r\n    padding: 15px;\r\n  }\r\n  .filters_toggle .f_lable {\r\n    display: none;\r\n  }\r\n  .bar_input input {\r\n    min-width: 190px;\r\n  }\r\n  .tabs_bar {\r\n    width: 100%;\r\n  }\r\n  .tabs_bar .tab {\r\n    width: 100%;\r\n  }\r\n  .tabs_bar .tab_content span {\r\n    font-size: 1rem;\r\n  }\r\n  /* .tabs_bar .tab_content {\r\n    padding: 13.5px 25px;\r\n  } */\r\n}\r\n@media screen and (max-width: 600px) {\r\n  .page_head_set {\r\n    margin: 0;\r\n    font-size: 4vw;\r\n  }\r\n\r\n  .bar_input input {\r\n    min-width: 100px;\r\n    font-size: 16px;\r\n  }\r\n  .bar_icon,\r\n  .col_image {\r\n    margin-right: 5px;\r\n    padding-right: 0;\r\n  }\r\n  .bar_icon {\r\n    border: 0;\r\n  }\r\n  .bar_icon svg {\r\n    max-width: 80%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;AAMA;;;;;AAIA;;;;;AAOA;;;;;;AAcA;;;;;;AAMA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;;;;;AAaA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;AAQA;;;;AAKA;;;;AAGA;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;AAIA;;;;;;;;;;;AAUA;;;;AAGA;;;;;;;AAOA;;;;;;;;AAUA;;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AASA;;;;AAOA;;;;;;;AAMA;EAEE;;;;;AAIF;;;;;;AAOA;;;;AAIA;;;;;;;;;;;AAeA;;;;AAGA;;;;;;;;;;;AAYA;;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;AAIA;;;;;AAKA;;;;AAGA;;;;;AAKA;EACE;;;;EAMA;;;;EAGA;;;;;AAIF;;;;AAQA;EAIE;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;AAKF;EACE;;;;EAGA;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;;;;AAQF;EACE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAOF;EACE;;;;;EAKA;;;;;EAIA;;;;;EAKA;;;;EAGA"}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}