/* [project]/src/styles/herosectionbanner.css [client] (css) */
.fullWidthCntr {
  position: relative;
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
}

.editor-picks__primary-pick {
  width: 100%;
  padding: 1.25rem;
  padding-bottom: 0;
  text-align: center;
  position: relative;
}

.editor-picks__primary-pick .post-meta {
  display: flex;
  flex-direction: row-reverse;
  align-items: flex-start;
  justify-content: center;
  padding-bottom: 35px;
}

.editor-picks__primary-pick .featured-image {
  position: relative;
  margin-bottom: 1.25rem;
  width: 100%;
  aspect-ratio: 16 / 9;
  overflow: hidden;
}

.featured-image a {
  position: relative;
  height: 100%;
  width: 100%;
  display: block;
}

.editor-picks__primary-pick .featured-image img {
  object-fit: cover;
}

.editor-picks__primary-pick .entry {
  padding: .9375rem 2.8125rem;
  padding-bottom: 0;
  width: 100%;
  text-align: center;
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
  margin-top: -50px;
  position: relative;
  z-index: 99;
  margin-left: auto;
  margin-right: auto;
}

.editor-picks__primary-pick .entry__category {
  font-size: 11px;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 15px;
}

.editor-picks__primary-pick .entry__heading {
  font-size: 23px;
  line-height: 28px;
  text-transform: none;
  margin-bottom: .625rem;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  font-family: rocky, sans-serif;
}

.editor-picks__primary-pick .entry__excerpt {
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  font-family: sweet-sans-pro, sans-serif;
  margin-bottom: 1.25rem;
  width: 100%;
  letter-spacing: .37px;
}

.editor-picks__primary-pick .entry__excerpt {
  font-size: 19px;
  font-family: sweet-sans-pro, sans-serif;
  margin-bottom: .625rem;
  font-weight: 300;
  line-height: 1.1;
}

@media only screen and (width >= 61.25rem) {
  .editor-picks__primary-pick {
    padding: 0;
    text-align: left;
  }

  .editor-picks__primary-pick .featured-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 9;
    overflow: hidden;
    margin-bottom: 0;
    transform: none;
  }

  .editor-picks__primary-pick .entry__category {
    font-size: 15px;
    line-height: 22px;
  }

  .editor-picks__primary-pick .entry__heading {
    font-size: 34px;
    line-height: 41px;
  }
}

/*# sourceMappingURL=src_styles_herosectionbanner_73511378.css.map*/