/* [project]/src/styles/stories.css [client] (css) */
#story_wrapper {
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
}

.story_hero_hero_container {
  width: 100%;
  height: 100%;
  padding: 1rem 0;
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.story-hero-section .story-photoBy {
  padding-inline: .625rem;
  margin: 5px 0 0;
}

.story_hero_text_container {
  width: 100%;
  height: 83%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.story_hero_category {
  font-size: 1rem;
  color: red;
  font-weight: 500;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 1rem;
}

.story_hero_title {
  font-size: 2rem;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  width: 95%;
  line-height: 1;
  font-family: rocky, sans-serif;
  letter-spacing: .37px;
  margin-bottom: 2rem;
}

.story_hero_description {
  font-size: 1rem;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  opacity: .7;
  width: 75%;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 1.1;
  letter-spacing: .37px;
  margin-bottom: 30px;
}

.story_hero_info_container {
  width: 100%;
  height: 17%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  padding: 0 1rem;
  font-family: sweet-sans-pro, sans-serif;
  flex-wrap: wrap;
  letter-spacing: .37px;
}

.story_hero_info {
  display: inherit;
  white-space: pre;
  font-size: 1rem;
}

@media only screen and (width <= 767px) {
  .mob-py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}

@media only screen and (width >= 768px) {
  .story_hero_hero_container {
    height: 70vh;
    padding: 0;
  }
}

@media only screen and (width >= 92.5rem) {
  .story-main-wrapper > * {
    max-width: 100rem;
  }
}

.story_hero_image_container {
  width: 100%;
  height: auto;
  aspect-ratio: 16 / 9;
  background-color: gray;
  position: relative;
}

.story-photoBy {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin: 1px 0 20px;
  line-height: 1.35;
  text-align: left;
}

.story-caption {
  font-family: sweet-sans-pro, sans-serif;
  font-size: .875rem;
  letter-spacing: .37px;
  font-weight: 500;
  line-height: 1.1;
  opacity: .8;
  text-align: left;
  color: var(--text-color) !important;
}

.story-courtesy {
  font-family: rocky, sans-serif;
  font-size: .875rem;
  letter-spacing: 1px;
  text-align: left;
  color: var(--text-color) !important;
}

.story-main-wrapper .story-bannerSec {
  display: block;
  position: relative;
  width: 100vw;
  height: auto;
  margin-bottom: 20px;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-inline: 0 !important;
}

.story-main-wrapper .story-bannerSec .story-photoBy {
  padding: 0 20px;
}

.react-tweet-theme {
  margin-bottom: 0 !important;
}

.embed-frame iframe {
  height: 100% !important;
}

.embed-frame.embed-youtube {
  aspect-ratio: 16 / 9;
}

.instaembed {
  width: 100% !important;
}

.embed-frame.embed-instagram {
  height: auto;
  aspect-ratio: 9 / 16;
  margin: 0 auto;
}

.story-listStyle {
  padding-inline-start: 25px !important;
}

.d-flex {
  display: grid;
  grid-template-columns: 1fr;
}

.d-flex > :nth-child(3) {
  grid-column: 1 / -1;
}

.story-main-container p span {
  color: var(--text-color) !important;
}

@media (width <= 500px) {
  .story_hero_description {
    width: 95%;
  }

  .story_hero_info_container {
    gap: 20px;
  }

  .story_info_tag:nth-child(2) {
    order: 3;
  }

  .story_info_tag:nth-child(3) {
    order: 2;
  }
}

@media (width >= 768px) {
  .instaembed {
  }

  .embed-frame.embed-instagram {
    height: 100vh;
    aspect-ratio: 9 / 16;
    margin: 0 auto;
  }

  .story_hero_title {
    font-size: 4rem;
    line-height: 1.1;
    font-family: rocky, sans-serif;
    width: 75%;
  }

  .story_hero_description {
    font-size: 1.5rem;
    font-family: sweet-sans-pro, sans-serif;
    margin-bottom: 0;
  }

  .story_hero_info {
    font-size: 1.25rem;
    font-family: sweet-sans-pro, sans-serif;
  }

  .story_hero_info_container {
    padding: 0 2.5rem;
  }

  .d-flex {
    grid-template-columns: 3fr 1fr;
  }
}

@media (width <= 768px) {
  .d-flex {
    display: flex;
    flex-direction: column;
  }

  .submenu-body.d-flex {
    flex-direction: column-reverse !important;
  }

  #about_wrapper .d-flex {
    flex-direction: column-reverse;
  }

  .d-flex > :last-child {
    order: 2;
  }

  .d-flex > :nth-last-child(2) {
    order: 3;
  }

  .story-main-wrapper {
    border: none;
  }
}

.story_main_classname_container {
}

.story-main-container {
}

.story-side-container {
}

.story-side-wrapper {
  padding: 0 0;
}

.story_main_classname_main {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}

.story-main-container h2 {
  font-size: 1.5rem;
  line-height: 1.1;
  font-family: rocky, sans-serif;
}

.story-main-container h2 span, .story-main-container h3 span, .story-main-container h4 span, .story-main-container h5 span, .story-main-container h6 span {
  color: var(--text-color) !important;
}

.story-main-container a, .story-main-container a > span {
  position: relative;
  color: red !important;
}

.story-main-container a:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 1px;
  transition: width .45s;
  background-color: red !important;
}

.story-main-container a:hover:after {
  width: 100%;
}

@media only screen and (width >= 768px) {
  .story_main_classname_main {
    flex-direction: row;
  }

  .story-side-wrapper {
    padding: 0 1rem;
    padding-left: 2rem;
  }

  .story-main-container h2 {
    font-size: 2rem;
    line-height: 1.1;
  }
}

.story_main_classname_left {
  width: 100%;
  padding: 20px 0;
}

@media (width >= 768px) {
  .story_main_classname_left {
    width: 72%;
    padding: 40px 40px 40px 0;
  }
}

.story-main-wrapper {
  width: 100%;
  height: 100%;
  font-size: 20px;
  text-align: justify;
  font-family: Georgia, sans-serif;
  line-height: 1.3;
  font-weight: 100;
  padding: 1rem 0;
}

.story-main-wrapper p {
  font-weight: 300;
  margin-bottom: 1.5rem;
  line-height: 26px;
  font-size: 16px;
  font-family: Georgia, sans-serif;
  color: var(--text-color);
}

@media (width >= 768px) {
  .story-main-wrapper {
    font-size: 27px;
    font-size: 1.4rem;
    line-height: 1.2;
    border-right: 1px solid var(--line-color);
    padding: 0 1rem;
    padding-right: 2rem;
  }

  .story-main-wrapper p {
    font-size: 18px;
    line-height: 28px;
    letter-spacing: .37px;
  }
}

.story_main_classname_imageWrapper {
  width: 100%;
  background: #ccc;
  position: relative;
  margin: 3rem 0;
  aspect-ratio: 16 / 9;
}

.story_main_classname_divider {
  display: none;
}

@media (width >= 768px) {
  .story_main_classname_divider {
    display: block;
    width: 1px;
    background: #a4a4a475;
  }
}

.story_main_classname_right {
  width: 100%;
  padding: 20px 0;
}

@media (width >= 768px) {
  .story_main_classname_right {
    width: 28%;
    padding: 40px 0 40px 10px;
  }
}

@media (width >= 1200px) {
  .story_main_classname_right {
    width: 28%;
    padding: 40px 0 40px 40px;
  }
}

.story_main_classname_card {
  height: 65vh;
  display: flex;
  flex-direction: column;
}

.story_main_classname_cardImage {
  width: 100%;
  object-fit: cover;
}

.story_main_classname_img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story_main_classname_cardText {
  height: 10%;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 500;
  padding: 10px;
  font-family: rocky, sans-serif;
  line-height: 1;
  color: var(--text-colorblack);
}

.story_main_classname_alsoInteresting {
  margin-bottom: 20px;
  padding: 10px;
  font-weight: 500;
  position: relative;
  text-align: center;
}

.story_main_classname_alsoInteresting span {
  background-color: var(--body-bg-color);
  padding: 0 10px;
  font-size: 1.4rem;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
  color: var(--text-color);
  position: relative;
  z-index: 2;
}

#about_wrapper .story_main_classname_alsoInteresting span {
  background-color: var(--body-bg-color);
  color: var(--text-color);
  position: relative;
  position: relative;
  z-index: 2;
}

.story_main_classname_alsoInteresting:before {
  border-top: .1px solid var(--line-color);
  content: "";
  margin: 0 auto;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: 1;
}

#about_wrapper .story_main_classname_alsoInteresting:before {
  border-top: .1px solid var(--line-color);
  z-index: 1;
}

.story_main_classname_advertisement {
  margin-top: 20px;
  height: 60vh;
  width: 100%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: sweet-sans-pro, sans-serif;
  color: gray;
}

.story_main_classname_slider {
  padding: 10px 0;
}

.relatedWrapper {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 0;
}

#related-stories-section {
  padding-top: 0;
}

@media (width >= 668px) {
  .relatedWrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .relatedWrapper > .featured-category__story {
    width: 100%;
  }
}

@media (width >= 768px) {
  #related-stories-section {
    padding-top: 40px;
  }

  .story_main_classname_slider {
    padding: 40px 0;
  }
}

@media (width >= 980px) {
  .relatedWrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  .relatedWrapper > .featured-category__story {
    width: 100%;
  }
}

@media (width >= 1200px) {
  .story_main_classname_slider {
    padding: 10px 0;
  }
}

.story_main_classname_social {
  padding: 10px 0;
  margin-top: 5px;
}

@media (width >= 768px) {
  .story_main_classname_social {
    margin-top: 5vh;
    margin-bottom: 5vh;
  }
}

@media (width >= 1200px) {
  .story_main_classname_social {
    padding: 10px 0;
  }
}

.story_main_classname_footer {
  width: 100%;
}

@media (width >= 768px) {
  .story_main_classname_footer {
    display: flex;
  }
}

.story_interesting_classname_container {
  width: 100%;
  margin-bottom: 10px;
}

.story_interesting_classname_card {
  width: 100%;
}

.story_interesting_classname_imageWrapper {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  overflow: hidden;
}

.story_interesting_classname_image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story_interesting_classname_text {
  width: 100%;
  min-height: 10vh;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  margin-top: .625rem;
}

.story_interesting_classname_heading {
  font-size: 1rem;
  color: red;
  font-weight: 500;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.story_interesting_classname_description {
  font-size: 1.4rem;
  color: var(--text-color);
  font-family: rocky, sans-serif;
  line-height: 1.2;
}

#about_wrapper .story_interesting_classname_description {
  color: var(--text-color);
  line-height: 1.2;
}

.story_swiper_classname_container {
  position: relative;
  width: 100%;
  overflow: hidden;
  margin: 0 auto;
}

.story_swiper_classname_swiper {
  width: 100%;
  aspect-ratio: 1;
}

@media (width >= 768px) {
  .story_swiper_classname_swiper {
    height: 50vh;
  }
}

.story_swiper_classname_slide {
  position: relative;
  width: 100%;
  height: 100%;
}

.story_swiper_classname_image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story_swiper_classname_controls {
  position: relative;
  width: 100%;
  height: 10vh;
  display: flex;
  align-items: center;
  padding-top: 1rem;
  font-family: sweet-sans-pro, sans-serif;
}

.story_swiper_classname_buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.story_swiper_classname_arrow {
  background: none;
  border: none;
  cursor: pointer;
}

.story_swiper_classname_icon {
  font-size: 24px;
  color: #000;
  background-color: #fff;
}

.story_swiper_classname_counter {
  font-size: 1.2rem;
  margin-left: 1rem;
  letter-spacing: 1px;
}

.story_social_container {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 0;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
}

.story_social_wrapper {
  width: 100%;
  font-family: sweet-sans-pro, sans-serif;
}

.story_social_section {
  width: 100%;
  min-height: 5vh;
  padding: 10px 0;
  border-top: 1px solid var(--line-color);
  display: flex;
  flex-direction: column;
}

@media (width >= 768px) {
  .story_social_section {
    min-height: 10vh;
    flex-direction: row;
    align-items: center;
  }
}

.story_social_label {
  width: 100%;
  min-height: 5vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-transform: uppercase;
  font-size: 12px;
}

@media (width >= 768px) {
  .story_social_label {
    width: 15%;
    font-size: 16px;
  }
}

.story_social_buttons {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

@media (width >= 768px) {
  .story_social_buttons {
    width: 85%;
  }
}

.story_social_author {
  width: 100%;
  text-transform: uppercase;
  font-size: 12px;
  line-height: 1.1;
}

@media (width >= 768px) {
  .story_social_author {
    width: 85%;
    font-size: 16px;
  }
}

.story_social_bio {
  display: flex;
  align-items: center;
  gap: 5px;
  text-transform: none;
  font-size: 1.2rem;
}

.border_btn_button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.border_btn_button svg {
  font-size: 1.2rem;
}

.border_btn {
  display: inline-block;
}

.border_btn_button {
  padding: 4px 8px;
  font-size: 12px;
  text-transform: uppercase;
  border: 1px solid var(--text-color);
  color: var(--text-color);
  background-color: #0000;
  transition: all .3s;
  cursor: pointer;
}

.border_btn_button:hover {
  background-color: var(--text-color);
  color: var(--body-bg-color);
  border-color: var(--body-bg-color);
}

@media (width >= 768px) {
  .border_btn_button {
    padding: 8px 16px;
    font-size: .875rem;
  }
}

.container_fullImage {
  max-width: 100vw;
  flex: 1;
  margin: 0 auto;
  position: relative;
}

.full-image-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.full-image-wrapper img {
  width: 100%;
  height: auto;
  object-fit: cover;
  object-position: center;
}

.share__modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #00000080;
  transform: scale(1.1);
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear .3s, opacity .3s, transform .3s;
  z-index: 9999;
}

.share-close-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  cursor: pointer;
  font-size: 23px;
  color: #000;
}

.share__modal.show {
  opacity: 1;
  pointer-events: all;
  visibility: visible;
  transform: scale(1);
  transition: visibility linear, opacity .25s, transform .25s;
}

.share__content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  width: 30rem;
  padding: 30px;
  border-radius: 10px;
  text-align: left;
  box-shadow: 0 4px 6px #0000001a;
}

.share_body {
  display: flex;
  flex-direction: column;
  gap: 30px;
  position: relative;
}

.share_body h2 {
  font-size: 2.1rem;
  color: #000;
  text-align: center;
  line-height: 1;
  font-family: rocky, sans-serif;
  font-weight: 400;
}

.share__icons {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

.share_body h3 {
  color: gray;
  font-weight: 300;
  font-size: 1rem;
  font-family: sweet-sans-pro, sans-serif;
  margin-bottom: .5rem;
}

#check-group {
  animation: .32s ease-in-out 1.03s check-group;
  transform-origin: center;
}

#check-group #check {
  animation: .34s cubic-bezier(.65, 0, 1, 1) .8s forwards check;
  stroke-dasharray: 0 75;
  stroke-linecap: round;
  stroke-linejoin: round;
}

#check-group #outline {
  animation: .38s ease-in outline;
  transform: rotate(0);
  transform-origin: center;
}

#check-group #white-circle {
  animation: .35s ease-in .35s forwards circle;
  transform: none;
  transform-origin: center;
}

.copy_text_anim {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-family: sweet-sans-pro, sans-serif;
  color: var(--text-colorblack);
}

.StoriesInfo_left_innercntr-full-width.embed-twitter {
  position: relative;
  aspect-ratio: 16 / 9;
}

.flex-all-embed {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes outline {
  from {
    stroke-dasharray: 0 345.576;
  }

  to {
    stroke-dasharray: 345.576 345.576;
  }
}

@keyframes circle {
  from {
    transform: scale(1);
  }

  to {
    transform: scale(0);
  }
}

@keyframes check {
  from {
    stroke-dasharray: 0 75;
  }

  to {
    stroke-dasharray: 75 75;
  }
}

@keyframes check-group {
  from {
    transform: scale(1);
  }

  50% {
    transform: scale(1.09);
  }

  to {
    transform: scale(1);
  }
}

.share__icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f3f3f3;
  border-radius: 50%;
  width: 58px;
  height: 58px;
  color: #333;
  transition: background .3s, color .3s;
}

.share__icon svg {
  font-size: 22px;
}

.share__icon:hover {
  background: #000;
  color: #fff;
}

.share__icon button {
  all: unset;
  cursor: pointer;
}

.link_copycntr {
  width: 100%;
  padding: 10px;
  background-color: #f3f3f3;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.link_copycntr input {
  width: 100%;
  background: none;
  outline: none;
  border: none;
  font-family: sweet-sans-pro, sans-serif;
  color: var(--text-colorblack);
}

.link_copycntr svg {
  font-size: 24px;
  cursor: pointer;
  color: var(--text-colorblack);
}

@media only screen and (width <= 992px) {
  .share-close-btn {
    top: -15px;
    right: -15px;
  }

  .card-meta_meta {
    row-gap: 16px;
  }

  .contr-fluid {
    gap: 1em;
  }

  .Stories_caption {
    font-size: .875rem;
  }

  .Stories_courtesy {
    font-size: .875rem;
  }
}

@media only screen and (width <= 767px) {
  .share__content {
    width: 85vw;
  }

  .share_body h2 {
    font-size: 2rem;
  }

  .share__icon {
    width: 40px;
    height: 40px;
  }

  .share__icon svg {
    font-size: 18px;
  }

  .share__icons {
    justify-content: space-around;
  }
}

@media only screen and (width <= 425px) {
  .share_body h2 {
    font-size: 1.5rem;
  }
}

/*# sourceMappingURL=src_styles_stories_73511378.css.map*/