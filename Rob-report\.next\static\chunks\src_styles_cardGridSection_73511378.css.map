{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/cardGridSection.css"], "sourcesContent": ["/* .sectioner {\r\n  padding-bottom: 2.1875rem;\r\n} */\r\n.category_separate .sectioner{\r\n  padding-bottom: 0;\r\n}\r\n.containerWrapper.category_separate{\r\n  padding-bottom: 0;\r\n}\r\n.grid__Section_pt {\r\n  padding-top: 50px;\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .sectioner--featured-category {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n.sectioner--the-latest-posts {\r\n  /* padding: 1.5625rem; */\r\n  background-color: var(--background-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n}\r\n.module__heading,\r\n.section-header {\r\n  border-bottom: 1px solid #979797;\r\n  border-left: 1px solid #979797;\r\n  color: #000;\r\n  margin-bottom: 0.9375rem;\r\n  padding: 0 0 0.3125rem 0.625rem;\r\n  text-transform: uppercase;\r\n}\r\n.section-header {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.sectioner--featured-category .section-header {\r\n  display: block;\r\n  border-bottom: none;\r\n  border-left: none;\r\n  text-align: center;\r\n  padding: 0;\r\n  margin-bottom: 50px;\r\n  /* margin-top: 50px; */\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.section-header__heading {\r\n  /* font: 400 1rem / 1, sweet-sans-pro, sans-serif; */\r\n  letter-spacing: 1px;\r\n  font-weight: 600;\r\n}\r\n.sectioner--featured-category .section-header .section-header__heading {\r\n  position: relative;\r\n  z-index: 1;\r\n  font-size: var(--desktop-font-heading--d-h2);\r\n}\r\n.sectioner--featured-category .section-header .section-header__heading:before {\r\n  border-top: 0.1px solid var(--text-color);\r\n  content: \"\";\r\n  margin: 0 auto;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  z-index: -1;\r\n}\r\n.sectioner--featured-category .section-header .section-header__heading a,\r\n.sectioner--featured-category .section-header .section-header__heading span {\r\n    background-color: var(--body-bg-color);\r\n  padding: 0 10px;\r\n  font-size: 2rem;\r\n  letter-spacing: 1px;\r\n  line-height: 24px;\r\n  color: var(--text-color);\r\n\r\n}\r\n.sectioner--the-latest-posts .section-header__date {\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n  font-size: 14px;\r\n  letter-spacing: 0.35px;\r\n  color: var(--gray-span);\r\n  /* margin-top: 5px; */\r\n  margin: 14px;\r\n}\r\n.the-latest__secondary-wrapper .the-latest__story:last-of-type {\r\n  margin-bottom: 0;\r\n  border-bottom: none;\r\n  padding-bottom: 0;\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .sectioner--featured-category {\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n    /* padding-bottom: 0;\r\n    margin-bottom: 0; */\r\n    /* padding: 25px 0; */\r\n  }\r\n  .sectioner--featured-category .section-header .section-header__heading a,\r\n  .sectioner--featured-category .section-header .section-header__heading span {\r\n    font-size: 1.375rem;\r\n    letter-spacing: 1px;\r\n  }\r\n  .sectioner {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n@media screen and (width <= 479px) {\r\n  .sectioner--featured-category .section-header .section-header__heading {\r\n    font-size: var(--mobile-font-heading--m-h2);\r\n  }\r\n}\r\n.featured-category__secondary-wrapper {\r\n  width: 100%;\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .featured-category__secondary-wrapper {\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n@media only screen and (min-width: 61.25rem) {\r\n  .featured-category__secondary-wrapper {\r\n    flex-direction: column;\r\n    width: calc(35% - 1.875rem);\r\n  }\r\n}\r\n@media only screen and (min-width: 92.5rem) {\r\n  .featured-category__secondary-wrapper {\r\n    width: calc(30% - 0.125rem);\r\n  }\r\n}\r\n.the-latest__secondary-wrapper {\r\n  flex-direction: row;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n}\r\n.article {\r\n  display: block;\r\n}\r\n.featured-category__story {\r\n  width: 100%;\r\n  margin-bottom: 1.25rem;\r\n  text-align: center;\r\n}\r\n.featured-category__story .post-meta {\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  align-items: flex-start;\r\n  justify-content: center;\r\n  padding-bottom: 35px;\r\n}\r\n.post-meta__timestamp {\r\n  margin-left: 25px;\r\n  font-size: 14px;\r\n  line-height: 20px;\r\n  letter-spacing: 0.35px;\r\n  color: var(--gray-span);\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .featured-category__story {\r\n    width: calc(50% - 1.25rem);\r\n  }\r\n}\r\n@media only screen and (min-width: 61.25rem) {\r\n  .featured-category__story {\r\n    width: 100%;\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .the-latest__secondary-wrapper .the-latest__story {\r\n    width: calc(50% - 1.25rem);\r\n  }\r\n}\r\n.featured-image {\r\n  margin-bottom: 0.625rem;\r\n}\r\n.featured-category__secondary-wrapper .featured-image {\r\n  position: relative;\r\n}\r\n.featured-category__secondary-wrapper .featured-image:before {\r\n  display: block;\r\n  content: \" \";\r\n  width: 100%;\r\n  padding-top: 56.25%;\r\n}\r\n.featured-image .image-wrapper {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n.featured-category__secondary-wrapper .featured-image .image-wrapper {\r\n  overflow: hidden;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n.featured-image img {\r\n  display: block;\r\n  width: 100%;\r\n  height: auto;\r\n  object-fit: cover;\r\n  /* height: auto; */\r\n}\r\n.featured-image .image-wrapper:after {\r\n  content: \"\";\r\n  display: block;\r\n  /* background-image: radial-gradient(\r\n    90% 168%,\r\n    hsla(0, 0%, 100%, 0) 0,\r\n    rgba(0, 0, 0, 0.11) 100%\r\n  ); */\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n.entry__category {\r\n  /* font: 400 0.875rem / normal, sans-serif; */\r\n  color: #e02020;\r\n  display: inline-block;\r\n  letter-spacing: 1px;\r\n  margin-bottom: 0.625rem;\r\n  text-transform: uppercase;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 600;\r\n}\r\n.featured-category__secondary-wrapper .entry__category {\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n}\r\n.featured-category__story .entry__category {\r\n  /* font-family: sans-serif; */\r\n  font-size: 11px;\r\n  line-height: 15px;\r\n  /* letter-spacing: 0.35px; */\r\n}\r\n@media only screen and (min-width: 70.625rem) {\r\n  .featured-category__story .entry__category {\r\n    font-size: 15px;\r\n    line-height: 22px;\r\n  }\r\n}\r\n@media only screen and (max-width: 61.1875rem) {\r\n  .featured-category__secondary-wrapper .featured-image {\r\n    max-width: 100%;\r\n    min-width: 35%;\r\n    overflow: hidden;\r\n  }\r\n}\r\n@media only screen and (max-width: 375px) {\r\n  .featured-category__secondary-wrapper .featured-image {\r\n    max-height: 90px;\r\n  }\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .the-latest__secondary-wrapper .featured-image {\r\n    max-width: 100%;\r\n    min-width: 35%;\r\n    overflow: hidden;\r\n  }\r\n  .grid__Section_pt {\r\n    padding-top: 25px;\r\n    padding-bottom: 0px;\r\n  }\r\n}\r\n@media only screen and (max-width: 375px) {\r\n  .the-latest__secondary-wrapper .featured-image {\r\n    max-height: 90px;\r\n  }\r\n}\r\n.entry__category a {\r\n  color: inherit;\r\n  text-decoration: none;\r\n}\r\nhtml,\r\nhtml a {\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  font-smoothing: antialiased;\r\n  text-rendering: optimizeLegibility;\r\n}\r\n.entry__heading {\r\n  /* font: 600 1.375rem / normal titling-gothic-fb-narrow, sans-serif; */\r\n  color: inherit;\r\n  margin-bottom: 0.75rem;\r\n  max-width: 100%;\r\n  font-family: rocky, sans-serif;\r\n  letter-spacing: 0.37px;\r\n}\r\n.category_space .entry__heading{\r\n  margin-top: 1rem;\r\n}\r\n.featured-category__secondary-wrapper .entry__heading {\r\n  /* font-family: rocky, sans-serif; */\r\n}\r\n.featured-category__story .entry__heading {\r\n  font-family: rocky, sans-serif;\r\n  color: var(--text-color);\r\n  /* font-family: rocky, sans-serif; */\r\n  font-size: 23px;\r\n  line-height: 29px;\r\n  letter-spacing: normal;\r\n  transition: opacity 0.3s ease-in 0.5s;\r\n}\r\n.featured-category__story .entry__heading:hover {\r\n  opacity: 0.8;\r\n}\r\n.home .entry__heading {\r\n  transition: opacity 0.3s ease-in 0.5s;\r\n}\r\n\r\n.entry__heading a {\r\n  color: inherit;\r\n  text-decoration: none;\r\n  transition: opacity 0.3s ease-in 0.5s;\r\n}\r\n.entry__heading a:hover {\r\n  opacity: 0.7;\r\n}\r\n.post-meta {\r\n  align-items: center;\r\n  display: flex;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.featured-category__story .post-meta {\r\n  justify-content: center;\r\n  padding-bottom: 15px;\r\n}\r\n.sectioner--the-latest-posts .post-meta {\r\n  flex-direction: row-reverse;\r\n  align-items: flex-start;\r\n  padding-bottom: 35px;\r\n}\r\n.latest-story--primary .post-meta {\r\n  flex-direction: row-reverse;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n}\r\n.latest-story .post-meta {\r\n  flex-direction: row-reverse;\r\n  justify-content: flex-end;\r\n  align-items: flex-start;\r\n}\r\n.grid__Section_dark .post-meta__author,\r\n.grid__Section_dark .post-meta__timestamp,\r\n.two-rivers-wrapper .post-meta__author,\r\n.two-rivers-wrapper .post-meta__timestamp {\r\n  color: var(--gray-span)!important;\r\n}\r\n.post-meta__author {\r\n  /* font-family: Georgia, sans-serif; */\r\n  font-size: 14px;\r\n  font-style: italic;\r\n  color: var(--gray-span);\r\n  order: 2;\r\n  letter-spacing: 1px;\r\n}\r\n.post-meta__author a {\r\n  color: inherit;\r\n}\r\n.featured-category__story .post-meta a {\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n  font-style: normal;\r\n  text-transform: uppercase;\r\n}\r\n.post-meta__author .by_author{\r\n  font-family: Georgia, sans-serif;\r\n  line-height: 14px;\r\n}\r\n.post-meta__author .author_name{\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  text-transform: uppercase;\r\n  font-style: normal;\r\n  font-weight: 600;\r\n  line-height: 14px;\r\n}\r\n\r\n\r\n.post-meta__author a span {\r\n  letter-spacing: 0.35px;\r\n  font-style: normal;\r\n  text-transform: uppercase;\r\n}\r\n.post-meta__author a span,\r\n.post-meta__author time {\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n}\r\n.sectioner--the-latest-posts .post-meta__timestamp {\r\n  margin-left: 25px;\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n  /* text-transform: uppercase; */\r\n  font-size: 14px;\r\n  line-height: 20px;\r\n  letter-spacing: 0.35px;\r\n  color: var(--gray-span);\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .featured-category__secondary-wrapper .featured-category__story {\r\n    display: flex;\r\n  }\r\n  .featured-category__secondary-wrapper .entry {\r\n    text-align: start;\r\n    margin-left: 10px;\r\n  }\r\n  .the-latest__secondary-wrapper .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .the-latest__secondary-wrapper .entry .entry__category {\r\n    order: 1;\r\n    margin-bottom: 5px;\r\n  }\r\n  .featured-category__story .entry__heading {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n  }\r\n  .the-latest__secondary-wrapper .entry .entry__heading {\r\n    order: 2;\r\n    margin-bottom: 5px;\r\n  }\r\n  .category_space .entry__heading {\r\n    margin-top: 0;\r\n}\r\n  .featured-category__story .post-meta {\r\n    padding-bottom: 0;\r\n  }\r\n  .the-latest__secondary-wrapper .post-meta {\r\n    /* display: none; */\r\n  }\r\n  .featured-category__secondary-wrapper .entry .post-meta {\r\n    justify-content: flex-end;\r\n    order: 3;\r\n  }\r\n  .featured-category__secondary-wrapper .entry .post-meta .post-meta__author {\r\n    font-size: 12px;\r\n  }\r\n  .featured-category__secondary-wrapper .entry .post-meta .post-meta__timestamp {\r\n    display: none;\r\n  }\r\n}\r\n@media only screen and (max-width: 41.6875rem) and (max-width: 41.6875rem) {\r\n  .featured-category__secondary-wrapper .featured-category__story {\r\n    border-bottom: 0.0625rem solid #dddee4;\r\n    padding-bottom: 1.25rem;\r\n  }\r\n  /* .featured-category__story .entry__heading {\r\n    font-size: 17px;\r\n  } */\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .the-latest__secondary-wrapper .the-latest__story {\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    text-align: left;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;AAGA;;;;AAGA;;;;AAGA;EACE;;;;;;;AAMF;;;;;AAKA;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;;;AAYA;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;AAKA;EACE;;;;;EAOA;;;;;EAKA;;;;;AAIF;EACE;;;;;AAIF;;;;AAGA;EACE;;;;;;AAKF;EACE;;;;;;AAKF;EACE;;;;;AAIF;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;AAOA;EACE;;;;;AAIF;EACE;;;;;;AAKF;EACE;;;;;AAIF;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;AAcA;;;;;;;;;;AAUA;;;AAGA;;;;;AAMA;EACE;;;;;;AAKF;EACE;;;;;;;AAMF;EACE;;;;;AAIF;EACE;;;;;;EAKA;;;;;;AAKF;EACE;;;;;AAIF;;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAGA;;;AAGA;;;;;;;;;AASA;;;;AAGA;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAMA;;;;;;;;AAQA;;;;AAGA;;;;;AAKA;;;;;AAIA;;;;;;;;AASA;;;;;;AAKA;;;AAIA;;;;;;;;AASA;EACE;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;EAGA;;;;;EAIA;;;;EAGA;;;;;AAIF;EACE;;;;;;AAQF;EACE"}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}