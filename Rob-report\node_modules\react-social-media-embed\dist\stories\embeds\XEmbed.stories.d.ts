import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
declare const StoryComponent: ({ url, width, height, linkText, placeholderImageUrl, placeholderSpinner, placeholderSpinnerDisabled, placeholderProps, embedPlaceholder, placeholderDisabled, twitterTweetEmbedProps, ...divProps }: import("../../components/embeds/XEmbed").XEmbedProps) => React.JSX.Element;
declare const meta: Meta<typeof StoryComponent>;
export default meta;
type Story = StoryObj<typeof meta>;
export declare const MainExample: Story;
export declare const FluidWidth: Story;
export declare const Width250AtMin: Story;
export declare const Width400: Story;
export declare const Width550AtMax: Story;
export declare const Width800AtOverMax: Story;
export declare const Width150AtUnderMin: Story;
export declare const Width50Percent: Story;
export declare const Width100Percent: Story;
export declare const UrlOnly: Story;
export declare const WithPlaceholderImage: Story;
export declare const CustomPlaceholder: Story;
export declare const CustomPlaceholderLinkText: Story;
export declare const PlaceholderSpinnerDisabled: Story;
export declare const PlaceholderDisabled: Story;
