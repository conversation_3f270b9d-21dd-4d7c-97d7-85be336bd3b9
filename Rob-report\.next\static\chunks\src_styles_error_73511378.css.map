{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/error.css"], "sourcesContent": ["#error_page{\r\n    width: 100%;\r\n    height: 88vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background-color: var(--body-bg-color);\r\n}\r\n\r\n#error_page h2{\r\n    font-size: 6rem;\r\n    line-height: 1.1;\r\n    font-family: rocky, sans-serif;\r\n    font-weight: 300;\r\n}\r\n\r\n#error_page h4{\r\n    font-size: 4rem;\r\n    line-height: 1.1;\r\n    font-family: rocky, sans-serif;\r\n    font-weight: 300;\r\n    text-transform: capitalize;\r\n}\r\n#error_page p{\r\n    width: 60%;\r\n    text-align: center;\r\n    font-size: 18px;\r\n    line-height: 28px;\r\n    letter-spacing: .37px;\r\n    font-family: Georgia, sans-serif;\r\n    margin-top: 30px;\r\n}\r\n\r\n\r\n@media screen and (max-width: 767px) {\r\n    #error_page{\r\n        padding: 1rem;\r\n    }\r\n    #error_page p{\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n@media screen and (max-width: 960px) and (min-width: 767px) {\r\n    #error_page p{\r\n        width: 85%;\r\n    }\r\n}\r\n@media screen and (max-width: 1300px) and (min-width: 960px) {\r\n    #error_page p{\r\n        width: 75%;\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;;;AAWA;EACI;;;;EAGA;;;;;AAKJ;EACI;;;;;AAIJ;EACI"}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}