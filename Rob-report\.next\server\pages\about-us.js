const CHUNK_PUBLIC_PATH = "server/pages/about-us.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/src_components_common_BacktoTop_jsx_7d0bacb4._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__3dd7ba2b._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_217620e7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_md_index_mjs_a52b9af3._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_gr_index_mjs_79793c0c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_rx_index_mjs_f6315842._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_ci_index_mjs_c424bbd0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_io_index_mjs_0ae45aa7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_io5_index_mjs_d99fb21f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_fa_index_mjs_275a2265._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_bs_index_mjs_111623f2._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_lia_index_mjs_2c85c98e._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_lu_index_mjs_2828a6d6._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_fi_index_mjs_faa0559a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_lib_6322dabf._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-tweet_dist_5e45449f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_aea8b254._.js");
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/src/pages/[category]/[subcategory]/[stories]/index.jsx [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/src/pages/_document.jsx [ssr] (ecmascript)\", INNER_APP => \"[project]/src/pages/_app.jsx [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/src/pages/[category]/[subcategory]/[stories]/index.jsx [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/src/pages/_document.jsx [ssr] (ecmascript)\", INNER_APP => \"[project]/src/pages/_app.jsx [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
