{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/globals.css"], "sourcesContent": [":root {\r\n  --background: #fff;\r\n  --foreground: #171717;\r\n\r\n  --btn-top-bg: #fff;\r\n  --gray-bg: rgba(0, 0, 0, 0.85);\r\n  /*  */\r\n  --red: red;\r\n  --mobile-font-size--size-normal: 1rem;\r\n  --black-dark: #0e1010;\r\n  --white: white;\r\n  --desktop-font-heading--d-h1: 4.25rem;\r\n  --mobile-font-heading--m-h1: 2.75rem;\r\n  --mobile-font-size--size-large-30: 1.875rem;\r\n  --desktop-font-heading--d-h2: 2.5rem;\r\n  --mobile-font-heading--m-h2: 1.2rem;\r\n  --desktop-font-heading--d-h3: 1.5625rem;\r\n  --mobile-font-heading--m-h3: 1.4375rem;\r\n  --desktop-font-size-normal--subtitle-18: 1.125rem;\r\n  --mobile-font-size--subtitle-15: 0.9375rem;\r\n  --orange: #f68a33;\r\n  --desktop-font-size-normal--font-size-large-60: 3.75rem;\r\n  --desktop-font-size-normal--body-19: 1.1875rem;\r\n  --transparent: #0000;\r\n  --dark-text: #0f1628;\r\n  --black: black;\r\n  --r-page--cta: 0.812rem;\r\n  --desktop-paragraph-font--small-p: 0.9375rem;\r\n  --desktop-font-size-normal--tag-14: 0.875rem;\r\n  --dark-gray: #727272;\r\n  --gray: #8d8d8d;\r\n  --black-2-0: #0f1628;\r\n  --mobile-font-size--small-12: 0.75rem;\r\n  --desktop-font-size-normal--questions-20: 1.25rem;\r\n  --border-light: #0f16284d;\r\n  --white-2-0: #efefef;\r\n  --white-light: #fafaf1;\r\n  --desktop-font-size-normal--caption-10: 0.625rem;\r\n  --green: #396e8b;\r\n  --border-dark: white;\r\n  --navy-blue: #0b347c;\r\n  --desktop-paragraph-font--paragraph: 1.0625rem;\r\n  --desktop-font-size-normal--intro-35: 2.1875rem;\r\n  --mobile-font-size--size-medium-20: 1.25rem;\r\n  --Rockyfontfamily: rocky, serif;\r\n  --Sweetfontfamily: sweet, serif;\r\n\r\n  --gray-span: #fff;\r\n\r\n  --body-bg-color: #000;\r\n  --body-bg-colorblackwhite: #fff;\r\n  --text-color: #fff;\r\n  --text-colorblack: #000;\r\n  --filterblack: invert(1);\r\n  --drawer-btn-bg: #000;\r\n  --drawer-btn-bg-hover: #fff;\r\n  --about-side-border: rgba(128, 128, 128, 0.6);\r\n  --chip-color: #fff;\r\n\r\n  --line-color: rgba(128, 128, 128, 0.6);\r\n}\r\n/* Light Mode Theme */\r\n[data-theme=\"light\"] {\r\n  --body-bg-color: #fff;\r\n  --chip-color: #000;\r\n  --body-bg-colorblackwhite: #000;\r\n  --text-color: #000;\r\n  --text-colorwhite: #fff;\r\n  --filterwhite: invert(100%);\r\n  --filterblack: invert(0);\r\n  --btn-top-bg: #000;\r\n  --gray-span: #575757;\r\n  --drawer-btn-bg: #fff;\r\n  --drawer-btn-bg-hover: #000;\r\n  --about-side-border: #00000026;\r\n  --line-color: #00000026;\r\n  --related-post-bg: #f7f7f7;\r\n}\r\n[data-theme=\"dark\"] {\r\n  --related-post-bg: rgba(225, 225, 225, 0.05);\r\n}\r\n/* .light {\r\n  background-color: #fff !important;\r\n  color: black;\r\n}\r\n.dark {\r\n  color: white;\r\n  background-color: black !important;\r\n} */\r\n.dark .nav-content {\r\n  color: #000;\r\n}\r\n.dark .nav-content .brand img {\r\n  filter: invert(0);\r\n}\r\n.dark .nav-content .menu-item {\r\n  color: #000;\r\n}\r\n.HalfWidthBtn {\r\n  align-items: center;\r\n  justify-content: center;\r\n  display: flex;\r\n  width: 100%;\r\n}\r\nhtml,\r\nbody {\r\n  max-width: 100vw;\r\n  /* overflow-x: hidden; */\r\n}\r\n\r\nbody {\r\n  /* color: var(--foreground); */\r\n  background: var(--background);\r\n  /* font-family: Arial, sans-serif; */\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  font-size: var(--mobile-font-size--size-normal);\r\n  /* background-color: #fafafa; */\r\n  font-weight: 400;\r\n  line-height: 120%;\r\n  /* font-size: 1vw; */\r\n  /* color: #333; */\r\n  background-color: var(--background);\r\n  min-height: 100%;\r\n  font-display: swap;\r\n}\r\n\r\n* {\r\n  box-sizing: border-box;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\na {\r\n  color: inherit;\r\n  text-decoration: none;\r\n}\r\n/* .d-flex {\r\n  display: flex !important;\r\n} */\r\n.w-100,\r\n.w-full {\r\n  width: 100% !important;\r\n}\r\n.w-50 {\r\n  width: 50% !important;\r\n}\r\n.p-0 {\r\n  padding: 0 !important;\r\n}\r\n.py-0 {\r\n  padding-block: 0 !important;\r\n}\r\n.pt-0 {\r\n  padding-top: 0 !important;\r\n}\r\n.pb-0 {\r\n  padding-bottom: 0 !important;\r\n}\r\n.pl-0 {\r\n  padding-left: 0 !important;\r\n}\r\n.pr-0 {\r\n  padding-right: 0 !important;\r\n}\r\n.mb-0 {\r\n  margin-bottom: 0 !important;\r\n}\r\n.mb-2 {\r\n  margin-bottom: 0.5rem !important;\r\n}\r\n.text-underline {\r\n  text-decoration: underline !important;\r\n}\r\n.text-bold {\r\n  font-weight: bold !important;\r\n}\r\n.overflow-hidden {\r\n  overflow: hidden !important;\r\n}\r\n.flex-space-between {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n.ShareBtn {\r\n  display: flex;\r\n  gap: 5px;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  /* opacity: 0.8; */\r\n  transition: all 0.5s ease;\r\n  line-height: 1;\r\n}\r\n.ShareBtn svg {\r\n  font-size: 1rem;\r\n  /* font-weight: 400; */\r\n}\r\n.ShareBtn span {\r\n  font-size: 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 400;\r\n}\r\n.ShareBtn:hover {\r\n  /* fill: rgb(163, 163, 163); */\r\n  opacity: 0.8;\r\n}\r\n\r\n#sideBtn_container {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  right: 10px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: end;\r\n  gap: 20px;\r\n  z-index: 998;\r\n}\r\n\r\n.back-to-top {\r\n  opacity: 0;\r\n  pointer-events: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.back-to-top.show {\r\n  opacity: 1;\r\n  pointer-events: auto;\r\n}\r\n.btn-top {\r\n  position: relative;\r\n  gap: 8px;\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  padding: 5px;\r\n  font-size: 1.35rem;\r\n  cursor: pointer;\r\n  border: 1px solid currentColor;\r\n  background-color: transparent;\r\n  color: var(--btn-top-color, #fff);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.btn-top svg {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n.btn-top {\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n.btn-top:hover {\r\n  scale: 0.93;\r\n}\r\n\r\n/* Dark background -> white button */\r\n.back-to-top.dark .btn-top {\r\n  color: #fff;\r\n  background-color: transparent;\r\n}\r\n\r\n/* Light background -> black button with white bg */\r\n.back-to-top.light .btn-top {\r\n  color: #000;\r\n  background-color: #fff;\r\n}\r\n\r\n.search_btn {\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n.search_btn:hover {\r\n  scale: 0.93;\r\n}\r\n.search-btn {\r\n  position: relative;\r\n  gap: 8px;\r\n  width: 40px;\r\n  height: 40px;\r\n  padding: 5px;\r\n  font-size: 1.8rem;\r\n  cursor: pointer;\r\n  border: 1px solid currentColor;\r\n  background-color: transparent;\r\n  color: var(--text-color) !important;\r\n}\r\n.search-btn svg {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Dark background -> white button */\r\n.search_btn.dark .search-btn {\r\n  color: #fff;\r\n  background-color: transparent;\r\n}\r\n\r\n/* Light background -> black button with white bg */\r\n.search_btn.light .search-btn {\r\n  color: white;\r\n  border: none;\r\n}\r\n\r\n/* Ads Css */\r\n.ad-flex-all {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.ad-text::before {\r\n  color: var(--gray-span);\r\n  content: \"ADVERTISEMENT\";\r\n  display: block;\r\n  font-family: Arial, sans-serif;\r\n  font-size: 9px;\r\n  font-weight: 400;\r\n  letter-spacing: 1px;\r\n  line-height: 1.2;\r\n  margin: 5px auto;\r\n  text-align: center;\r\n  text-transform: uppercase;\r\n  -webkit-font-smoothing: antialiased;\r\n}\r\n\r\n.chipContainer {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin: 0 42px 40px;\r\n}\r\n\r\n@media only screen and (min-width: 768px) {\r\n  #sideBtn_container {\r\n    bottom: 50px;\r\n    right: 25px;\r\n  }\r\n\r\n  .ShareBtn svg {\r\n    font-size: 1.25rem;\r\n  }\r\n  .ShareBtn span {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n.headingTitle,\r\n.headingTitle h1 {\r\n  display: inline;\r\n  font-size: 3vw;\r\n  line-height: 10vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 600;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.sponsoredTitle,\r\n.sponsoredTitle h1 {\r\n  display: inline;\r\n  font-size: 2.5vw;\r\n  line-height: 10vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 600;\r\n  letter-spacing: 1px;\r\n  color: #e02020;\r\n  text-transform: uppercase;\r\n}\r\n\r\nhtml.w-mod-touch * {\r\n  background-attachment: scroll !important;\r\n}\r\n.containerWrapper {\r\n  width: 100%;\r\n  /* background-color: var(--body-bg-color); */\r\n  max-width: 70rem;\r\n  margin: 0 auto;\r\n  padding: 2rem 0;\r\n  padding-right: 0.625rem !important;\r\n  padding-left: 0.625rem !important;\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .headingTitle,\r\n  .headingTitle h1 {\r\n    font-size: 4vw;\r\n  }\r\n  .sponsoredTitle,\r\n  .sponsoredTitle h1 {\r\n    font-size: 4vw;\r\n  }\r\n}\r\n@media screen and (max-width: 768px) {\r\n  .headingTitle,\r\n  .headingTitle h1 {\r\n    font-size: 5vw;\r\n  }\r\n  .sponsoredTitle,\r\n  .sponsoredTitle h1 {\r\n    font-size: 5vw;\r\n  }\r\n  .chipContainer {\r\n    margin: 10px 10px 25px 10px;\r\n  }\r\n}\r\n@media screen and (max-width: 479px) {\r\n  .headingTitle,\r\n  .headingTitle h1 {\r\n    font-size: 6.5vw;\r\n  }\r\n  .sponsoredTitle,\r\n  .sponsoredTitle h1 {\r\n    font-size: 6vw;\r\n  }\r\n}\r\n@media only screen and (min-width: 92.5rem) {\r\n  .containerWrapper {\r\n    max-width: 100rem;\r\n    width: calc(100% - 21.875rem);\r\n    padding-right: 0.625rem !important;\r\n    padding-left: 0.625rem !important;\r\n  }\r\n}\r\n/* button css */\r\n.w-inline-block {\r\n  max-width: 100%;\r\n  display: inline-block;\r\n}\r\n.button_base {\r\n  font-weight: 500;\r\n  font-size: 17px;\r\n  line-height: 20px;\r\n  letter-spacing: 1px;\r\n  display: block;\r\n  /* padding: 1.125rem 0 0.875rem; */\r\n  text-align: center;\r\n  text-transform: uppercase;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  /* width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center; */\r\n}\r\n.view-more {\r\n  padding: 1.125rem 0 0.875rem;\r\n}\r\n.button_base.black {\r\n  background-color: #323440;\r\n  letter-spacing: 1.4px;\r\n  color: var(--white);\r\n  font-weight: 700;\r\n  text-transform: uppercase;\r\n  padding: 1.25rem 1.875rem;\r\n  /* font-size: 1.2vw; */\r\n  transition: all 0.5s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.button_base.black:hover {\r\n  background-color: #323440e6;\r\n}\r\n/* drawer save btn */\r\n.drawer_footer .button_base.black {\r\n  background-color: var(--drawer-btn-bg);\r\n  color: var(--drawer-btn-bg-hover);\r\n}\r\n.drawer_footer .button_base.black:hover {\r\n  background-color: var(--drawer-btn-bg-hover);\r\n  color: var(--drawer-btn-bg);\r\n}\r\n.hasMore_btn_wrap {\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n.hasMore_btn_wrap .button_base {\r\n  min-width: calc(50% - 1.625rem * 2);\r\n  border: none;\r\n}\r\n\r\n.sectioner--latest-stories .view-more-stories {\r\n  width: 100%;\r\n  margin-top: 2.5rem;\r\n}\r\n#home_wrappper {\r\n  background-color: var(--body-bg-color);\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .sectioner--latest-stories .view-more-stories {\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n  }\r\n  .button_base.black {\r\n    width: 100%;\r\n    display: inline-block;\r\n    font-size: 17px;\r\n    line-height: 10px;\r\n    letter-spacing: 1px;\r\n  }\r\n}\r\n@media only screen and (max-width: 479px) {\r\n  .button_base.black {\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    font-size: 4vw;\r\n    display: flex;\r\n    padding: 1.25rem 0.875rem;\r\n  }\r\n}\r\n/* button css */\r\n/* ::-webkit-scrollbar-button {\r\n  transition: 0ms !important;\r\n  width: 0px;\r\n  height: 0px;\r\n} */\r\n/* ::-webkit-scrollbar-track {\r\n  background: transparent;\r\n}\r\n::-webkit-scrollbar-thumb {\r\n  -webkit-border-radius: 0px;\r\n  border-radius: 0px;\r\n  background: transparent;\r\n  opacity: 0;\r\n} */\r\n::-webkit-resizer {\r\n  width: 0px;\r\n  height: 0px;\r\n}\r\n/* ::-webkit-scrollbar {\r\n  width: 0px;\r\n} */\r\n.cont-link .ex-text {\r\n  pointer-events: none;\r\n}\r\n\r\n/* transitionloader */\r\n.modes_helpers {\r\n  height: calc(var(--vh, 1vh) * 100);\r\n  position: fixed;\r\n  pointer-events: none;\r\n  z-index: 9999;\r\n  visibility: hidden;\r\n  bottom: auto;\r\n  width: 100%;\r\n}\r\n\r\n.modes_helpers i {\r\n  transform: scaleY(0);\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  visibility: visible;\r\n}\r\n\r\n.modes_helpers .a {\r\n  background-color: #000000;\r\n}\r\n\r\n.modes_helpers .b {\r\n  background-color: #ffffff;\r\n}\r\n\r\n.modes_helpers.reverse .a {\r\n  background-color: #000;\r\n}\r\n\r\n.modes_helpers.reverse .b {\r\n  background: #fff;\r\n}\r\n/* transitionloader */\r\n/* tagwrapper */\r\n.TagWrapper {\r\n  color: var(--text-color);\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n}\r\n.TagWrapper .featured-category__story .entry__heading {\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n}\r\n\r\n/* tagwrapper */\r\n\r\n/* Page Loader */\r\n.result-loader-div {\r\n  /* position: absolute; */\r\n  /* z-index: 999; */\r\n  /* top: 0; */\r\n  width: 100vw;\r\n  /* height: 100vh; */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  /* margin-block: 50%; */\r\n}\r\n.loader-cont {\r\n  position: fixed;\r\n  z-index: 999;\r\n  top: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: var(--body-bg-color);\r\n}\r\n.MuiLinearProgress-root {\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: block;\r\n  z-index: 0;\r\n  background-color: rgb(220, 220, 220) !important;\r\n  height: 2px !important;\r\n}\r\n\r\n.MuiBox-root {\r\n  width: 15% !important;\r\n}\r\n\r\n.MuiLinearProgress-bar {\r\n  background-color: var(--text-colorblack) !important;\r\n}\r\n\r\n.MuiLinearProgress-bar1 {\r\n  width: 100%;\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  top: 0;\r\n  -webkit-transition: -webkit-transform 0.2s linear;\r\n  transition: transform 0.2s linear;\r\n  transform-origin: left;\r\n  background-color: var(--text-colorblack);\r\n  width: auto;\r\n  -webkit-animation: animation-1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395)\r\n    infinite;\r\n  animation: animation-1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\r\n}\r\n\r\n.MuiLinearProgress-bar2 {\r\n  width: 100%;\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  top: 0;\r\n  -webkit-transition: -webkit-transform 0.2s linear;\r\n  transition: transform 0.2s linear;\r\n  transform-origin: left;\r\n  --LinearProgressBar2-barColor: var(--text-colorblack);\r\n  background-color: var(--LinearProgressBar2-barColor, currentColor);\r\n  width: auto;\r\n  -webkit-animation: animation-2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s\r\n    infinite;\r\n  animation: animation-2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;\r\n}\r\n\r\n@keyframes animation-1 {\r\n  0% {\r\n    left: -35%;\r\n    right: 100%;\r\n  }\r\n\r\n  60% {\r\n    left: 100%;\r\n    right: -90%;\r\n  }\r\n\r\n  100% {\r\n    left: 100%;\r\n    right: -90%;\r\n  }\r\n}\r\n\r\n@keyframes animation-2 {\r\n  0% {\r\n    left: -200%;\r\n    right: 100%;\r\n  }\r\n\r\n  60% {\r\n    left: 107%;\r\n    right: -8%;\r\n  }\r\n\r\n  100% {\r\n    left: 107%;\r\n    right: -8%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;;;;;;;;;;;;;;;;;AAgBA;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAMA;;;;;;;;;;;;AAiBA;;;;;;AAMA;;;;;AAOA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;;;AASA;;;;AAIA;;;;;;AAKA;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;AAIA;;;;;;;;;;;;;;;AAeA;;;;;;;AAMA;;;;;AAIA;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;;AAYA;;;;;;;AAQA;;;;;AAMA;;;;;AAMA;;;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;;;;AAQA;EACE;;;;;EAKA;;;;EAGA;;;;;AAIF;;;;;;;;;AAUA;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;AAUA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;AAIF;EACE;;;;EAIA;;;;;AAKF;EACE;;;;;;;;AAQF;;;;;AAIA;;;;;;;;;;;AAeA;;;;AAGA;;;;;;;;;;;AAYA;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAKA;;;;;AAIA;;;;AAGA;EACE;;;;;EAIA;;;;;;;;;AAQF;EACE;;;;;;;;;;AAwBF;;;;;AAOA;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;AAKA;;;;;AAQA;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;;;;;;;;AAiBA", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/homeHero.css"], "sourcesContent": [".specials-section {\r\n  z-index: 3;\r\n  background-color: var(--black);\r\n  padding-top: 0.625rem;\r\n  padding-bottom: 6.5625rem;\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n.specials-slider-wrap {\r\n  position: relative;\r\n  /* margin-top: 3.75rem; */\r\n}\r\n.swiper-horizontal {\r\n  touch-action: pan-y;\r\n}\r\n\r\n.swiper-wrapper.specials_swiper_wrapper {\r\n  z-index: -1;\r\n  margin-left: 2.25rem;\r\n  display: flex;\r\n}\r\n.swiper-slide.specials_swiper_slide {\r\n  z-index: -1;\r\n  cursor: grabbing;\r\n  flex: none;\r\n  width: 83rem;\r\n  /* height: 45.875rem; */\r\n  /* height: 100vh; */\r\n  aspect-ratio: 4/2;\r\n}\r\n.swiper-backface-hidden .swiper-slide {\r\n  transform: translateZ(0);\r\n  -webkit-backface-visibility: hidden;\r\n  backface-visibility: hidden;\r\n}\r\n.special-stories-slider-block {\r\n  z-index: 11;\r\n  color: var(--black);\r\n  /* background-image: url(https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg); */\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n  margin: 0 auto;\r\n}\r\n.special-stories-slider-block img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n.film-overlay-wrap {\r\n  background-image: linear-gradient(\r\n    221.52deg,\r\n    #0e101033 33.53%,\r\n    #0e1010cc 63.89%,\r\n    #0e1010fa 85.9%\r\n  );\r\n  justify-content: flex-start;\r\n  align-items: flex-end;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: absolute;\r\n  z-index: 99;\r\n  top: 0;\r\n  padding-left: 2.5rem;\r\n  padding: 0 5rem;\r\n  padding-bottom: 4.375rem;\r\n  padding-right: 2.5rem;\r\n  display: flex;\r\n}\r\n.heroSliderContentWrapper {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  display: flex;\r\n}\r\n.video-stories-slider-inner-block {\r\n  z-index: 1;\r\n  grid-column-gap: 1.375rem;\r\n  grid-row-gap: 1.375rem;\r\n  border-radius: 0.3125rem;\r\n  flex-flow: column;\r\n  justify-content: flex-end;\r\n  align-items: flex-start;\r\n  /* width: 100%; */\r\n  height: 100%;\r\n  max-width: 35.5rem;\r\n  display: flex;\r\n  position: relative;\r\n  /* padding-bottom: 4.375rem; */\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n\r\n.heading-h2 {\r\n  font-size: var(--desktop-font-heading--d-h2);\r\n  letter-spacing: -0.16px;\r\n  margin-top: 0;\r\n  margin-bottom: 0;\r\n  font-weight: 400;\r\n  line-height: 120%;\r\n  font-family: rocky, sans-serif;\r\n}\r\n.special-stories-summary {\r\n  color: var(--dark-gray);\r\n  font-size: var(--mobile-font-size--subtitle-15);\r\n  letter-spacing: 0.32px;\r\n  width: 100%;\r\n  max-width: 31rem;\r\n  line-height: 1.40625rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.text-color-white {\r\n  color: var(--white);\r\n}\r\n.special-stories-date-block {\r\n  grid-column-gap: 0.75rem;\r\n  grid-row-gap: 0.75rem;\r\n  color: var(--red);\r\n  /*  */\r\n  font-size: var(--desktop-paragraph-font--small-p);\r\n  letter-spacing: 0.16px;\r\n  text-transform: uppercase;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  display: flex;\r\n}\r\n.special-stories-date-block.color-white,\r\n.special-stories-title.letter-animation.color-white {\r\n  color: var(--white);\r\n}\r\n.swiper-arrow-wrap {\r\n  z-index: 5;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  max-width: 95%;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  display: flex;\r\n  position: absolute;\r\n  inset: 45% 0% auto;\r\n  transform: translateY(-50%);\r\n}\r\n.play-films-html {\r\n  color: var(--white);\r\n  justify-content: center;\r\n  align-items: center;\r\n  display: flex;\r\n}\r\n.w-embed:before,\r\n.w-embed:after {\r\n  content: \" \";\r\n  grid-area: 1 / 1 / 2 / 2;\r\n  display: table;\r\n}\r\n.swiper-button-prev:after,\r\n.swiper-rtl .swiper-button-next:after,\r\n.swiper-button-next:after,\r\n.swiper-rtl .swiper-button-prev:after {\r\n  content: \"\" !important;\r\n}\r\n\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  position: absolute;\r\n  top: var(--swiper-navigation-top-offset, 50%);\r\n  width: calc(var(--swiper-navigation-size) / 44 * 27);\r\n  height: var(--swiper-navigation-size);\r\n  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));\r\n  z-index: 10;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: var(--swiper-navigation-color, var(--swiper-theme-color));\r\n}\r\n\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  position: static !important;\r\n}\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  color: #fff;\r\n}\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  border: 1px solid var(--white);\r\n  color: var(--white);\r\n  cursor: pointer;\r\n  border-radius: 2.5rem;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 3.125rem;\r\n  min-width: 3.125rem;\r\n  height: 3.125rem;\r\n  min-height: 3.125rem;\r\n  transition: all 0.3s ease-in-out;\r\n  display: flex;\r\n}\r\n/* .swiper-button-prev, */\r\n.swiper-button-prev:hover {\r\n  background-color: #fff;\r\n}\r\n.swiper-button-next:hover {\r\n  background-color: #fff;\r\n}\r\n.swiper-button-next:hover svg:not(.swiper- button-prev) {\r\n  fill: var(--black);\r\n}\r\n.swiper-button-prev:hover svg:not(.swiper- button-prev) {\r\n  fill: var(--black);\r\n}\r\n.controls-wrapper {\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  max-width: 80%;\r\n  margin-top: 2.25rem;\r\n  padding-left: 2.125rem;\r\n  display: flex;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.next-up-text {\r\n  color: var(--white);\r\n\r\n  font-size: var(--desktop-font-size-normal--tag-14);\r\n  letter-spacing: 0.16px;\r\n  text-transform: uppercase;\r\n  flex: none;\r\n  position: relative;\r\n}\r\n\r\n.controls-row {\r\n  grid-column-gap: 1rem;\r\n  grid-row-gap: 1rem;\r\n  flex-flow: column;\r\n  flex: none;\r\n  width: 100%;\r\n  max-width: 15rem;\r\n  display: flex;\r\n  position: absolute;\r\n  inset: auto 10.0625rem 0% auto;\r\n}\r\n\r\n.bullet-pagination {\r\n  color: var(--white);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n.bullet-pagination .bullet {\r\n  font-size: 15px;\r\n  width: 100%;\r\n  display: flex;\r\n}\r\n.swiper-paginationn {\r\n  position: relative !important;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n.customscrollbar {\r\n  background-color: #fff3;\r\n  width: 100%;\r\n  height: 3px;\r\n  position: relative;\r\n}\r\n.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {\r\n  background-color: #fff !important;\r\n}\r\n/* .scrollfill {\r\n  width: 0%;\r\n  height: 100%;\r\n  transition: opacity 0.2scubic-bezier (0.257, 0.235, 0.632, 0.621);\r\n  position: absolute;\r\n  inset: 0% auto auto 0%;\r\n} */\r\n@media screen and (min-width: 1920px) {\r\n  .swiper-slide.specials_swiper_slide {\r\n    width: 96%;\r\n  }\r\n  .specials-section {\r\n  }\r\n}\r\n@media screen and (max-width: 991px) {\r\n  .specials-section {\r\n    padding-top: 0.3125rem;\r\n    padding-bottom: 7rem;\r\n  }\r\n  .swiper-slide.specials_swiper_slide {\r\n    width: 100%;\r\n    /* height: 45.9375rem; */\r\n  }\r\n  .swiper-wrapper.specials_swiper_wrapper {\r\n    margin-left: auto;\r\n  }\r\n  .special-stories-summary {\r\n    max-width: 33rem;\r\n  }\r\n  .swiper-arrow-wrap {\r\n    grid-column-gap: 0.8125rem;\r\n    grid-row-gap: 0.8125rem;\r\n    justify-content: flex-end;\r\n    align-items: center;\r\n    display: flex;\r\n    inset: auto 2% -3% auto;\r\n  }\r\n  .controls-wrapper {\r\n    margin-top: 2.875rem;\r\n  }\r\n\r\n  .controls-row {\r\n    max-width: 13.25rem;\r\n    position: absolute;\r\n    inset: auto 17% 1% auto;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .specials-section {\r\n    padding-bottom: 6.25rem;\r\n  }\r\n  .swiper-slide.specials_swiper_slide {\r\n    width: 100%;\r\n    /* height: 40rem; */\r\n  }\r\n  .special-stories-slider-block {\r\n    justify-content: flex-start;\r\n    align-items: flex-end;\r\n  }\r\n  .special-stories-slider-block img {\r\n    /* height: auto; */\r\n  }\r\n  .film-overlay-wrap {\r\n    /* position: relative;\r\n    z-index: 9; */\r\n    padding-left: 1rem;\r\n    padding-right: 1rem;\r\n    padding-bottom: 0;\r\n    /* height: 40%; */\r\n  }\r\n  .remove-margin-bottom {\r\n    width: 100%;\r\n  }\r\n  .controls-wrapper {\r\n    margin-top: 6.25rem;\r\n    padding-left: 1.25rem;\r\n  }\r\n\r\n  .controls-row {\r\n    inset: auto auto 10% 3%;\r\n  }\r\n  .swiper-arrow-wrap {\r\n    max-width: 100%;\r\n    bottom: 3%;\r\n    right: 5%;\r\n  }\r\n  .swiper-button-prev,\r\n  .swiper-button-next {\r\n    width: 2.8rem !important;\r\n    min-width: 2.8rem !important;\r\n    height: 2.8rem !important;\r\n    min-height: 2.8rem !important;\r\n    /* border-radius: 1.5rem; */\r\n  }\r\n}\r\n@media screen and (max-width: 600px) {\r\n  .specials-section {\r\n    background-color: var(--black-dark);\r\n  }\r\n  .swiper-slide.specials_swiper_slide {\r\n    height: auto;\r\n  }\r\n  .special-stories-slider-block img {\r\n    /* height: auto; */\r\n  }\r\n\r\n  .film-overlay-wrap {\r\n    padding-left: 1rem;\r\n    padding-right: 1rem;\r\n    position: relative;\r\n  }\r\n  .video-stories-slider-inner-block {\r\n    max-width: 100%;\r\n    grid-column-gap: 0.7rem;\r\n    grid-row-gap: 0.7rem;\r\n  }\r\n  .special-stories-date-block {\r\n    grid-column-gap: 0.7rem;\r\n    grid-row-gap: 0.7rem;\r\n    font-size: var(--desktop-font-size-normal--tag-14);\r\n  }\r\n  .heading-h2 {\r\n    font-size: var(--mobile-font-heading--m-h2);\r\n  }\r\n  .controls-wrapper {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .controls-row {\r\n    max-width: 11rem;\r\n    left: 5%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;AAKA;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAKA;;;;;;;;;;;;;;;;AAoBA;;;;;;;AAMA;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;;;;;;;;AAaA;;;;;;;AAMA;;;;;;AAMA;;;;AAOA;;;;;;;;;;;;;;AAeA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;AAgBA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;;;AAMA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;;;;AAUA;EACE;;;;EAGA;;;;AAGF;EACE;;;;;EAIA;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;EAIA;;;;;;;AAMF;EACE;;;;EAGA;;;;EAIA;;;;;EAIA;;;EAGA;;;;;;EAQA;;;;EAGA;;;;;EAKA;;;;EAGA;;;;;;EAKA;;;;;;;;AASF;EACE;;;;EAGA;;;;EAGA;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAIA", "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1098, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/animatedParagraph.css"], "sourcesContent": [".wrapper_base {\r\n  padding: 152px 0;\r\n  font-size: 1vw;\r\n}\r\n.wrapper_base.specialalign {\r\n  display: flex;\r\n  /* align-items: center; */\r\n  justify-content: center;\r\n  flex-direction: column;\r\n}\r\n.split_box {\r\n  width: 60%;\r\n  display: block;\r\n  font-family: rocky, sans-serif;\r\n}\r\n.wrapper_base.specialalign .split_box {\r\n  width: 60%;\r\n}\r\n.m_txt {\r\n  /* font-size: 3.1em; */\r\n  font-size: 2.5em;\r\n  line-height: 1.05;\r\n}\r\n.line {\r\n  position: relative;\r\n}\r\n.line-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  background-color: #fff;\r\n  /* background-color: red; */\r\n  /* opacity: 0.5; */\r\n  height: 100%;\r\n  /* width: 100%; */\r\n  z-index: 1;\r\n}\r\n.parabtn {\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  margin-top: 24px;\r\n}\r\n@media screen and (max-width: 991px) {\r\n  .wrapper_base {\r\n    padding: 112px 0;\r\n  }\r\n  .split_box,\r\n  .split_box:lang(de-de) {\r\n    width: 66%;\r\n  }\r\n  .wrapper_base.speciallign .split_box {\r\n    width: 80%;\r\n  }\r\n  .m_txt.split-lines {\r\n    font-size: 4em;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .wrapper_base {\r\n    padding: 72px 0;\r\n  }\r\n  .split_box {\r\n    width: 76%;\r\n  }\r\n  .wrapper_base.specialalign .split_box {\r\n    width: 76%;\r\n  }\r\n  .m_txt.split-lines {\r\n    font-size: 4.4em;\r\n  }\r\n}\r\n@media screen and (max-width: 479px) {\r\n  .wrapper_base {\r\n    padding: 100px 16px;\r\n  }\r\n  .split_box {\r\n    width: 100%;\r\n  }\r\n  .wrapper_base.specialalign .split_box {\r\n    width: 100%;\r\n  }\r\n  .m_txt.split-lines {\r\n    font-size: 5.9em;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;AAIA;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;;AAKA;;;;AAGA;;;;;;;;;AAWA;;;;;;;AAMA;EACE;;;;EAGA;;;;EAIA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;EAGA", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/cardGridSection.css"], "sourcesContent": ["/* .sectioner {\r\n  padding-bottom: 2.1875rem;\r\n} */\r\n.category_separate .sectioner{\r\n  padding-bottom: 0;\r\n}\r\n.containerWrapper.category_separate{\r\n  padding-bottom: 0;\r\n}\r\n.grid__Section_pt {\r\n  padding-top: 50px;\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .sectioner--featured-category {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n.sectioner--the-latest-posts {\r\n  /* padding: 1.5625rem; */\r\n  background-color: var(--background-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n}\r\n.module__heading,\r\n.section-header {\r\n  border-bottom: 1px solid #979797;\r\n  border-left: 1px solid #979797;\r\n  color: #000;\r\n  margin-bottom: 0.9375rem;\r\n  padding: 0 0 0.3125rem 0.625rem;\r\n  text-transform: uppercase;\r\n}\r\n.section-header {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.sectioner--featured-category .section-header {\r\n  display: block;\r\n  border-bottom: none;\r\n  border-left: none;\r\n  text-align: center;\r\n  padding: 0;\r\n  margin-bottom: 50px;\r\n  /* margin-top: 50px; */\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.section-header__heading {\r\n  /* font: 400 1rem / 1, sweet-sans-pro, sans-serif; */\r\n  letter-spacing: 1px;\r\n  font-weight: 600;\r\n}\r\n.sectioner--featured-category .section-header .section-header__heading {\r\n  position: relative;\r\n  z-index: 1;\r\n  font-size: var(--desktop-font-heading--d-h2);\r\n}\r\n.sectioner--featured-category .section-header .section-header__heading:before {\r\n  border-top: 0.1px solid var(--text-color);\r\n  content: \"\";\r\n  margin: 0 auto;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  z-index: -1;\r\n}\r\n.sectioner--featured-category .section-header .section-header__heading a,\r\n.sectioner--featured-category .section-header .section-header__heading span {\r\n    background-color: var(--body-bg-color);\r\n  padding: 0 10px;\r\n  font-size: 2rem;\r\n  letter-spacing: 1px;\r\n  line-height: 24px;\r\n  color: var(--text-color);\r\n\r\n}\r\n.sectioner--the-latest-posts .section-header__date {\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n  font-size: 14px;\r\n  letter-spacing: 0.35px;\r\n  color: var(--gray-span);\r\n  /* margin-top: 5px; */\r\n  margin: 14px;\r\n}\r\n.the-latest__secondary-wrapper .the-latest__story:last-of-type {\r\n  margin-bottom: 0;\r\n  border-bottom: none;\r\n  padding-bottom: 0;\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .sectioner--featured-category {\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n    /* padding-bottom: 0;\r\n    margin-bottom: 0; */\r\n    /* padding: 25px 0; */\r\n  }\r\n  .sectioner--featured-category .section-header .section-header__heading a,\r\n  .sectioner--featured-category .section-header .section-header__heading span {\r\n    font-size: 1.375rem;\r\n    letter-spacing: 1px;\r\n  }\r\n  .sectioner {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n@media screen and (width <= 479px) {\r\n  .sectioner--featured-category .section-header .section-header__heading {\r\n    font-size: var(--mobile-font-heading--m-h2);\r\n  }\r\n}\r\n.featured-category__secondary-wrapper {\r\n  width: 100%;\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .featured-category__secondary-wrapper {\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n@media only screen and (min-width: 61.25rem) {\r\n  .featured-category__secondary-wrapper {\r\n    flex-direction: column;\r\n    width: calc(35% - 1.875rem);\r\n  }\r\n}\r\n@media only screen and (min-width: 92.5rem) {\r\n  .featured-category__secondary-wrapper {\r\n    width: calc(30% - 0.125rem);\r\n  }\r\n}\r\n.the-latest__secondary-wrapper {\r\n  flex-direction: row;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n}\r\n.article {\r\n  display: block;\r\n}\r\n.featured-category__story {\r\n  width: 100%;\r\n  margin-bottom: 1.25rem;\r\n  text-align: center;\r\n}\r\n.featured-category__story .post-meta {\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  align-items: flex-start;\r\n  justify-content: center;\r\n  padding-bottom: 35px;\r\n}\r\n.post-meta__timestamp {\r\n  margin-left: 25px;\r\n  font-size: 14px;\r\n  line-height: 20px;\r\n  letter-spacing: 0.35px;\r\n  color: var(--gray-span);\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .featured-category__story {\r\n    width: calc(50% - 1.25rem);\r\n  }\r\n}\r\n@media only screen and (min-width: 61.25rem) {\r\n  .featured-category__story {\r\n    width: 100%;\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .the-latest__secondary-wrapper .the-latest__story {\r\n    width: calc(50% - 1.25rem);\r\n  }\r\n}\r\n.featured-image {\r\n  margin-bottom: 0.625rem;\r\n}\r\n.featured-category__secondary-wrapper .featured-image {\r\n  position: relative;\r\n}\r\n.featured-category__secondary-wrapper .featured-image:before {\r\n  display: block;\r\n  content: \" \";\r\n  width: 100%;\r\n  padding-top: 56.25%;\r\n}\r\n.featured-image .image-wrapper {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n.featured-category__secondary-wrapper .featured-image .image-wrapper {\r\n  overflow: hidden;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n.featured-image img {\r\n  display: block;\r\n  width: 100%;\r\n  height: auto;\r\n  object-fit: cover;\r\n  /* height: auto; */\r\n}\r\n.featured-image .image-wrapper:after {\r\n  content: \"\";\r\n  display: block;\r\n  /* background-image: radial-gradient(\r\n    90% 168%,\r\n    hsla(0, 0%, 100%, 0) 0,\r\n    rgba(0, 0, 0, 0.11) 100%\r\n  ); */\r\n  height: 100%;\r\n  width: 100%;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n.entry__category {\r\n  /* font: 400 0.875rem / normal, sans-serif; */\r\n  color: #e02020;\r\n  display: inline-block;\r\n  letter-spacing: 1px;\r\n  margin-bottom: 0.625rem;\r\n  text-transform: uppercase;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 600;\r\n}\r\n.featured-category__secondary-wrapper .entry__category {\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n}\r\n.featured-category__story .entry__category {\r\n  /* font-family: sans-serif; */\r\n  font-size: 11px;\r\n  line-height: 15px;\r\n  /* letter-spacing: 0.35px; */\r\n}\r\n@media only screen and (min-width: 70.625rem) {\r\n  .featured-category__story .entry__category {\r\n    font-size: 15px;\r\n    line-height: 22px;\r\n  }\r\n}\r\n@media only screen and (max-width: 61.1875rem) {\r\n  .featured-category__secondary-wrapper .featured-image {\r\n    max-width: 100%;\r\n    min-width: 35%;\r\n    overflow: hidden;\r\n  }\r\n}\r\n@media only screen and (max-width: 375px) {\r\n  .featured-category__secondary-wrapper .featured-image {\r\n    max-height: 90px;\r\n  }\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .the-latest__secondary-wrapper .featured-image {\r\n    max-width: 100%;\r\n    min-width: 35%;\r\n    overflow: hidden;\r\n  }\r\n  .grid__Section_pt {\r\n    padding-top: 25px;\r\n    padding-bottom: 0px;\r\n  }\r\n}\r\n@media only screen and (max-width: 375px) {\r\n  .the-latest__secondary-wrapper .featured-image {\r\n    max-height: 90px;\r\n  }\r\n}\r\n.entry__category a {\r\n  color: inherit;\r\n  text-decoration: none;\r\n}\r\nhtml,\r\nhtml a {\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  font-smoothing: antialiased;\r\n  text-rendering: optimizeLegibility;\r\n}\r\n.entry__heading {\r\n  /* font: 600 1.375rem / normal titling-gothic-fb-narrow, sans-serif; */\r\n  color: inherit;\r\n  margin-bottom: 0.75rem;\r\n  max-width: 100%;\r\n  font-family: rocky, sans-serif;\r\n  letter-spacing: 0.37px;\r\n}\r\n.category_space .entry__heading{\r\n  margin-top: 1rem;\r\n}\r\n.featured-category__secondary-wrapper .entry__heading {\r\n  /* font-family: rocky, sans-serif; */\r\n}\r\n.featured-category__story .entry__heading {\r\n  font-family: rocky, sans-serif;\r\n  color: var(--text-color);\r\n  /* font-family: rocky, sans-serif; */\r\n  font-size: 23px;\r\n  line-height: 29px;\r\n  letter-spacing: normal;\r\n  transition: opacity 0.3s ease-in 0.5s;\r\n}\r\n.featured-category__story .entry__heading:hover {\r\n  opacity: 0.8;\r\n}\r\n.home .entry__heading {\r\n  transition: opacity 0.3s ease-in 0.5s;\r\n}\r\n\r\n.entry__heading a {\r\n  color: inherit;\r\n  text-decoration: none;\r\n  transition: opacity 0.3s ease-in 0.5s;\r\n}\r\n.entry__heading a:hover {\r\n  opacity: 0.7;\r\n}\r\n.post-meta {\r\n  align-items: center;\r\n  display: flex;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.featured-category__story .post-meta {\r\n  justify-content: center;\r\n  padding-bottom: 15px;\r\n}\r\n.sectioner--the-latest-posts .post-meta {\r\n  flex-direction: row-reverse;\r\n  align-items: flex-start;\r\n  padding-bottom: 35px;\r\n}\r\n.latest-story--primary .post-meta {\r\n  flex-direction: row-reverse;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n}\r\n.latest-story .post-meta {\r\n  flex-direction: row-reverse;\r\n  justify-content: flex-end;\r\n  align-items: flex-start;\r\n}\r\n.grid__Section_dark .post-meta__author,\r\n.grid__Section_dark .post-meta__timestamp,\r\n.two-rivers-wrapper .post-meta__author,\r\n.two-rivers-wrapper .post-meta__timestamp {\r\n  color: var(--gray-span)!important;\r\n}\r\n.post-meta__author {\r\n  /* font-family: Georgia, sans-serif; */\r\n  font-size: 14px;\r\n  font-style: italic;\r\n  color: var(--gray-span);\r\n  order: 2;\r\n  letter-spacing: 1px;\r\n}\r\n.post-meta__author a {\r\n  color: inherit;\r\n}\r\n.featured-category__story .post-meta a {\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n  font-style: normal;\r\n  text-transform: uppercase;\r\n}\r\n.post-meta__author .by_author{\r\n  font-family: Georgia, sans-serif;\r\n  line-height: 14px;\r\n}\r\n.post-meta__author .author_name{\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  text-transform: uppercase;\r\n  font-style: normal;\r\n  font-weight: 600;\r\n  line-height: 14px;\r\n}\r\n\r\n\r\n.post-meta__author a span {\r\n  letter-spacing: 0.35px;\r\n  font-style: normal;\r\n  text-transform: uppercase;\r\n}\r\n.post-meta__author a span,\r\n.post-meta__author time {\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n}\r\n.sectioner--the-latest-posts .post-meta__timestamp {\r\n  margin-left: 25px;\r\n  /* font-family: sweet-sans-pro, sans-serif; */\r\n  /* text-transform: uppercase; */\r\n  font-size: 14px;\r\n  line-height: 20px;\r\n  letter-spacing: 0.35px;\r\n  color: var(--gray-span);\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .featured-category__secondary-wrapper .featured-category__story {\r\n    display: flex;\r\n  }\r\n  .featured-category__secondary-wrapper .entry {\r\n    text-align: start;\r\n    margin-left: 10px;\r\n  }\r\n  .the-latest__secondary-wrapper .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .the-latest__secondary-wrapper .entry .entry__category {\r\n    order: 1;\r\n    margin-bottom: 5px;\r\n  }\r\n  .featured-category__story .entry__heading {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n  }\r\n  .the-latest__secondary-wrapper .entry .entry__heading {\r\n    order: 2;\r\n    margin-bottom: 5px;\r\n  }\r\n  .category_space .entry__heading {\r\n    margin-top: 0;\r\n}\r\n  .featured-category__story .post-meta {\r\n    padding-bottom: 0;\r\n  }\r\n  .the-latest__secondary-wrapper .post-meta {\r\n    /* display: none; */\r\n  }\r\n  .featured-category__secondary-wrapper .entry .post-meta {\r\n    justify-content: flex-end;\r\n    order: 3;\r\n  }\r\n  .featured-category__secondary-wrapper .entry .post-meta .post-meta__author {\r\n    font-size: 12px;\r\n  }\r\n  .featured-category__secondary-wrapper .entry .post-meta .post-meta__timestamp {\r\n    display: none;\r\n  }\r\n}\r\n@media only screen and (max-width: 41.6875rem) and (max-width: 41.6875rem) {\r\n  .featured-category__secondary-wrapper .featured-category__story {\r\n    border-bottom: 0.0625rem solid #dddee4;\r\n    padding-bottom: 1.25rem;\r\n  }\r\n  /* .featured-category__story .entry__heading {\r\n    font-size: 17px;\r\n  } */\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .the-latest__secondary-wrapper .the-latest__story {\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    text-align: left;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;AAGA;;;;AAGA;;;;AAGA;EACE;;;;;;;AAMF;;;;;AAKA;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;;;AAYA;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;AAKA;EACE;;;;;EAOA;;;;;EAKA;;;;;AAIF;EACE;;;;;AAIF;;;;AAGA;EACE;;;;;;AAKF;EACE;;;;;;AAKF;EACE;;;;;AAIF;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;AAOA;EACE;;;;;AAIF;EACE;;;;;;AAKF;EACE;;;;;AAIF;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;AAcA;;;;;;;;;;AAUA;;;AAGA;;;;;AAMA;EACE;;;;;;AAKF;EACE;;;;;;;AAMF;EACE;;;;;AAIF;EACE;;;;;;EAKA;;;;;;AAKF;EACE;;;;;AAIF;;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAGA;;;AAGA;;;;;;;;;AASA;;;;AAGA;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAMA;;;;;;;;AAQA;;;;AAGA;;;;;AAKA;;;;;AAIA;;;;;;;;AASA;;;;;;AAKA;;;AAIA;;;;;;;;AASA;EACE;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;EAGA;;;;;EAIA;;;;EAGA;;;;;AAIF;EACE;;;;;;AAQF;EACE", "debugId": null}}, {"offset": {"line": 1702, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/highlightSection.css"], "sourcesContent": [".wrapper_base2 {\r\n  /* padding: 152px 0; */\r\n  font-size: 1vw;\r\n}\r\n.flexbox_wrapper {\r\n  grid-column-gap: 6em;\r\n  grid-row-gap: 6em;\r\n  justify-content: flex-start;\r\n  align-items: flex-start;\r\n  display: flex;\r\n  /* height: 40vh; */\r\n}\r\n.flexbox_wrapper.special_layout {\r\n  grid-column-gap: 0em;\r\n  grid-row-gap: 0em;\r\n}\r\n.l_side_tall {\r\n  width: 44%;\r\n  margin-right: auto;\r\n  padding-right: 3.9em;\r\n  position: relative;\r\n  /* top: 6em; */\r\n}\r\n.headline_blog_home {\r\n  grid-column-gap: 6px;\r\n  grid-row-gap: 6px;\r\n  flex-flow: column;\r\n  margin-bottom: 24px;\r\n  display: flex;\r\n}\r\n.article_headline {\r\n  width: auto;\r\n  font-size: 2.5em;\r\n  line-height: 1.1;\r\n  font-family: rocky, sans-serif;\r\n  font-weight: 600;\r\n}\r\n.b_txt {\r\n  letter-spacing: 0.02em;\r\n  font-size: 1.1rem;\r\n  line-height: 1.2;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.img_blog.r_side {\r\n  width: 55.8%;\r\n  /* height: 52em; */\r\n  margin-left: auto;\r\n  overflow: hidden;\r\n}\r\n.highlightimage {\r\n  object-fit: cover;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.button_base.black {\r\n  transition: all 0.5s ease;\r\n  border: none;\r\n}\r\n.button_base.black:hover {\r\n  background-color: #323440e6;\r\n}\r\n@media screen and (max-width: 1200px) {\r\n  .l_side_tall {\r\n    width: 50%;\r\n  }\r\n  .b_txt {\r\n    font-size: 1.2em;\r\n  }\r\n  .button_base.black {\r\n    font-size: 2vw;\r\n  }\r\n}\r\n@media screen and (max-width: 991px) {\r\n  .l_side_tall {\r\n    width: 50%;\r\n    padding-right: 4em;\r\n  }\r\n  .article_headline {\r\n    font-size: 4em;\r\n  }\r\n  .b_txt {\r\n    letter-spacing: 0.03em;\r\n    font-size: 1.6em;\r\n  }\r\n  .button_base {\r\n    padding-top: 20px;\r\n    padding-bottom: 18px;\r\n    /* font-size: 3vw; */\r\n  }\r\n  .button_base.black {\r\n    font-size: 2vw;\r\n  }\r\n  .img_blog.r_side {\r\n    width: 50%;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .flexbox_wrapper.special_layout {\r\n    flex-flow: column-reverse;\r\n  }\r\n  .l_side_tall {\r\n    width: 100%;\r\n    position: static;\r\n  }\r\n  .article_headline {\r\n    font-size: 6em;\r\n  }\r\n  .b_txt,\r\n  .b_txt.white {\r\n    /* font-size: 2.15em; */\r\n    font-size: var(--desktop-paragraph-font--small-p);\r\n  }\r\n  .button_base.black {\r\n    font-size: 3vw;\r\n  }\r\n  .img_blog.r_side {\r\n    width: 100%;\r\n    /* height: 92em; */\r\n    /* margin-top: 32px; */\r\n  }\r\n}\r\n@media screen and (max-width: 479px) {\r\n  .flexbox_wrapper.special_layout,\r\n  .flexbox_wrapper.partnerships,\r\n  .flexbox_wrapper.spec_part {\r\n    flex-flow: column-reverse;\r\n  }\r\n  .l_side_tall {\r\n    width: 100%;\r\n    padding-right: 0;\r\n    position: static;\r\n  }\r\n  .headline_blog_home {\r\n    margin-bottom: 16px;\r\n    margin-top: 16px;\r\n  }\r\n  .article_headline {\r\n    font-size: 7.1em;\r\n  }\r\n  .b_txt {\r\n    font-size: 4.5em;\r\n    font-size: var(--desktop-paragraph-font--small-p);\r\n  }\r\n  .button_base.black {\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    font-size: 3.6vw;\r\n    line-height: 1.5;\r\n    display: flex;\r\n  }\r\n  .img_blog.r_side {\r\n    width: 100%;\r\n    /* height: 102em; */\r\n    /* margin-top: 42px; */\r\n    margin-left: 0;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;;;AAQA;;;;;AAIA;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;AAMA;;;;;;AAKA;;;;;AAIA;;;;AAGA;EACE;;;;EAGA;;;;EAGA;;;;;AAIF;EACE;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAKA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAKA;;;;EAGA;;;;;AAMF;EACE;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;;;;;EAQA", "debugId": null}}, {"offset": {"line": 1882, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/swiperCardSection.css"], "sourcesContent": [".swipperCardsectionlatest {\r\n  position: relative;\r\n  padding: 5rem 0 0 0;\r\n}\r\n@media screen and (width <= 767px) {\r\n  .swipperCardsectionlatest {\r\n    padding: 2rem 0 0 0;\r\n  }\r\n}\r\n.latestCardPostHeading {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n.editor-picks__secondary-inner-wrapper {\r\n  /* padding: 1.25rem 0; */\r\n  /* margin-bottom: 1.9375rem; */\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.editor-picks__secondary-pick {\r\n  text-align: center;\r\n  /* margin-bottom: 1.25rem; */\r\n  margin-top: 1.5625rem;\r\n  position: relative;\r\n  /* margin-right: 0; */\r\n}\r\n.editor-picks__secondary-pick .featured-image {\r\n  overflow: hidden;\r\n  max-height: 260px !important;\r\n}\r\n.editor-picks__secondary-pick .featured-image img {\r\n  aspect-ratio: 16/9;\r\n}\r\n.editor-picks__secondary-pick .entry__category {\r\n  font-size: 11px;\r\n  line-height: 15px;\r\n  /* letter-spacing: 0.35px; */\r\n}\r\n.sectioner--editor-picks .entry {\r\n  color: #000;\r\n}\r\n.editor-picks__secondary-pick .entry__heading {\r\n  font-size: 23px;\r\n  line-height: 29px;\r\n}\r\n.entry__heading {\r\n  transition: opacity 0.3s ease-in;\r\n}\r\n.arrows_arrow {\r\n  position: relative;\r\n  z-index: 0;\r\n  display: inline-flex;\r\n  width: 45px;\r\n  height: 45px;\r\n  flex-shrink: 0;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 50%;\r\n  text-overflow: ellipsis;\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  user-select: none;\r\n  border: 1px solid #737373;\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n  color: #737373;\r\n  background-color: #0000;\r\n  /* transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1); */\r\n}\r\n.arrows_arrow svg {\r\n  position: relative;\r\n  z-index: 1;\r\n  opacity: 1 !important;\r\n  font-size: 16px;\r\n  transition: color 0.3s ease;\r\n}\r\n.arrows_arrow:hover {\r\n  border: 2px solid #000000;\r\n}\r\n.arrows_arrow:hover .arrows_arrow svg {\r\n  color: #000000;\r\n  font-weight: 900;\r\n}\r\n@media only screen and (width >= 70.625rem) {\r\n  .editor-picks__secondary-pick .entry__category {\r\n    font-size: 15px;\r\n    line-height: 22px;\r\n  }\r\n}\r\n/* @media only screen and (min-width: 41.75rem) {\r\n  .editor-picks__secondary-inner-wrapper {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n  }\r\n  .editor-picks__secondary-pick {\r\n    width: 32% !important;\r\n    min-width: 32% !important;\r\n    margin-bottom: 0;\r\n  }\r\n} */\r\n/* @media only screen and (max-width: 61.1875rem) {\r\n  .editor-picks__secondary-pick .featured-image {\r\n    max-width: 100% !important;\r\n    min-width: 35% !important;\r\n    overflow: hidden;\r\n    position: relative;\r\n  }\r\n\r\n  .editor-picks__secondary-pick .featured-image:before {\r\n    position: absolute;\r\n    inset: 0;\r\n    display: block;\r\n    content: \" \";\r\n    width: 100%;\r\n    padding-top: 75%;\r\n  }\r\n\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .editor-picks__secondary-pick {\r\n    display: flex;\r\n    border-top: 1px solid #e8e8e8;\r\n    padding-top: 25px;\r\n    box-sizing: border-box;\r\n   \r\n  }\r\n  .editor-picks__secondary-pick .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .editor-picks__secondary-pick .entry .entry__category {\r\n    font-size: 11px;\r\n    order: 2;\r\n  }\r\n  .editor-picks__secondary-pick .entry .entry__heading {\r\n    order: 1;\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n  }\r\n} */\r\n"], "names": [], "mappings": "AAAA;;;;;AAIA;EACE;;;;;AAIF;;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAIA;;;;AAGA;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;;;;;AAOA;;;;AAGA;;;;;AAIA;EACE", "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1989, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/categoryPage.css"], "sourcesContent": [".categoryWrapper {\r\n  color: var(--text-color);\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n  min-height: 60vh;\r\n}\r\n.categoryWrapper .featured-category__story .entry__heading {\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n}\r\n.categoryWrapper\r\n  .sectioner--featured-category\r\n  .section-header\r\n  .section-header__heading:before {\r\n  border-top: 0.1px solid var(--body-bg-colorblackwhite);\r\n  transition: border-top 0.45s ease-in 0.5s;\r\n}\r\n.categoryWrapper\r\n  .sectioner--featured-category\r\n  .section-header\r\n  .section-header__heading\r\n  a,\r\n.categoryWrapper\r\n  .sectioner--featured-category\r\n  .section-header\r\n  .section-header__heading\r\n  span {\r\n  background-color: var(--body-bg-color);\r\n  color: var(--text-color);\r\n  transition: background-color 0.45s ease-in 0.5s, color 0.45s ease-in 0.5s;\r\n}\r\nsection.tab_section {\r\n  position: relative;\r\n  padding: 40px 0;\r\n  scroll-margin-top: 120px;\r\n  /* height: 100vh; */\r\n}\r\nsection.tab_section.noPadding{\r\n  padding: 0;\r\n}\r\n.page_head_set {\r\n  text-align: center;\r\n  padding: 1vw 0 1.5vw 0;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  font-size: 1.25vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n}\r\n.editionTitle {\r\n  font-size: 3vw;\r\n  line-height: 10vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n/* .bar_input input::-webkit-input-placeholder {\r\n  color: red;\r\n  background: transparent;\r\n} */\r\n\r\n.page_breadcrumb {\r\n  padding-bottom: 2rem;\r\n}\r\n.page_breadcrumb ol li a {\r\n  font-size: 16px;\r\n}\r\n.page_breadcrumb ol {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n  list-style: none;\r\n  font-size: 20px;\r\n  padding: 0;\r\n}\r\n.page_breadcrumb ol li::after {\r\n  content: \">\";\r\n  display: inline-block;\r\n  position: relative;\r\n  font-weight: 300;\r\n  font-size: 14px;\r\n  margin: 0 5px;\r\n}\r\n.page_breadcrumb ol li:last-child::after {\r\n  content: \"\";\r\n}\r\n.search_bar {\r\n  padding: 12px 30px;\r\n  justify-content: space-between;\r\n  background-color: var(--body-bg-colorblackwhite);\r\n  color: var(--text-colorwhite);\r\n  transition: background-color 0.45s ease-in 0.5s, color 0.45s ease-in 0.5s;\r\n}\r\n/* optional for filter */\r\n.search_bar,\r\n.search_bar_side,\r\n.filters_toggle {\r\n  align-items: center;\r\n}\r\n.bar_input {\r\n  width: 100%;\r\n}\r\n.flexCntr {\r\n  display: flex;\r\n}\r\n\r\n.search_bar_side {\r\n  mix-blend-mode: exclusion;\r\n}\r\n.bar_icon {\r\n  position: relative;\r\n  padding-right: 20px;\r\n  border-right: 1px solid rgb(255 255 255 / 30%);\r\n}\r\n.bar_icon,\r\n.col_image {\r\n  margin-right: 20px;\r\n}\r\n.bar_input input {\r\n  background: transparent;\r\n  border: none;\r\n  outline: none;\r\n  appearance: none;\r\n  color: #fff;\r\n  width: 100%;\r\n  min-width: 232px;\r\n  font-size: 1rem;\r\n}\r\ninput::selection {\r\n  background: transparent;\r\n}\r\n.bar_input input:focus {\r\n  outline: none;\r\n  border: none;\r\n  box-shadow: none;\r\n  background-color: transparent !important;\r\n}\r\n\r\ninput:-webkit-autofill,\r\ninput:-webkit-autofill:hover, \r\ninput:-webkit-autofill:focus{\r\n  background-color: transparent !important;\r\n  border: 1px solid transparent;\r\n  -webkit-text-fill-color: #F1F1F1;\r\n  -webkit-box-shadow: 0 0 0px 1000px transparent inset;\r\n  transition: background-color 5000s ease-in-out 0s;\r\n}\r\n\r\n.f_lable {\r\n  font-size: 16px;\r\n  text-transform: uppercase;\r\n  font-weight: 500;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n}\r\n.filters_circle {\r\n  width: 32px;\r\n  height: 32px;\r\n  background: #fff;\r\n  border-radius: 50%;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n.filters_circle,\r\n.tab span a {\r\n  margin-left: 20px;\r\n}\r\n.filters_circle i {\r\n  width: 4px;\r\n  height: 4px;\r\n  background: #fff;\r\n  mix-blend-mode: exclusion;\r\n  margin: 1px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.search_bar,\r\n.search_bar_side,\r\n.filters_toggle {\r\n  align-items: center;\r\n}\r\n\r\n/* fixed  */\r\n.fixed-bar {\r\n  position: sticky;\r\n  top: 6.5rem;\r\n  z-index: 99;\r\n  width: 100%;\r\n}\r\n@media only screen and (width <= 61.1875rem) {\r\n  /* fixed  */\r\n  .fixed-bar {\r\n    top: 4.5rem;\r\n  }\r\n}\r\n.fixed_item {\r\n  z-index: 599;\r\n  position: relative;\r\n\r\n  /* top: -1px; */\r\n  transition: margin 0.2s cubic-bezier(0.22, 0.61, 0.36, 1);\r\n}\r\n.page_bar {\r\n  margin: 0 42px;\r\n}\r\n\r\n.tabs_bar {\r\n  width: 100%;\r\n  /* margin: 0 auto; */\r\n  display: flex;\r\n  align-items: center;\r\n  /* position: sticky;\r\n  top: 0%; */\r\n  /* overflow: hidden; */\r\n  color: #fff;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  background: #2a2a2a;\r\n  height: 60px;\r\n  overflow-x: hidden;\r\n  /* touch-action: pan-y; */\r\n}\r\n.tabs_bar::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n.tabs_bar .tab {\r\n  width: calc(100% / 6);\r\n  height: 100%;\r\n  /* background-color: #ff000082; */\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  /* flex-wrap: wrap; */\r\n  flex-shrink: 0;\r\n  cursor: pointer;\r\n}\r\n.tabs_bar .tab:before {\r\n  position: absolute;\r\n  content: \"\";\r\n  width: 1px;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(255, 255, 255, 0.406);\r\n  z-index: 99;\r\n}\r\n.full_bg {\r\n  height: 100%;\r\n  width: 0%;\r\n  /* background-color: gray; */\r\n  background-color: #3f3f3f;\r\n  transform-origin: left center;\r\n  position: absolute;\r\n  left: 0;\r\n}\r\n.tab_content {\r\n  position: relative;\r\n  opacity: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  padding: 20px 25px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.tabs_bar .tab_content span {\r\n  text-overflow: ellipsis;\r\n  /* overflow: hidden; */\r\n  width: 100%;\r\n  text-align: center;\r\n  letter-spacing: 1px;\r\n}\r\n.tabs_bar .tab_content .f_16 {\r\n  font-size: 16px;\r\n  line-height: 20px;\r\n}\r\n.tabs_bar .tab_content .f_40 {\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.tab span:not(.flex) {\r\n  display: block;\r\n}\r\n.bar_icon svg {\r\n  max-width: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .tabs_bar {\r\n    width: 100%;\r\n  }\r\n  /* .tabs_bar .tab {\r\n    width: calc(100% / 6);\r\n  } */\r\n  .editionTitle {\r\n    font-size: 4vw;\r\n  }\r\n  .tabs_bar .tab_content span {\r\n    font-size: 13px;\r\n  }\r\n}\r\n@media screen and (max-width: 960px) {\r\n  /* .tabs_bar {\r\n    width: 100%;\r\n  }\r\n  .tabs_bar .tab {\r\n    width: calc(100% / 6);\r\n  } */\r\n}\r\n@media screen and (max-width: 800px) {\r\n  /* .tabs_bar .tab {\r\n    width: calc(100% / 4);\r\n  } */\r\n  .tab_content {\r\n    padding: 10px 0;\r\n  }\r\n  .page_bar {\r\n    margin: 0 10px;\r\n  }\r\n  .tabs_bar .tab_content span {\r\n    font-size: 13px;\r\n  }\r\n  .page_head_set {\r\n    margin: 0;\r\n    font-size: 3vw;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .tabs_bar .tab {\r\n    width: 100%;\r\n  }\r\n  .editionTitle,\r\n  .search_input input {\r\n    font-size: 6vw;\r\n    line-height: 19vw;\r\n  }\r\n  .line-height-10 {\r\n    line-height: 10vw!important;\r\n  }\r\n  section.tab_section {\r\n    padding: 2rem 0;\r\n  }\r\n  .page_head_set {\r\n    margin: 0;\r\n    font-size: 3vw;\r\n  }\r\n  .page_breadcrumb ol li a {\r\n    font-size: 12px;\r\n  }\r\n  .categoryWrapper .featured-category__story .entry__heading {\r\n    display: -webkit-box;\r\n    max-width: 100%;\r\n    -webkit-line-clamp: 3;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n  }\r\n}\r\n@media screen and (max-width: 640px) {\r\n  .search_bar {\r\n    padding: 15px;\r\n  }\r\n  .filters_toggle .f_lable {\r\n    display: none;\r\n  }\r\n  .bar_input input {\r\n    min-width: 190px;\r\n  }\r\n  .tabs_bar {\r\n    width: 100%;\r\n  }\r\n  .tabs_bar .tab {\r\n    width: 100%;\r\n  }\r\n  .tabs_bar .tab_content span {\r\n    font-size: 1rem;\r\n  }\r\n  /* .tabs_bar .tab_content {\r\n    padding: 13.5px 25px;\r\n  } */\r\n}\r\n@media screen and (max-width: 600px) {\r\n  .page_head_set {\r\n    margin: 0;\r\n    font-size: 4vw;\r\n  }\r\n\r\n  .bar_input input {\r\n    min-width: 100px;\r\n    font-size: 16px;\r\n  }\r\n  .bar_icon,\r\n  .col_image {\r\n    margin-right: 5px;\r\n    padding-right: 0;\r\n  }\r\n  .bar_icon {\r\n    border: 0;\r\n  }\r\n  .bar_icon svg {\r\n    max-width: 80%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;AAMA;;;;;AAIA;;;;;AAOA;;;;;;AAcA;;;;;;AAMA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;;;;;AAaA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;AAQA;;;;AAKA;;;;AAGA;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;AAIA;;;;;;;;;;;AAUA;;;;AAGA;;;;;;;AAOA;;;;;;;;AAUA;;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AASA;;;;AAOA;;;;;;;AAMA;EAEE;;;;;AAIF;;;;;;AAOA;;;;AAIA;;;;;;;;;;;AAeA;;;;AAGA;;;;;;;;;;;AAYA;;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;AAIA;;;;;AAKA;;;;AAGA;;;;;AAKA;EACE;;;;EAMA;;;;EAGA;;;;;AAIF;;;;AAQA;EAIE;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;AAKF;EACE;;;;EAGA;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;;;;AAQF;EACE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAOF;EACE;;;;;EAKA;;;;;EAIA;;;;;EAKA;;;;EAGA", "debugId": null}}, {"offset": {"line": 2403, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2406, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/stories.css"], "sourcesContent": ["/* ///////////////////////////////hero section/////////////////////////// */\r\n#story_wrapper {\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n}\r\n.story_hero_hero_container {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 1rem 0;\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n.story-hero-section .story-photoBy {\r\n  padding-inline: 0.625rem;\r\n  margin: 5px 0 0;\r\n}\r\n.story_hero_text_container {\r\n  width: 100%;\r\n  height: 83%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  /* gap: 2rem; */\r\n}\r\n\r\n.story_hero_category {\r\n  font-size: 1rem;\r\n  color: red;\r\n  font-weight: 500;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n  text-transform: uppercase;\r\n  margin-bottom: 1rem;\r\n}\r\n.story_hero_title {\r\n  font-size: 2rem;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n  width: 95%;\r\n  line-height: 1;\r\n  font-family: rocky, sans-serif;\r\n  letter-spacing: 0.37px;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.story_hero_description {\r\n  font-size: 1rem;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n  opacity: 0.7;\r\n  width: 75%;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 1.1;\r\n  letter-spacing: 0.37px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.story_hero_info_container {\r\n  width: 100%;\r\n  height: 17%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n\r\n  /* opacity: 0.8; */\r\n  padding: 0 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  flex-wrap: wrap;\r\n  letter-spacing: 0.37px;\r\n}\r\n\r\n.story_hero_info {\r\n  display: inherit;\r\n  white-space: pre;\r\n  font-size: 1rem;\r\n}\r\n\r\n/* .story-main-wrapper:has(.story-bannerSec) {\r\n  border-right: none !important;\r\n} */\r\n@media only screen and (max-width: 767px) {\r\n  .mob-py-0 {\r\n    padding-top: 0 !important;\r\n    padding-bottom: 0 !important;\r\n  }\r\n}\r\n@media only screen and (min-width: 768px) {\r\n  .story_hero_hero_container {\r\n    height: 70vh;\r\n    padding: 0;\r\n  }\r\n}\r\n@media only screen and (min-width: 92.5rem) {\r\n  .story-main-wrapper > * {\r\n    max-width: 100rem;\r\n  }\r\n}\r\n\r\n.story_hero_image_container {\r\n  width: 100%;\r\n  height: auto;\r\n  aspect-ratio: 16/9;\r\n  background-color: gray;\r\n  position: relative;\r\n}\r\n.story-photoBy {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 5px;\r\n  margin: 1px 0 20px;\r\n  line-height: 1.35;\r\n  text-align: left;\r\n}\r\n.story-caption {\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-size: 0.875rem;\r\n  letter-spacing: 0.37px;\r\n  font-weight: 500;\r\n  line-height: 1.1;\r\n  color: var(--text-color) !important;\r\n  opacity: 0.8;\r\n  text-align: left;\r\n}\r\n.story-courtesy {\r\n  font-family: rocky, sans-serif;\r\n  font-size: 0.875rem;\r\n  letter-spacing: 1px;\r\n  color: var(--text-color) !important;\r\n  text-align: left;\r\n}\r\n.story-main-wrapper .story-bannerSec {\r\n  display: block;\r\n  position: relative;\r\n  width: 100vw;\r\n  height: auto;\r\n  margin-left: 0 !important;\r\n  margin-right: 0 !important;\r\n  margin-bottom: 20px;\r\n  padding-inline: 0 !important;\r\n}\r\n.story-main-wrapper .story-bannerSec .story-photoBy {\r\n  padding: 0px 20px;\r\n}\r\n.react-tweet-theme {\r\n  margin-bottom: 0px !important;\r\n}\r\n.embed-frame iframe {\r\n  height: 100% !important;\r\n}\r\n.embed-frame.embed-youtube {\r\n  aspect-ratio: 16/9;\r\n}\r\n.instaembed {\r\n  width: 100% !important;\r\n}\r\n.embed-frame.embed-instagram {\r\n  height: auto;\r\n  aspect-ratio: 9 / 16;\r\n  margin: 0 auto;\r\n}\r\n.story-listStyle {\r\n  padding-inline-start: 25px !important;\r\n}\r\n.d-flex {\r\n  display: grid;\r\n  grid-template-columns: 1fr;\r\n}\r\n.d-flex > :nth-child(3) {\r\n  grid-column: 1 / -1;\r\n}\r\n.story-main-container p span {\r\n  color: var(--text-color) !important;\r\n}\r\n@media (max-width: 500px) {\r\n  .story_hero_description {\r\n    width: 95%;\r\n  }\r\n  .story_hero_info_container {\r\n    gap: 20px;\r\n  }\r\n  .story_info_tag:nth-child(2) {\r\n    order: 3;\r\n  }\r\n\r\n  .story_info_tag:nth-child(3) {\r\n    order: 2;\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (min-width: 768px) {\r\n  .instaembed {\r\n    /* width: 75% !important; */\r\n  }\r\n  .embed-frame.embed-instagram {\r\n    height: 100vh;\r\n    aspect-ratio: 9 / 16;\r\n    margin: 0 auto;\r\n  }\r\n  .story_hero_title {\r\n    font-size: 4rem;\r\n    line-height: 1.1;\r\n    font-family: rocky, sans-serif;\r\n    width: 75%;\r\n  }\r\n\r\n  .story_hero_description {\r\n    font-size: 1.5rem;\r\n    font-family: sweet-sans-pro, sans-serif;\r\n    margin-bottom: 0px;\r\n  }\r\n\r\n  .story_hero_info {\r\n    font-size: 1.25rem;\r\n    font-family: sweet-sans-pro, sans-serif;\r\n  }\r\n\r\n  .story_hero_info_container {\r\n    padding: 0 2.5rem;\r\n  }\r\n  .d-flex {\r\n    grid-template-columns: 3fr 1fr;\r\n  }\r\n}\r\n@media (max-width: 768px) {\r\n  .d-flex {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .submenu-body.d-flex {\r\n    flex-direction: column-reverse !important;\r\n  }\r\n  #about_wrapper .d-flex {\r\n    flex-direction: column-reverse;\r\n  }\r\n\r\n  .d-flex > :nth-last-child(1) {\r\n    order: 2; /* last child becomes second */\r\n  }\r\n\r\n  .d-flex > :nth-last-child(2) {\r\n    order: 3; /* second-last becomes last */\r\n  }\r\n\r\n  .story-main-wrapper {\r\n    border: none;\r\n  }\r\n}\r\n\r\n/* /////////////////////////////main section/////////////////////////// */\r\n\r\n.story_main_classname_container {\r\n  /* width: 100%;\r\n  margin: 0 auto; */\r\n}\r\n.story-main-container {\r\n  /* width: 67vw; */\r\n}\r\n.story-side-container {\r\n  /* width: 33vw; */\r\n}\r\n.story-side-wrapper {\r\n  /* padding: 40px 0 40px 40px; */\r\n  /* margin-right: 10rem; */\r\n  padding: 0rem 0;\r\n}\r\n\r\n.story_main_classname_main {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-top: 10px;\r\n}\r\n.story-main-container h2 {\r\n  font-size: 1.5rem;\r\n  line-height: 1.1;\r\n  font-family: rocky, sans-serif;\r\n}\r\n.story-main-container h2 span,\r\n.story-main-container h3 span,\r\n.story-main-container h4 span,\r\n.story-main-container h5 span,\r\n.story-main-container h6 span {\r\n  color: var(--text-color) !important;\r\n}\r\n.story-main-container a,\r\n.story-main-container a > span {\r\n  position: relative;\r\n  color: red !important;\r\n}\r\n.story-main-container a::after {\r\n  content: \"\"; /* Required for the pseudo-element */\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 0%; /* Always 100% for active link */\r\n  height: 1px; /* Height of the border */\r\n  background-color: red !important; /* Border color */\r\n  transition: width 0.45s ease; /* Smooth transition of width */\r\n}\r\n\r\n.story-main-container a:hover::after {\r\n  width: 100%;\r\n}\r\n@media only screen and (min-width: 768px) {\r\n  .story_main_classname_main {\r\n    flex-direction: row;\r\n  }\r\n  .story-side-wrapper {\r\n    padding: 0rem 1rem;\r\n    padding-left: 2rem;\r\n  }\r\n  .story-main-container h2 {\r\n    font-size: 2rem;\r\n    line-height: 1.1;\r\n  }\r\n}\r\n\r\n.story_main_classname_left {\r\n  width: 100%;\r\n  padding: 20px 0;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_left {\r\n    width: 72%;\r\n    padding: 40px 40px 40px 0;\r\n  }\r\n}\r\n\r\n.story-main-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  /* padding: 20px; */\r\n  font-size: 20px;\r\n  text-align: justify;\r\n  font-family: Georgia, sans-serif;\r\n  line-height: 1.3;\r\n  font-weight: 100;\r\n  padding: 1rem 0;\r\n  /* padding: 40px 40px 40px 0px; */\r\n  /* border-bottom: 1px solid rgba(0, 0, 0, 0.15); */\r\n}\r\n\r\n.story-main-wrapper p {\r\n  font-weight: 300;\r\n  margin-bottom: 1.5rem;\r\n  line-height: 26px;\r\n  font-size: 16px;\r\n  /* letter-spacing: 0.37px; */\r\n  font-family: Georgia, sans-serif;\r\n  color: var(--text-color);\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story-main-wrapper {\r\n    font-size: 27px;\r\n    font-size: 1.4rem;\r\n    line-height: 1.2;\r\n    border-right: 1px solid var(--line-color);\r\n    padding: 0rem 1rem;\r\n    padding-right: 2rem;\r\n\r\n    /* padding-left: 40px; */\r\n  }\r\n  .story-main-wrapper p {\r\n    font-size: 18px;\r\n    line-height: 28px;\r\n    letter-spacing: 0.37px;\r\n  }\r\n}\r\n\r\n.story_main_classname_imageWrapper {\r\n  width: 100%;\r\n  /* height: 90vh; */\r\n  background: #ccc;\r\n  position: relative;\r\n  margin: 3rem 0;\r\n  aspect-ratio: 16/9;\r\n}\r\n\r\n.story_main_classname_divider {\r\n  display: none;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_divider {\r\n    display: block;\r\n    width: 1px;\r\n    background: #a4a4a475;\r\n  }\r\n}\r\n\r\n.story_main_classname_right {\r\n  width: 100%;\r\n  padding: 20px 0 20px 0px;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_right {\r\n    width: 28%;\r\n    padding: 40px 0 40px 10px;\r\n  }\r\n}\r\n@media (min-width: 1200px) {\r\n  .story_main_classname_right {\r\n    width: 28%;\r\n    padding: 40px 0 40px 40px;\r\n  }\r\n}\r\n.story_main_classname_card {\r\n  height: 65vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.story_main_classname_cardImage {\r\n  width: 100%;\r\n  object-fit: cover;\r\n}\r\n.story_main_classname_img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n.story_main_classname_cardText {\r\n  height: 10%;\r\n  text-align: center;\r\n  font-size: 1.5rem;\r\n  font-weight: 500;\r\n  padding: 10px;\r\n  font-family: rocky, sans-serif;\r\n  line-height: 1;\r\n  color: var(--text-colorblack);\r\n}\r\n\r\n.story_main_classname_alsoInteresting {\r\n  /* margin-top: 3rem; */\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  font-weight: 500;\r\n  position: relative;\r\n  /* border-bottom: 2px solid rgba(0, 0, 0, 0.12);\r\n  border-left: 2px solid rgba(0, 0, 0, 0.12); */\r\n  text-align: center;\r\n}\r\n.story_main_classname_alsoInteresting span {\r\n  background-color: var(--body-bg-color);\r\n  padding: 0 10px;\r\n  font-size: 1.4rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n  color: var(--text-color);\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n#about_wrapper .story_main_classname_alsoInteresting span {\r\n  background-color: var(--body-bg-color);\r\n  color: var(--text-color);\r\n  position: relative;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n.story_main_classname_alsoInteresting:before {\r\n  border-top: 0.1px solid var(--line-color);\r\n  content: \"\";\r\n  margin: 0 auto;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  z-index: 1;\r\n}\r\n#about_wrapper .story_main_classname_alsoInteresting:before {\r\n  border-top: 0.1px solid var(--line-color);\r\n  z-index: 1;\r\n}\r\n.story_main_classname_advertisement {\r\n  margin-top: 20px;\r\n  height: 60vh;\r\n  width: 100%;\r\n  background: #e0e0e0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  color: gray;\r\n}\r\n\r\n.story_main_classname_slider {\r\n  padding: 10px 0px 10px 0px;\r\n}\r\n.relatedWrapper {\r\n  display: grid;\r\n  grid-template-columns: repeat(1, 1fr);\r\n  gap: 0;\r\n}\r\n#related-stories-section {\r\n  padding-top: 0px;\r\n}\r\n\r\n@media (min-width: 668px) {\r\n  .relatedWrapper {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 24px;\r\n  }\r\n  .relatedWrapper > .featured-category__story {\r\n    width: 100%;\r\n  }\r\n}\r\n@media (min-width: 768px) {\r\n  #related-stories-section {\r\n    padding-top: 40px;\r\n  }\r\n  .story_main_classname_slider {\r\n    padding: 40px 0 40px 0px;\r\n  }\r\n}\r\n@media (min-width: 980px) {\r\n  .relatedWrapper {\r\n    display: grid;\r\n    grid-template-columns: repeat(3, 1fr);\r\n    gap: 24px;\r\n  }\r\n  .relatedWrapper > .featured-category__story {\r\n    width: 100%;\r\n  }\r\n}\r\n@media (min-width: 1200px) {\r\n  .story_main_classname_slider {\r\n    padding: 10px 0 10px 0;\r\n  }\r\n}\r\n.story_main_classname_social {\r\n  padding: 10px 0px;\r\n  margin-top: 5px;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_social {\r\n    margin-top: 5vh;\r\n    margin-bottom: 5vh;\r\n  }\r\n}\r\n@media (min-width: 1200px) {\r\n  .story_main_classname_social {\r\n    padding: 10px 0 10px 0;\r\n  }\r\n}\r\n.story_main_classname_footer {\r\n  width: 100%;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_footer {\r\n    display: flex;\r\n  }\r\n}\r\n\r\n/* ////////////////////////////////// interestin card /////////////////////////// */\r\n\r\n.story_interesting_classname_container {\r\n  width: 100%;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.story_interesting_classname_card {\r\n  width: 100%;\r\n}\r\n\r\n.story_interesting_classname_imageWrapper {\r\n  position: relative;\r\n  width: 100%;\r\n  aspect-ratio: 16/9;\r\n  overflow: hidden;\r\n  /* height: 22vh; */\r\n}\r\n\r\n.story_interesting_classname_image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.story_interesting_classname_text {\r\n  width: 100%;\r\n  min-height: 10vh;\r\n  text-align: center;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  margin-top: 0.625rem;\r\n}\r\n\r\n.story_interesting_classname_heading {\r\n  font-size: 1rem;\r\n  color: red;\r\n  font-weight: 500;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.story_interesting_classname_description {\r\n  font-size: 1.4rem;\r\n  color: var(--text-color);\r\n  font-family: rocky, sans-serif;\r\n  line-height: 1.2;\r\n}\r\n#about_wrapper .story_interesting_classname_description {\r\n  color: var(--text-color);\r\n  line-height: 1.2;\r\n}\r\n/* //////////////////////////////////////////swiper slider/////////////////////////////////////////// */\r\n\r\n.story_swiper_classname_container {\r\n  position: relative;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  margin: 0 auto;\r\n}\r\n\r\n.story_swiper_classname_swiper {\r\n  width: 100%;\r\n  aspect-ratio: 1;\r\n  /* height: 50vh; */\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_swiper_classname_swiper {\r\n    height: 50vh;\r\n  }\r\n}\r\n\r\n.story_swiper_classname_slide {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.story_swiper_classname_image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.story_swiper_classname_controls {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 10vh;\r\n  display: flex;\r\n  align-items: center;\r\n  padding-top: 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n\r\n.story_swiper_classname_buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.story_swiper_classname_arrow {\r\n  background: transparent;\r\n  border: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.story_swiper_classname_icon {\r\n  font-size: 24px;\r\n  color: black;\r\n  background-color: white;\r\n}\r\n\r\n.story_swiper_classname_counter {\r\n  font-size: 1.2rem;\r\n  margin-left: 1rem;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n/* ///////////////////////////////////social card/////////////////////////////////////// */\r\n\r\n.story_social_container {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px 0;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n}\r\n\r\n.story_social_wrapper {\r\n  width: 100%;\r\n  /* padding: 0 5%; */\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n\r\n.story_social_section {\r\n  width: 100%;\r\n  min-height: 5vh;\r\n  padding: 10px 0;\r\n  border-top: 1px solid var(--line-color);\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_social_section {\r\n    min-height: 10vh;\r\n    flex-direction: row;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.story_social_label {\r\n  width: 100%;\r\n  min-height: 5vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n  text-transform: uppercase;\r\n  font-size: 12px;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_social_label {\r\n    width: 15%;\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n.story_social_buttons {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_social_buttons {\r\n    width: 85%;\r\n  }\r\n}\r\n\r\n.story_social_author {\r\n  width: 100%;\r\n  text-transform: uppercase;\r\n  font-size: 12px;\r\n  line-height: 1.1;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_social_author {\r\n    width: 85%;\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n.story_social_bio {\r\n  /* margin-top: 10px; */\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  text-transform: none;\r\n  font-size: 1.2rem;\r\n}\r\n.border_btn_button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n.border_btn_button svg {\r\n  font-size: 1.2rem;\r\n}\r\n\r\n/* ////////////////////////////////////////border btn /////////////////////////////////////// */\r\n\r\n.border_btn {\r\n  display: inline-block;\r\n}\r\n\r\n.border_btn_button {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n  text-transform: uppercase;\r\n  border: 1px solid var(--text-color);\r\n  color: var(--text-color);\r\n  background-color: transparent;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.border_btn_button:hover {\r\n  background-color: var(--text-color);\r\n  color: var(--body-bg-color);\r\n  border-color: var(--body-bg-color);\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .border_btn_button {\r\n    padding: 8px 16px;\r\n    font-size: 0.875rem;\r\n  }\r\n}\r\n\r\n.container_fullImage {\r\n  /* padding: 20px; */\r\n  max-width: 100vw;\r\n  flex: 1;\r\n  margin: 0 auto;\r\n  position: relative;\r\n}\r\n.full-image-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  /* aspect-ratio: 1; */\r\n}\r\n\r\n.full-image-wrapper img {\r\n  width: 100%;\r\n  height: auto;\r\n  object-fit: cover;\r\n  object-position: center;\r\n}\r\n\r\n/* Share Modal Css */\r\n\r\n.share__modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  transform: scale(1.1);\r\n  opacity: 0;\r\n  pointer-events: none;\r\n  visibility: hidden;\r\n  transition: visibility 0s linear 0.3s, opacity 0.3s 0s, transform 0.3s;\r\n  z-index: 9999;\r\n}\r\n.share-close-btn {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  cursor: pointer;\r\n  font-size: 23px;\r\n  color: #000;\r\n}\r\n.share__modal.show {\r\n  opacity: 1;\r\n  pointer-events: all;\r\n  visibility: visible;\r\n  transform: scale(1);\r\n  transition: visibility 0s linear 0s, opacity 0.25s 0s, transform 0.25s;\r\n}\r\n.share__content {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background-color: white;\r\n  width: 30rem;\r\n  /* height: 20rem; */\r\n  /* overflow-y: scroll; */\r\n  padding: 30px;\r\n  border-radius: 10px;\r\n  text-align: left;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n.share_body {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30px;\r\n  position: relative;\r\n}\r\n.share_body h2 {\r\n  font-size: 2.1rem;\r\n  color: #000000;\r\n  text-align: center;\r\n  line-height: 1;\r\n  font-family: rocky, sans-serif;\r\n  font-weight: 400;\r\n}\r\n\r\n.share__icons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  justify-content: center;\r\n}\r\n.share_body h3 {\r\n  color: gray;\r\n  font-weight: 300;\r\n  font-size: 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  margin-bottom: 0.5rem;\r\n}\r\n#check-group {\r\n  animation: 0.32s ease-in-out 1.03s check-group;\r\n  transform-origin: center;\r\n}\r\n\r\n#check-group #check {\r\n  animation: 0.34s cubic-bezier(0.65, 0, 1, 1) 0.8s forwards check;\r\n  stroke-dasharray: 0, 75px;\r\n  stroke-linecap: round;\r\n  stroke-linejoin: round;\r\n}\r\n\r\n#check-group #outline {\r\n  animation: 0.38s ease-in outline;\r\n  transform: rotate(0deg);\r\n  transform-origin: center;\r\n}\r\n\r\n#check-group #white-circle {\r\n  animation: 0.35s ease-in 0.35s forwards circle;\r\n  transform: none;\r\n  transform-origin: center;\r\n}\r\n.copy_text_anim {\r\n  text-align: center;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  color: var(--text-colorblack);\r\n}\r\n\r\n.StoriesInfo_left_innercntr-full-width.embed-twitter {\r\n  position: relative;\r\n  aspect-ratio: 16/9;\r\n}\r\n.flex-all-embed {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n@keyframes outline {\r\n  from {\r\n    stroke-dasharray: 0, 345.576px;\r\n  }\r\n  to {\r\n    stroke-dasharray: 345.576px, 345.576px;\r\n  }\r\n}\r\n@keyframes circle {\r\n  from {\r\n    transform: scale(1);\r\n  }\r\n  to {\r\n    transform: scale(0);\r\n  }\r\n}\r\n@keyframes check {\r\n  from {\r\n    stroke-dasharray: 0, 75px;\r\n  }\r\n  to {\r\n    stroke-dasharray: 75px, 75px;\r\n  }\r\n}\r\n@keyframes check-group {\r\n  from {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.09);\r\n  }\r\n  to {\r\n    transform: scale(1);\r\n  }\r\n}\r\n.share__icon {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background: #f3f3f3;\r\n  border-radius: 50%;\r\n  width: 58px;\r\n  height: 58px;\r\n  color: #333;\r\n\r\n  transition: background 0.3s, color 0.3s;\r\n}\r\n.share__icon svg {\r\n  font-size: 22px;\r\n}\r\n.share__icon:hover {\r\n  background: rgba(0, 0, 0, 1);\r\n  color: #fff;\r\n}\r\n\r\n.share__icon button {\r\n  all: unset;\r\n  cursor: pointer;\r\n}\r\n.link_copycntr {\r\n  width: 100%;\r\n  padding: 10px;\r\n  background-color: #f3f3f3;\r\n  border-radius: 5px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 10px;\r\n}\r\n.link_copycntr input {\r\n  width: 100%;\r\n  background: none;\r\n  outline: none;\r\n  border: none;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  color: var(--text-colorblack);\r\n}\r\n.link_copycntr svg {\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: var(--text-colorblack);\r\n}\r\n@media only screen and (max-width: 992px) {\r\n  .share-close-btn {\r\n    top: -15px;\r\n    right: -15px;\r\n  }\r\n  .card-meta_meta {\r\n    row-gap: 16px;\r\n  }\r\n  .contr-fluid {\r\n    gap: 1em;\r\n  }\r\n  .Stories_caption {\r\n    font-size: 0.875rem;\r\n  }\r\n  .Stories_courtesy {\r\n    font-size: 0.875rem;\r\n  }\r\n}\r\n@media only screen and (max-width: 767px) {\r\n  .share__content {\r\n    width: 85vw;\r\n  }\r\n  .share_body h2 {\r\n    font-size: 2rem;\r\n  }\r\n  .share__icon {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n  .share__icon svg {\r\n    font-size: 18px;\r\n  }\r\n  .share__icons {\r\n    justify-content: space-around;\r\n  }\r\n}\r\n@media only screen and (max-width: 425px) {\r\n  .share_body h2 {\r\n    font-size: 1.5rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAIA;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;AAgBA;;;;;;AASA;EACE;;;;;;AAKF;EACE;;;;;;AAKF;EACE;;;;;AAKF;;;;;;;;AAOA;;;;;;;;;AAQA;;;;;;;;;;;AAUA;;;;;;;;AAOA;;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;EACE;;;;EAGA;;;;EAGA;;;;EAIA;;;;;AAMF;EACE;;;EAGA;;;;;;EAKA;;;;;;;EAOA;;;;;;EAMA;;;;;EAKA;;;;EAGA;;;;;AAIF;EACE;;;;;EAIA;;;;EAGA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAOF;;;AAIA;;;AAGA;;;AAGA;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;AAOA;;;;;AAKA;;;;;;;;;;;AAWA;;;;AAGA;EACE;;;;EAGA;;;;;EAIA;;;;;;AAMF;;;;;AAKA;EACE;;;;;;AAMF;;;;;;;;;;;AAcA;;;;;;;;;AAUA;EACE;;;;;;;;;EAUA;;;;;;;AAOF;;;;;;;;AASA;;;;AAIA;EACE;;;;;;;AAOF;;;;;AAKA;EACE;;;;;;AAKF;EACE;;;;;;AAKF;;;;;;AAMA;;;;;AAIA;;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;AAUA;;;;;;;;;;;AAUA;;;;;;;;AAOA;;;;;;;;;;;;;AAYA;;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;AAKA;;;;AAIA;EACE;;;;;;EAKA;;;;;AAIF;EACE;;;;EAGA;;;;;AAIF;EACE;;;;;;EAKA;;;;;AAIF;EACE;;;;;AAIF;;;;;AAKA;EACE;;;;;;AAKF;EACE;;;;;AAIF;;;;AAIA;EACE;;;;;AAOF;;;;;AAKA;;;;AAIA;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;AAMA;;;;;AAMA;;;;;;;AAOA;;;;;AAMA;EACE;;;;;AAKF;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAQA;;;;;;;;;AASA;;;;;AAMA;;;;;;;;;AASA;EACE;;;;;;;AAOF;;;;;;;;;;AAUA;EACE;;;;;;AAMF;;;;;;;;AAQA;EACE;;;;;AAKF;;;;;;;AAOA;EACE;;;;;;AAMF;;;;;;;;AAQA;;;;;;AAKA;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;AAMA;EACE;;;;;;AAMF;;;;;;;AAOA;;;;;;AAOA;;;;;;;AASA;;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;AAQA;;;;;;;;AAOA;;;;;;;;;;;;;AAcA;;;;;;;AAMA;;;;;;;;;AASA;;;;;;;AAMA;;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;AAKA;;;;;AAIA;;;;;;;;;;;AAUA;;;;;;;;;AAQA;;;;;;AAKA;EACE;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;AAIF;EACE", "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3538, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/navbar.css"], "sourcesContent": [".nav {\r\n  position: relative;\r\n  padding: 2rem 3rem;\r\n  /* background-color: var(--black); */\r\n  color: var(--text-color);\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s, color 0.45s ease-in 0.5s;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 999;\r\n}\r\n.brand {\r\n  cursor: pointer;\r\n  justify-self: left;\r\n  font-size: 2vw;\r\n  font-family: rocky, sans-serif;\r\n  display: flex;\r\n  /* margin-left: 15px; */\r\n}\r\n.brand img {\r\n  /* aspect-ratio: 1; */\r\n  width: 200px;\r\n  height: auto;\r\n  filter: var(--filterblack);\r\n  transition: filter 0.45s ease-in 0.5s;\r\n\r\n  /* fill: var(--text-color); */\r\n}\r\n.nav-content {\r\n  /* display: grid; */\r\n  /* grid-template-rows: minmax(min-content, 60px); */\r\n\r\n  /* grid-template-columns: 1fr 3fr 1fr; */\r\n  align-items: center;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  height: 2.5rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 1;\r\n  overflow: hidden;\r\n}\r\n.links.nav-items {\r\n  /* display: flex;\r\n  justify-content: space-between; */\r\n  cursor: pointer;\r\n  /* font-weight: 400; */\r\n  /* font-size: 1.2rem; */\r\n  text-transform: uppercase;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in;\r\n  display: none;\r\n}\r\n.links.nav-items ul {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n.links.nav-items .menu-linksul {\r\n  display: flex;\r\n  text-transform: uppercase;\r\n  width: 100%;\r\n  flex-direction: column;\r\n  padding: 0.625rem;\r\n}\r\n.links.nav-items li:not(:last-of-type) {\r\n  margin-bottom: 0.625rem;\r\n}\r\n.links.nav-items .menu-linksul a {\r\n  font-size: 1vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 23px;\r\n  letter-spacing: 0.08rem;\r\n}\r\n\r\n.menu {\r\n  display: none;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  transition: color 0.45s ease-in;\r\n}\r\n.toggle_cntr {\r\n  position: relative;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n.toggle_cntr:hover{\r\n  scale: 0.92;\r\n}\r\n/* For the icons */\r\n.toggle_cntr svg {\r\n  transition: all 0.3s ease;\r\n  /* mix-blend-mode: difference;\r\n  color: var(--white); */\r\n}\r\n.icon {\r\n  display: none;\r\n  transition: transform 0.45s ease; /* Smooth scaling effect */\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  padding: 8px;\r\n  border: 1px solid black;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  background-color: white;\r\n  color: black;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute;\r\n}\r\n\r\n.toggle_cntr .icon:hover svg {\r\n  rotate: 25deg;\r\n}\r\n\r\n.icon.scale-0 {\r\n  transform: scale(0);\r\n}\r\n\r\n.icon.scale-1 {\r\n  transform: scale(1);\r\n}\r\n/* Ensure the menu item is positioned correctly */\r\n.menu-item {\r\n  position: relative; /* For the pseudo-element positioning */\r\n}\r\n\r\n/* Active state: when the link is active */\r\n.nav_active {\r\n  position: relative; /* Ensure relative positioning */\r\n}\r\n\r\n/* Create the pseudo-element for the border */\r\n.menu-item a::after {\r\n  content: \"\"; /* Required for the pseudo-element */\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 0%; /* Always 100% for active link */\r\n  height: 1px; /* Height of the border */\r\n  background-color: red; /* Border color */\r\n  transition: width 0.45s ease; /* Smooth transition of width */\r\n}\r\n.menu-item a.nav_active::after {\r\n  width: 100% !important;\r\n}\r\n.menu-item a:hover::after {\r\n  width: 100%;\r\n}\r\n\r\n@media only screen and (min-width: 61.25rem) {\r\n  .nav {\r\n    padding: 2rem 2rem;\r\n    border-bottom: none;\r\n  }\r\n  .links.nav-items {\r\n    display: flex;\r\n    flex-basis: 80%;\r\n  }\r\n  .links.nav-items .menu-linksul {\r\n    align-items: center;\r\n    /* justify-content: space-between; */\r\n    justify-content: center;\r\n    flex-direction: row;\r\n    padding: 0;\r\n    gap: 1.5rem;\r\n  }\r\n  .links.nav-items li:not(:last-of-type) {\r\n    margin: 0;\r\n  }\r\n  .brand img {\r\n    width: 200px;\r\n    height: auto;\r\n  }\r\n\r\n  .toggle_cntr .icon svg {\r\n    font-size: 25px;\r\n  }\r\n  .toggle_cntr .icon:nth-child(1) svg {\r\n    font-size: 30px;\r\n  }\r\n}\r\n@media only screen and (max-width: 61.1875rem) {\r\n  .nav {\r\n    padding: 1rem 1rem;\r\n  }\r\n  .menu {\r\n    display: flex;\r\n  }\r\n  .brand img {\r\n    width: 160px;\r\n    height: auto;\r\n  }\r\n  .brand {\r\n    order: 2;\r\n  }\r\n  .material-icons.menu {\r\n    font-size: 25px;\r\n    cursor: pointer;\r\n    line-height: 1;\r\n    order: 3;\r\n  }\r\n  .toggle_cntr svg {\r\n    font-size: 25px;\r\n  }\r\n  .toggle_cntr {\r\n    order: 1;\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n@media screen and (max-width: 1200px) {\r\n  .links.nav-items .menu-linksul a {\r\n    font-size: 1.2vw;\r\n  }\r\n  /* .login {\r\n    font-size: 1.25vw;\r\n  } */\r\n}\r\n@media only screen and (max-width: 768px) {\r\n  /* .nav {\r\n    padding: 1rem 1rem;\r\n  } */\r\n  /* .nav-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  } */\r\n  /* .nav-content .links {\r\n    display: none;\r\n  }\r\n  .menu {\r\n    display: initial;\r\n  } */\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;AASA;;;;;;;;;;AAaA;;;;;;;;AAWA;;;;;;AAKA;;;;;;;;AAOA;;;;AAGA;;;;;;;AAOA;;;;;;AAKA;;;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAKA;;;;;;;;;;;AAUA;;;;AAGA;;;;AAIA;EACE;;;;;EAIA;;;;;EAIA;;;;;;;;EAQA;;;;EAGA;;;;;EAKA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;AAKF;EACE;;;;;AAOF", "debugId": null}}, {"offset": {"line": 3767, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3770, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/footer.css"], "sourcesContent": [".footer {\r\n  background-color: var(--black);\r\n}\r\n.wrapper_footer {\r\n  padding-top: 0;\r\n  padding-bottom: 2.5rem;\r\n}\r\n.dest_news {\r\n  border-bottom: 1px solid #f1f1f126;\r\n  width: 100%;\r\n  /* height: 80vh; */\r\n  display: flex;\r\n}\r\n.dest_ft {\r\n  border-right: 1px solid #f1f1f126;\r\n  width: 50%;\r\n  height: 100%;\r\n}\r\n\r\n.btn_wrapper {\r\n  border-top: 1px solid #f1f1f126;\r\n  padding: 16px 32px;\r\n  border-bottom: 1px solid #f1f1f126;\r\n}\r\n.btn_wrapper.expobtn {\r\n  padding: 16px 32px 16px 0;\r\n}\r\n.btn_wrapper.subbtn {\r\n  padding: 16px 0 16px 16;\r\n}\r\n\r\n.news_ft {\r\n  width: 50%;\r\n  height: 100%;\r\n}\r\n.dt_nw_wrapper {\r\n  flex-flow: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  /* padding: 52px 0 0; */\r\n  display: flex;\r\n}\r\n\r\n.footer_button {\r\n  background-color: var(--white);\r\n  color: var(--black);\r\n  letter-spacing: 1px;\r\n  border-radius: 2px;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: auto;\r\n  height: 52px;\r\n  margin-left: auto;\r\n  padding: 8px 62px 8px;\r\n  transition: all 0.4s;\r\n  display: flex;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n}\r\n.w-form {\r\n  margin: 0 0 15px;\r\n}\r\n.form-block {\r\n  margin-bottom: 0;\r\n}\r\n.subscribe_flex {\r\n  justify-content: space-around;\r\n  display: flex;\r\n}\r\n.w-form-done {\r\n  text-align: center;\r\n  background-color: #ddd;\r\n  padding: 20px;\r\n  display: none;\r\n}\r\n.subscribe_thanks {\r\n  background-color: var(--white);\r\n  border-radius: 2px;\r\n}\r\n.w-form-fail {\r\n  background-color: #ffdede;\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  display: none;\r\n}\r\n.error_state {\r\n  letter-spacing: 1px;\r\n\r\n  background-color: #fc5656;\r\n  border-radius: 3px;\r\n  width: 80%;\r\n  margin-top: 12px;\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n  padding: 7px 12px 6px;\r\n  font-size: 0.9em;\r\n  line-height: 1.2;\r\n  position: static;\r\n}\r\n.w-input,\r\n.w-select {\r\n  color: #333;\r\n  vertical-align: middle;\r\n  background-color: #fff;\r\n  border: 1px solid #ccc;\r\n  width: 100%;\r\n  height: 38px;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  font-size: 14px;\r\n  line-height: 1.42857;\r\n  display: block;\r\n}\r\n.field_prefooter {\r\n  color: var(--white);\r\n  letter-spacing: 0.03em;\r\n  background-color: #fff0;\r\n  border: 1px solid #f1f1f140;\r\n  border-radius: 3px;\r\n  width: 100%;\r\n  height: 52px;\r\n  margin-bottom: 0;\r\n  margin-right: 8px;\r\n  padding: 8px 16px 4px;\r\n  transition: all 0.4s;\r\n}\r\ninput.w-button {\r\n  -webkit-appearance: button;\r\n  cursor: pointer;\r\n}\r\n.footer_links {\r\n  padding-top: 0;\r\n}\r\n.wrapper_footer_links {\r\n  padding-top: 32px;\r\n  /* padding-left: 32px; */\r\n  /* padding-right: 32px; */\r\n}\r\n.flexbox_footer {\r\n  display: flex;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.footer-left-block {\r\n  width: 50%;\r\n}\r\n.footer-right-block {\r\n  width: 50%;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  /* gap: 10%; */\r\n}\r\n.div-block-2 {\r\n  width: 100%;\r\n}\r\n.title_footer {\r\n  opacity: 0.65;\r\n  color: var(--white);\r\n  text-transform: uppercase;\r\n  margin-bottom: 16px;\r\n  font-size: 0.9em;\r\n  letter-spacing: 1px;\r\n}\r\n.links_flex {\r\n  grid-column-gap: 4px;\r\n  grid-row-gap: 4px;\r\n  flex-flow: column;\r\n  justify-content: flex-start;\r\n  align-items: flex-start;\r\n  display: flex;\r\n}\r\n.footer_link {\r\n  color: var(--white);\r\n  letter-spacing: 1px;\r\n  font-size: 0.9em;\r\n  transition: all 0.4s;\r\n}\r\n.last_line {\r\n  margin-top: 8em;\r\n}\r\n.flexbox_line {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.left_rights {\r\n  width: 30%;\r\n  margin-right: 0;\r\n}\r\n.footer_link:hover,\r\n.footer_link.just_rights {\r\n  opacity: 0.65;\r\n}\r\n.privacy_box {\r\n  width: 25%;\r\n  margin-right: 0;\r\n  display: flex;\r\n  grid-column-gap: 15px;\r\n  grid-row-gap: 15px;\r\n  justify-content: flex-end;\r\n}\r\n.website_by {\r\n  justify-content: flex-end;\r\n  align-items: flex-start;\r\n  width: 25%;\r\n  margin-left: auto;\r\n  display: flex;\r\n}\r\n.footer_link.web_by {\r\n  opacity: 0.65;\r\n}\r\n.footer-brand {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 55px;\r\n  overflow: hidden;\r\n}\r\n.footer-brand img {\r\n  /* aspect-ratio: 1; */\r\n  /* width: 200px;\r\n  height: auto; */\r\n  object-fit: cover;\r\n  filter: invert();\r\n  transition: filter 0.45s ease-in 0.5s;\r\n\r\n  /* fill: var(--text-color); */\r\n}\r\n/* Sub Footer */\r\n.sub-footer {\r\n  background-color: var(--gray-bg);\r\n}\r\n.sub-footer > .containerWrapper {\r\n  padding: 10px 0;\r\n}\r\n.flexbox_sub-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  /* height: 60px; */\r\n}\r\n.footer-tag-line {\r\n  color: var(--white);\r\n  letter-spacing: 1px;\r\n  font-size: 0.8em;\r\n  transition: all 0.4s;\r\n}\r\n.footer-vr-line {\r\n  display: inline-block;\r\n  align-self: stretch;\r\n  background: #fff;\r\n  width: 1px;\r\n  height: 100%;\r\n  opacity: 0.7;\r\n  margin: auto 15px;\r\n  min-height: 2.75em;\r\n}\r\n.sub-footer-img-block {\r\n  position: relative;\r\n  width: 180px;\r\n  min-width: 180px;\r\n  height: 5vw;\r\n  overflow: hidden;\r\n}\r\n.sub-footer-img-block-2 {\r\n  position: relative;\r\n  width: 80px;\r\n  min-width: 80px;\r\n  height: 5vw;\r\n  overflow: hidden;\r\n}\r\n.sub-footer-img-block img,\r\n.sub-footer-img-block-2 img {\r\n  object-fit: contain;\r\n}\r\n@media screen and (max-width: 991px) {\r\n  /* .dest_news {\r\n    height: 65vh;\r\n  } */\r\n  .btn_wrapper {\r\n    padding-left: 24px;\r\n    padding-right: 24px;\r\n  }\r\n  .footer_button {\r\n    padding-left: 32px;\r\n    padding-right: 32px;\r\n    font-size: 1em;\r\n  }\r\n  .field_prefooter {\r\n    font-size: 1em;\r\n  }\r\n  .subscribe_thanks {\r\n    border-radius: 2px;\r\n  }\r\n  .error_state {\r\n    padding-top: 10px;\r\n    font-size: 1.5em;\r\n  }\r\n  .thanks_txt {\r\n    font-size: 1.5em;\r\n    line-height: 1.1;\r\n  }\r\n  .wrapper_footer_links {\r\n    padding-top: 32px;\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n  }\r\n  .title_footer {\r\n    letter-spacing: 1px;\r\n\r\n    font-size: 0.9em;\r\n  }\r\n  .footer_link {\r\n    font-size: 1em;\r\n  }\r\n  .last_line {\r\n    margin-top: 25em;\r\n  }\r\n  .privacy_box {\r\n    grid-column-gap: 15px;\r\n    grid-row-gap: 15px;\r\n    width: 50%;\r\n    display: flex;\r\n  }\r\n  .website_by {\r\n    width: 20%;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .wrapper_footer {\r\n    padding-bottom: 20px;\r\n  }\r\n  .dest_news {\r\n    flex-flow: column;\r\n    height: auto;\r\n  }\r\n  .dest_ft,\r\n  .news_ft {\r\n    width: 100%;\r\n  }\r\n  .btn_wrapper {\r\n    /* margin-top: 6em; */\r\n    padding-left: 20px;\r\n    padding-right: 20px;\r\n  }\r\n  .footer_button {\r\n    font-size: 0.9em;\r\n  }\r\n  .field_prefooter {\r\n    font-size: 0.8em;\r\n  }\r\n  .subscribe_thanks {\r\n    border-radius: 2px;\r\n  }\r\n  .error_state {\r\n    font-size: 2em;\r\n  }\r\n  .thanks_txt {\r\n    font-size: 1em;\r\n  }\r\n  .wrapper_footer_links {\r\n    padding-top: 32px;\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n  }\r\n  .flexbox_footer {\r\n    flex-direction: column;\r\n  }\r\n  .footer-left-block {\r\n    width: 100%;\r\n    margin-bottom: 30px;\r\n  }\r\n  .footer-right-block {\r\n    width: 100%;\r\n    display: grid;\r\n    justify-content: flex-start;\r\n    grid-column-gap: 0px;\r\n    grid-row-gap: 52px;\r\n    grid-template-rows: auto auto;\r\n    grid-template-columns: 1fr 1fr;\r\n    grid-auto-columns: 1fr;\r\n  }\r\n  .sub-footer-img-block,\r\n  .sub-footer-img-block-2 {\r\n    width: 100%;\r\n  }\r\n  .footer-hr-line {\r\n    display: inline-block;\r\n    align-self: stretch;\r\n    background: #fff;\r\n    width: 100%;\r\n    height: 1px;\r\n    opacity: 0.7;\r\n    margin: 15px auto;\r\n  }\r\n\r\n  .flexbox_sub-footer {\r\n    flex-wrap: wrap;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .sub-footer-img-block {\r\n    order: 1;\r\n    width: calc(50% - (0.5px + 5px));\r\n    min-width: calc(50% - (0.5px + 5px));\r\n    height: 50px;\r\n  }\r\n\r\n  .footer-vr-line:first-of-type {\r\n    order: 2;\r\n    height: 100%;\r\n    width: 1px;\r\n    margin: 0;\r\n  }\r\n\r\n  .sub-footer-img-block-2 {\r\n    order: 3;\r\n    width: calc(50% - (0.5px + 5px));\r\n    min-width: calc(50% - (0.5px + 5px));\r\n    height: 50px;\r\n  }\r\n\r\n  .footer-vr-line:last-of-type {\r\n    order: 4;\r\n    width: 100%;\r\n    height: 1px;\r\n    min-height: 1px;\r\n    margin: 15px auto;\r\n  }\r\n\r\n  .footer-tag-line {\r\n    order: 5;\r\n    width: 100%;\r\n  }\r\n  /* .div-block-2 {\r\n    width: 50%;\r\n  } */\r\n  /* .div-block-3 {\r\n    width: 50%;\r\n  } */\r\n  .title_footer {\r\n    letter-spacing: 1px;\r\n\r\n    font-size: 1em;\r\n  }\r\n  .links_flex {\r\n    grid-column-gap: 4px;\r\n    grid-row-gap: 4px;\r\n  }\r\n  .footer_link {\r\n    letter-spacing: 1px;\r\n\r\n    padding-top: 0.2em;\r\n    font-size: 1em;\r\n  }\r\n  .last_line {\r\n    margin-top: 20em;\r\n  }\r\n  .flexbox_line {\r\n    flex-flow: column;\r\n  }\r\n  .privacy_box {\r\n    grid-column-gap: 16px;\r\n    grid-row-gap: 16px;\r\n    width: 100%;\r\n    margin-top: 8px;\r\n    display: flex;\r\n    justify-content: flex-start;\r\n  }\r\n  .website_by {\r\n    justify-content: flex-start;\r\n    align-items: flex-start;\r\n    width: 50%;\r\n    margin-top: 32px;\r\n    margin-left: 0;\r\n  }\r\n\r\n  .footer_link.web_by {\r\n    padding-top: 0.2em;\r\n  }\r\n}\r\n@media screen and (max-width: 479px) {\r\n  .footer-brand {\r\n    position: relative;\r\n    width: 180px;\r\n    height: 40px;\r\n    overflow: hidden;\r\n  }\r\n  .div-block-3 {\r\n    width: 100%;\r\n  }\r\n  .left_rights {\r\n    width: 100%;\r\n  }\r\n  .wrapper_footer_links {\r\n    padding-top: 32px;\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n  }\r\n  .flexbox_footer {\r\n    flex-direction: column;\r\n  }\r\n  .footer-left-block {\r\n    width: 100%;\r\n    margin-bottom: 30px;\r\n  }\r\n  .footer-right-block {\r\n    width: 100%;\r\n    display: grid;\r\n    justify-content: flex-start;\r\n    grid-column-gap: 20px;\r\n    grid-row-gap: 32px;\r\n    grid-template-rows: auto auto;\r\n    grid-template-columns: 1fr 1fr;\r\n    grid-auto-columns: 1fr;\r\n  }\r\n  .sub-footer-img-block,\r\n  .sub-footer-img-block-2 {\r\n    height: 40px;\r\n  }\r\n  .last_line {\r\n    margin-top: 1em;\r\n  }\r\n  .website_by {\r\n    justify-content: flex-start;\r\n    align-items: flex-start;\r\n    width: 100%;\r\n    margin-top: 52px;\r\n    margin-left: 0;\r\n  }\r\n  .footer_link.web_by {\r\n    text-decoration: underline;\r\n  }\r\n  .btn_wrapper.subbtn {\r\n    padding: 16px 0 16px 0px;\r\n  }\r\n  .btn_wrapper.expobtn {\r\n    padding: 16px 0px 16px 0;\r\n  }\r\n  .title_footer {\r\n    font-size: 0.9em;\r\n  }\r\n  .footer_link {\r\n    font-size: 0.9em;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAGA;;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;AAIA;;;;;AAIA;;;;;;;AAQA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;AAaA;;;;;AAIA;;;;AAGA;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;AAMA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;AAIA;;;;;;;;;AAQA;;;;;;;;AAOA;;;;AAGA;;;;;;;AAMA;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;;AAUA;;;;;;;;AAOA;;;;;;;;AAOA;;;;AAIA;EAIE;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;AAIF;EACE;;;;EAGA;;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAIA;;;;;;;;;;EAUA;;;;;;EAMA;;;;;;;EAOA;;;;;;;EAOA;;;;;;;EAOA;;;;;;;;EAQA;;;;;EAUA;;;;;EAKA;;;;;EAIA;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;;;;;EAQA;;;;;AAIF;EACE;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAIA;;;;EAGA;;;;;;;;EAOA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA", "debugId": null}}, {"offset": {"line": 4375, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4378, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/mobileMenu.css"], "sourcesContent": [".menu-cont {\r\n  position: fixed;\r\n  top: 0px;\r\n  height: 100dvh;\r\n  width: 100vw;\r\n  /* pointer-events: none; */\r\n  z-index: 999;\r\n  transition: 0.4s;\r\n  display: none;\r\n}\r\n.menu-inner {\r\n  width: 100%;\r\n  margin-left: auto;\r\n  background-color: rgb(0, 0, 0);\r\n}\r\n.manu-inner-block,\r\n.menu-inner {\r\n  height: 100dvh;\r\n  transition: 0.3s ease-out;\r\n}\r\n.manu-inner-block {\r\n  width: 100%;\r\n}\r\n.menu-top {\r\n  display: flex;\r\n  height: 80px;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 15px;\r\n  padding: 3vh 4vw;\r\n}\r\n.flex-all {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.menu-back {\r\n  position: relative;\r\n  width: 40px;\r\n  height: 40px;\r\n  font-size: 20px;\r\n  background-color: rgb(255, 255, 255);\r\n  transition: 0.3s;\r\n  border-radius: 50%;\r\n}\r\n.menu-close {\r\n  width: 40px;\r\n  height: 40px;\r\n  font-size: 19px;\r\n  background-color: rgb(255, 255, 255);\r\n  transition: 0.3s;\r\n  border-radius: 50%;\r\n}\r\n.menu-close svg {\r\n  color: #000 !important;\r\n}\r\n.menu-back svg {\r\n  color: #000 !important;\r\n}\r\n.menu-main {\r\n  position: relative;\r\n  flex-direction: column;\r\n  align-items: normal;\r\n  height: calc(100dvh - (80px + 60px));\r\n  width: 100%;\r\n  overflow: auto;\r\n  padding: 3vh 4vw;\r\n}\r\n.menu-extras,\r\n.menu-main {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n.menu-items {\r\n  height: 50px;\r\n  color: rgb(255, 255, 255);\r\n  width: 100%;\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.menu-name {\r\n  font-size: 1.25rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 500;\r\n  letter-spacing: 1px;\r\n  text-transform: uppercase;\r\n  width: 100%;\r\n  display: inline-block;\r\n}\r\n.menu-arrow {\r\n  position: absolute;\r\n  right: 0px;\r\n  font-size: 18px;\r\n  transition: 0.3s;\r\n}\r\n.menu-extras {\r\n  align-items: flex-end;\r\n}\r\n.menu-ext {\r\n  display: flex;\r\n  align-items: center;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 16px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 400;\r\n  letter-spacing: 1px;\r\n  margin: 8px 0px;\r\n  transition: 0.3s;\r\n}\r\n.menu-btm {\r\n  width: 100%;\r\n  position: relative;\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin: auto;\r\n  gap: 25px;\r\n  padding: 0vh 4vw;\r\n  border-top: 1px solid rgb(255, 255, 255);\r\n}\r\n.menu-follows {\r\n  margin-left: 10px;\r\n}\r\n.menu-follows .menu-follows-items {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n.menu-follows .menu-follows-items a {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: auto;\r\n}\r\n.menu-follows .menu-follows-items svg {\r\n  font-size: 18px;\r\n}\r\n.menu-follows-text {\r\n}\r\n.d-none {\r\n  display: none !important;\r\n}\r\n.d-block {\r\n  display: block !important;\r\n}\r\n.submenu-head,\r\n.submenu-items {\r\n  color: rgb(255, 255, 255);\r\n  width: 100%;\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.submenu-head {\r\n  /* height: 50px; */\r\n}\r\n.submenu-items {\r\n  margin-bottom: 16px;\r\n}\r\n.submenu-name,\r\n.submenu-title {\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 500;\r\n  letter-spacing: 1px;\r\n  line-height: 1.5;\r\n  text-transform: uppercase;\r\n}\r\n.submenu-name {\r\n  font-size: 20px;\r\n  gap: 15px;\r\n  color: rgba(224, 224, 224, 0.75) !important;\r\n  width: 100%;\r\n  display: inline-block;\r\n}\r\n.submenu-title {\r\n  font-size: 26px;\r\n  color: #fff !important;\r\n  width: 100%;\r\n  line-height: 1.2;\r\n  margin-bottom: 20px;\r\n}\r\n@media (max-width: 61.1875rem) {\r\n  .menu-cont {\r\n    display: flex;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAUA;;;;;;AAKA;;;;;AAKA;;;;AAGA;;;;;;;;;AAQA;;;;;;;AAMA;;;;;;;;;;AASA;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;AAMA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;AAGA;;;;;;AAKA;;;;;;;AAMA;;;;AAGA;;;AAEA;;;;AAGA;;;;AAGA;;;;;;;;;AASA;;;AAGA;;;;AAGA;;;;;;;;AAQA;;;;;;;;AAOA;;;;;;;;AAOA;EACE", "debugId": null}}, {"offset": {"line": 4593, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4596, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/herosectionbanner.css"], "sourcesContent": ["/* ._bgCntr {\r\n  background-color: var(--body-bg-color);\r\n} */\r\n.fullWidthCntr {\r\n  position: relative;\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n  /* padding-top: 1rem; */\r\n}\r\n.editor-picks__primary-pick {\r\n  width: 100%;\r\n  padding: 1.25rem;\r\n  padding-bottom: 0;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n.editor-picks__primary-pick .post-meta {\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  align-items: flex-start;\r\n  justify-content: center;\r\n  padding-bottom: 35px;\r\n}\r\n.editor-picks__primary-pick .featured-image {\r\n  position: relative;\r\n  margin-bottom: 1.25rem;\r\n  width: 100%;\r\n  aspect-ratio: 16/9;\r\n  overflow: hidden;\r\n  /* transform: translateX(-25px); */\r\n}\r\n.featured-image a {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n.editor-picks__primary-pick .featured-image img {\r\n  object-fit: cover;\r\n}\r\n.editor-picks__primary-pick .entry {\r\n  padding: 0.9375rem 2.8125rem;\r\n  padding-bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n  margin-top: -50px;\r\n  position: relative;\r\n  z-index: 99;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n.editor-picks__primary-pick .entry__category {\r\n  font-size: 11px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 15px;\r\n  /* letter-spacing: 0.35px; */\r\n}\r\n.editor-picks__primary-pick .entry__heading {\r\n  font-size: 23px;\r\n  line-height: 28px;\r\n  text-transform: none;\r\n  margin-bottom: 0.625rem;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n  font-family: rocky, sans-serif;\r\n}\r\n.editor-picks__primary-pick .entry__excerpt {\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  margin-bottom: 1.25rem;\r\n  width: 100%;\r\n  letter-spacing: 0.37px;\r\n}\r\n.editor-picks__primary-pick .entry__excerpt {\r\n  font-size: 19px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  margin-bottom: 0.625rem;\r\n  font-weight: 300;\r\n  line-height: 1.1;\r\n}\r\n@media only screen and (min-width: 61.25rem) {\r\n  .editor-picks__primary-pick {\r\n    padding: 0;\r\n    text-align: left;\r\n  }\r\n  .editor-picks__primary-pick .featured-image {\r\n    position: relative;\r\n    width: 100%;\r\n    aspect-ratio: 16/9;\r\n    overflow: hidden;\r\n    margin-bottom: 0;\r\n    transform: none;\r\n  }\r\n  .editor-picks__primary-pick .entry__category {\r\n    font-size: 15px;\r\n    line-height: 22px;\r\n  }\r\n  .editor-picks__primary-pick .entry__heading {\r\n    font-size: 34px;\r\n    line-height: 41px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;;;AAMA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAMA;;;;AAGA;;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;AAOA;EACE;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA", "debugId": null}}, {"offset": {"line": 4709, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4712, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/threecardsection.css"], "sourcesContent": [".editor-picks__secondary-wrapper {\r\n  padding: 4rem 0 0;\r\n}\r\n\r\n.editor-picks__secondary-pick {\r\n  text-align: center;\r\n  margin-bottom: 1.25rem;\r\n  margin-top: 1.5625rem;\r\n}\r\n.editor-picks__secondary-wrapper .entry__heading {\r\n  color: var(--text-color);\r\n  transition: color 0.1s ease-in 0.5s;\r\n}\r\n.editor-picks__secondary-wrapper .featured-image:before {\r\n  display: block;\r\n  content: \" \";\r\n  width: 100%;\r\n  padding-top: 56.25%;\r\n}\r\n.editor-picks__secondary-wrapper .featured-image .image-wrapper {\r\n  overflow: hidden;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n.editor-picks__secondary-wrapper .editor-picks__secondary-pick .featured-image {\r\n  max-height: 300px !important;\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .editor-picks__secondary-wrapper {\r\n    padding: 0 0;\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick a {\r\n    display: flex;\r\n    border-top: 1px solid #e8e8e8;\r\n    padding-top: 25px;\r\n    box-sizing: border-box;\r\n    margin: 10px;\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {\r\n    text-align: start;\r\n    margin-left: 10px;\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .editor-picks__secondary-pick .entry .entry__category {\r\n    font-size: 11px;\r\n    order: 1;\r\n    margin-bottom: 5px;\r\n  }\r\n  .editor-picks__secondary-pick .entry .entry__heading {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n    order: 2;\r\n    margin-bottom: 5px;\r\n  }\r\n  .editor-picks__secondary-pick .entry .post-meta {\r\n    justify-content: flex-end;\r\n    order: 3;\r\n  }\r\n  .editor-picks__secondary-pick .entry .post-meta .post-meta__author {\r\n    font-size: 12px;\r\n  }\r\n  .editor-picks__secondary-pick .entry .post-meta .post-meta__timestamp {\r\n    display: none;\r\n  }\r\n  .editor-picks__primary-pick .entry {\r\n    padding: 0.8rem 0.9375rem 0;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .editor-picks__primary-pick .entry .entry__category {\r\n    order: 1;\r\n  }\r\n  .editor-picks__primary-pick .entry .entry__heading {\r\n    order: 2;\r\n  }\r\n  .editor-picks__primary-pick .entry__excerpt {\r\n    display: none;\r\n  }\r\n  .editor-picks__primary-pick .entry .entry__excerpt {\r\n    order: 3;\r\n  }\r\n  .editor-picks__primary-pick .entry .post-meta {\r\n    order: 4;\r\n    padding-bottom: 0px;\r\n  }\r\n  .editor-picks__primary-pick .entry .post-meta .post-meta__author {\r\n    font-size: 12px;\r\n  }\r\n  .editor-picks__primary-pick .entry .post-meta .post-meta__timestamp {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media only screen and (max-width: 61.1875rem) {\r\n  .editor-picks__secondary-wrapper\r\n    .editor-picks__secondary-pick\r\n    .featured-image {\r\n    max-width: 100%;\r\n    min-width: 35%;\r\n    overflow: hidden;\r\n    position: relative;\r\n  }\r\n  /* .editor-picks__secondary-pick .featured-image img {\r\n    display: block;\r\n    position: absolute;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    height: 100%;\r\n    width: auto;\r\n    max-width: 200%;\r\n  } */\r\n}\r\n@media only screen and (max-width: 450px) {\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {\r\n    margin-left: 15px;\r\n  }\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-inner-wrapper {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n  }\r\n  .editor-picks__secondary-pick {\r\n    width: calc(33.33333% - 0.83333rem);\r\n    margin-bottom: 0;\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick {\r\n    width: calc(33.33333% - 0.83333rem);\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick {\r\n    margin-bottom: 1.5625rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;AAQA;;;;AAGA;EACE;;;;EAGA;;;;;;;;EAOA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;;EAMA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;AAKF;EACE;;;;;;;;AAkBF;EACE;;;;;AAIF;EACE;;;;;;EAKA;;;;;EAIA;;;;EAGA", "debugId": null}}, {"offset": {"line": 4867, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4870, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/tworiversection.css"], "sourcesContent": [".two-rivers-wrapper {\r\n  font-size: 1.15rem;\r\n}\r\n.two-rivers-wrapper .sectioner {\r\n  margin-bottom: 0;\r\n}\r\n.sectioner--latest-stories {\r\n  width: 100%;\r\n  padding: 75px 0;\r\nbackground-color: var(--body-bg-color);\r\n}\r\n.sectioner--latest-stories .entry {\r\n  margin-left: 10px;\r\n}\r\n.latest-story.latest-story--primary .entry {\r\n  margin-left: 0px;\r\n}\r\n.sectioner--latest-stories .entry__category {\r\n  font-size: 11px;\r\n  line-height: 15px;\r\n}\r\n.latest-story--primary {\r\n  flex-direction: column;\r\n}\r\n.sectioner--latest-stories .latest-story {\r\n  margin-bottom: 1.875rem;\r\n  display: flex;\r\n  /* justify-content: space-between; */\r\n  text-align: left;\r\n  border-bottom: 0.0625rem solid #dddee4;\r\n  padding-bottom: 1.25rem;\r\n}\r\n.sectioner--latest-stories .latest-story:last-of-type {\r\n  margin-bottom: 0;\r\n  border-bottom: none;\r\n  padding-bottom: 0;\r\n}\r\n.sectioner--latest-stories .latest-story--primary {\r\n  border-bottom: 0.0625rem solid #dddee4;\r\n  padding-bottom: 1.25rem;\r\n  text-align: center;\r\n}\r\n.sectioner--latest-stories .latest-story--primary .entry__excerpt {\r\n  font-size: 19px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 26px;\r\n  letter-spacing: normal;\r\n  font-weight: 400;\r\n  margin-bottom: 0.75rem;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n  color: var(--text-color);\r\n}\r\n\r\n.latest-story--primary .entry {\r\n  align-items: center;\r\n  text-align: center;\r\n  min-height: 160px;\r\n}\r\n.latest-story .entry__heading {\r\n  font-family: rocky, sans-serif;\r\n  font-size: 29px;\r\n  line-height: 29px;\r\n  letter-spacing: normal;\r\n  color: var(--text-color);\r\n}\r\n.sectioner--latest-stories .latest-story--primary .entry__heading {\r\n  font-size: 26px;\r\n  line-height: 32px;\r\n  letter-spacing: normal;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\ncolor: var(--text-color);\r\n}\r\n.sectioner--latest-stories .section-header {\r\n  display: block;\r\n  border-bottom: none;\r\n  border-left: none;\r\n  text-align: center;\r\n  padding: 0;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.sectioner--latest-stories .section-header .section-header__heading {\r\n  font-size: 1.875rem;\r\n  line-height: 24px;\r\n  letter-spacing: 1px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n    color: var(--text-color);\r\n\r\n}\r\n.latest-story:not(.latest-story--primary) .featured-image {\r\n  display: inline-block;\r\n  height: auto;\r\n  max-width: 350px;\r\n  overflow: hidden;\r\n}\r\n.latest-story:not(.latest-story--primary) .featured-image a {\r\n  height: auto;\r\n}\r\n.latest-story--primary .featured-image {\r\n  margin-bottom: 0.9375rem;\r\n}\r\n/* .sectioner--latest-stories .latest-story .entry__heading {\r\n  margin-left: 10px;\r\n} */\r\n@media only screen and (min-width: 41.75rem) {\r\n  /* .two-rivers-wrapper .sectioner {\r\n    margin-bottom: 5rem;\r\n  } */\r\n  .latest-story .featured-image a {\r\n    width: calc(50% - 0.625rem);\r\n  }\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    display: inline-table;\r\n    max-width: 350px;\r\n    min-width: 50%;\r\n    overflow: hidden;\r\n  }\r\n  .latest-story {\r\n    display: flex;\r\n    /* justify-content: space-between; */\r\n    text-align: left;\r\n  }\r\n  .latest-story .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n  }\r\n  .latest-story .entry__heading {\r\n    font-size: 23px;\r\n  }\r\n}\r\n.latest-story--primary .entry,\r\n.latest-story--primary .featured-image {\r\n  width: 100%;\r\n}\r\n\r\n@media only screen and (min-width: 61.25rem) {\r\n  .two-rivers-wrapper {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    border-top: 0.5px solid #d4d4d4;\r\n  }\r\n  /* .sectioner--latest-stories {\r\n    width: calc(50% - 1.25rem);\r\n  } */\r\n  .sectioner--latest-stories {\r\n    padding: 75px 1.625rem;\r\n  }\r\n  .sectioner--latest-stories .latest-story--primary {\r\n    font-size: 2.25rem;\r\n    justify-content: flex-start;\r\n  }\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    display: inline-table;\r\n    max-width: 200px;\r\n    /* min-width: 15.125rem; */\r\n    min-width: 35%;\r\n    overflow: hidden;\r\n  }\r\n  .latest-story .entry__heading {\r\n    font-size: 20px;\r\n  }\r\n  .latest-story .entry__heading {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n  }\r\n}\r\n@media only screen and (min-width: 70.625rem) {\r\n  /* .sectioner--latest-stories {\r\n    max-width: 33.125rem;\r\n  } */\r\n  /* .sectioner--latest-stories .featured-image {\r\n    margin-bottom: 0;\r\n  } */\r\n  .sectioner--latest-stories {\r\n    padding: 75px 1.625rem;\r\n  }\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    display: inline-table;\r\n    max-width: 250px;\r\n    min-width: 35%;\r\n    /* min-width: 18.125rem; */\r\n\r\n    overflow: hidden;\r\n  }\r\n  .latest-story .entry__heading {\r\n    font-size: 22px;\r\n    line-height: 28px;\r\n  }\r\n  .two-rivers-wrapper .sectioner--latest-stories .featured-image {\r\n    position: relative;\r\n    margin-bottom: 1.5625rem;\r\n  }\r\n  .two-rivers-wrapper .sectioner--latest-stories .featured-image::before {\r\n    display: block;\r\n    content: \" \";\r\n    width: 100%;\r\n    padding-top: 56.25%;\r\n  }\r\n  .two-rivers-wrapper .sectioner--latest-stories .featured-image .image-wrapper {\r\n    display: block;\r\n    position: absolute;\r\n    inset: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n  }\r\n  .two-rivers-wrapper .entry__category {\r\n    font-size: 15px;\r\n    line-height: 22px;\r\n  }\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  /* .two-rivers-wrapper .sectioner {\r\n    margin-bottom: 5rem;\r\n  } */\r\n  .sectioner--latest-stories .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .sectioner--latest-stories .entry .entry__category {\r\n    order: 1;\r\n    margin-bottom: 5px;\r\n  }\r\n  .sectioner--latest-stories .entry .entry__heading {\r\n    order: 2;\r\n    margin-bottom: 5px;\r\n  }\r\n  .sectioner--latest-stories .entry .post-meta {\r\n    order: 3;\r\n  }\r\n  .sectioner--latest-stories .entry .post-meta .post-meta__author {\r\n    font-size: 12px;\r\n  }\r\n  .sectioner--latest-stories .entry .post-meta .post-meta__timestamp {\r\n    display: none;\r\n  }\r\n  .sectioner--latest-stories .latest-story .entry__heading {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n    /* margin-left: 10px; */\r\n  }\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    max-width: 400px;\r\n    min-width: 40%;\r\n  }\r\n  .sectioner--latest-stories {\r\n    /* padding: 0; */\r\n    /* padding-bottom: 5rem;\r\n    padding-top: 0; */\r\n    padding: 25px 0;\r\n    /* padding-left: 0;\r\n    padding-right: 0; */\r\n  }\r\n  .latest-story.latest-story--primary .entry {\r\n    min-height: auto;\r\n  }\r\n  .sectioner--latest-stories .latest-story--primary .entry__excerpt {\r\n    display: none;\r\n  }\r\n  .sectioner--latest-stories .section-header .section-header__heading {\r\n    font-size: 1.375rem;\r\n    letter-spacing: 1px;\r\n  }\r\n}\r\n@media only screen and (width <= 375px) {\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    max-height: 90px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;AAQA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAMA;;;;AAGA;;;;AAMA;EAIE;;;;EAGA;;;;;;;EAMA;;;;;EAKA;;;;;;EAKA;;;;;AAIF;;;;AAKA;EACE;;;;;;EAQA;;;;EAGA;;;;;EAIA;;;;;;;EAOA;;;;EAGA;;;;;;AAKF;EAOE;;;;EAGA;;;;;;;EAQA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;;;EAQA;;;;;;AAKF;EAIE;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAKA;;;;;EAIA;;;;EAQA;;;;EAGA;;;;EAGA;;;;;;AAKF;EACE", "debugId": null}}, {"offset": {"line": 5162, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5165, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/author.css"], "sourcesContent": ["#author_header {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  /* background-color: #fff; */\r\n  padding: 30px 0 40px;\r\n}\r\n\r\n#profile_author {\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n}\r\n#profile_author img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  object-position: center;\r\n}\r\n\r\n#author_header h1 {\r\n  line-height: 6vw;\r\n  color: var(--text-color);\r\n}\r\n#author_header h4 {\r\n  font-weight: 500;\r\n  font-size: 1.5rem;\r\n  font-family: rocky, sans-serif;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n  color: var(--text-color);\r\n}\r\n\r\n#author-icons {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  font-size: 20px;\r\n}\r\n\r\n#author_header p {\r\n  font-size: 18px;\r\n  line-height: 28px;\r\n  letter-spacing: 0.37px;\r\n  font-family: Georgia, sans-serif;\r\n  color: var(--text-color);\r\n}\r\n\r\n\r\n@media (max-width:768px) {\r\n  #author_header h4{\r\n    margin-top: 18px;\r\n  }  \r\n  #author_header h1 {\r\n    margin-top: 8px;\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;;AAUA;;;;;;;AAMA;;;;;;;AAOA;;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;AASA;EACE;;;;EAGA", "debugId": null}}, {"offset": {"line": 5227, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5230, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/error.css"], "sourcesContent": ["#error_page{\r\n    width: 100%;\r\n    height: 88vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background-color: var(--body-bg-color);\r\n}\r\n\r\n#error_page h2{\r\n    font-size: 6rem;\r\n    line-height: 1.1;\r\n    font-family: rocky, sans-serif;\r\n    font-weight: 300;\r\n}\r\n\r\n#error_page h4{\r\n    font-size: 4rem;\r\n    line-height: 1.1;\r\n    font-family: rocky, sans-serif;\r\n    font-weight: 300;\r\n    text-transform: capitalize;\r\n}\r\n#error_page p{\r\n    width: 60%;\r\n    text-align: center;\r\n    font-size: 18px;\r\n    line-height: 28px;\r\n    letter-spacing: .37px;\r\n    font-family: Georgia, sans-serif;\r\n    margin-top: 30px;\r\n}\r\n\r\n\r\n@media screen and (max-width: 767px) {\r\n    #error_page{\r\n        padding: 1rem;\r\n    }\r\n    #error_page p{\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n@media screen and (max-width: 960px) and (min-width: 767px) {\r\n    #error_page p{\r\n        width: 85%;\r\n    }\r\n}\r\n@media screen and (max-width: 1300px) and (min-width: 960px) {\r\n    #error_page p{\r\n        width: 75%;\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;;;AAWA;EACI;;;;EAGA;;;;;AAKJ;EACI;;;;;AAIJ;EACI", "debugId": null}}, {"offset": {"line": 5286, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5289, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/about.css"], "sourcesContent": ["#about_wrapper {\r\n  background-color: var(--body-bg-color);\r\n  padding: 60px 0;\r\n}\r\n\r\n#about_wrapper h1 {\r\n  font-size: 4rem;\r\n  line-height: 1.1;\r\n  font-family: rocky, sans-serif;\r\n  margin-bottom: 1.5rem;\r\n  padding: 0 1rem;\r\n}\r\n\r\n.container-wrap {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n}\r\n\r\n.text-container {\r\n  height: 100%;\r\n  padding: 2rem 1rem;\r\n  border-right: 1px solid var(--about-side-border);\r\n  padding-right: 2rem;\r\n}\r\n\r\n.text-container p {\r\n  font-size: 18px;\r\n  line-height: 28px;\r\n  letter-spacing: 0.37px;\r\n  font-weight: 300;\r\n  font-family: Georgia, sans-serif;\r\n  color: var(--text-color);\r\n  margin-bottom: 1.5rem;\r\n}\r\n.text-container p .bold{\r\n  font-weight: 600;\r\n}\r\n.text-container p.bold-p{\r\n  margin-bottom: .5rem;\r\n}\r\n.mt-top{\r\n  margin-top: 2.5rem;\r\n}\r\n.tel{\r\n  color: rgb(52, 117, 222);\r\n}\r\n.text-container ul li {\r\n  font-size: 18px;\r\n  line-height: 28px;\r\n  letter-spacing: 0.37px;\r\n  font-weight: 300;\r\n  font-family: Georgia, sans-serif;\r\n  color: var(--text-color);\r\n  margin-bottom: 0.6rem;\r\n  list-style-position: inside;\r\n}\r\n.text-container ul {\r\n  padding-left: 20px;\r\n}\r\n\r\n.text-container ul li {\r\n  list-style-position: outside;\r\n  padding-left: 10px;\r\n}\r\n\r\n.text-container h2 {\r\n  font-size: 26px;\r\n  line-height: 32px;\r\n  letter-spacing: normal;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n  color: var(--text-color);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@media screen and (max-width: 767px) {\r\n  #about_wrapper h1 {\r\n    font-size: 2rem;\r\n    color: var(--text-color);\r\n    width: 95%;\r\n    line-height: 1;\r\n    font-family: rocky, sans-serif;\r\n    letter-spacing: 0.37px;\r\n    padding: 0 0.625rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n  #about_wrapper .containerWrapper {\r\n    padding: 0.625rem !important;\r\n  }\r\n  .text-container p,\r\n  .text-container ul li {\r\n    font-weight: 300;\r\n    margin-bottom: 1.5rem;\r\n    line-height: 26px;\r\n    font-size: 16px;\r\n    font-family: Georgia, sans-serif;\r\n  }\r\n  .text-container h2 {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n  }\r\n  .text-container ul li {\r\n    margin-bottom: 0.6rem;\r\n  }\r\n  .text-container {\r\n    height: 100%;\r\n    padding: 1rem 0.625rem;\r\n    border-right: none;\r\n    border-bottom: 1px solid #00000026;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 960px) and (min-width: 767px) {\r\n}\r\n@media screen and (max-width: 1300px) and (min-width: 960px) {\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;;AAUA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;AAYA;EACE;;;;;;;;;;;EAUA;;;;EAGA;;;;;;;;EAQA;;;;;EAIA;;;;EAGA;;;;;;;;AAQF;;;;AAEA", "debugId": null}}, {"offset": {"line": 5421, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5424, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/styles/filterDrawer.css"], "sourcesContent": ["#drawer_container {\r\n  width: 100%;\r\n  height: 100dvh;\r\n  background-color: rgba(0, 0, 0, 0.35);\r\n  position: fixed;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: 9999;\r\n  pointer-events: none;\r\n  backdrop-filter: blur(4px);\r\n  opacity: 0;\r\n}\r\n\r\n#list_container {\r\n  position: absolute;\r\n  right: -100%;\r\n  top: 0;\r\n  width: 30%;\r\n  height: 100%;\r\n  background-color: var(--body-bg-color);\r\n}\r\n.Search_drawer #list_container {\r\n  width: 35%;\r\n}\r\n.Search_drawer form svg {\r\n  cursor: pointer;\r\n}\r\n\r\n\r\n.Search_drawer .chipContainer {\r\n  margin: 0;\r\n  margin-top: 3rem;\r\n}\r\n.filter_title {\r\n  font-size: 1.2rem;\r\n  color: var(--text-color);\r\n  font-family: rocky, sans-serif;\r\n  line-height: 1.1;\r\n}\r\n\r\n.accordion {\r\n  margin-top: 10px;\r\n  border-bottom: 1px solid #d2d2d2;\r\n}\r\n\r\n.accordion_header {\r\n  cursor: pointer;\r\n  padding: 20px 0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.accordion_header span {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 1.2rem;\r\n  color: var(--text-color);\r\n  /* font-family: rocky, sans-serif; */\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 1.1;\r\n}\r\n.accordion_header span.clear_text {\r\n  font-size: 0.8rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 23px;\r\n  letter-spacing: 0.08rem;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.accordion_content {\r\n  margin-top: 5px;\r\n  padding-left: 10px;\r\n  padding-bottom: 20px;\r\n  max-height: 250px;\r\n  overflow: auto;\r\n}\r\n\r\n.category_item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 5px;\r\n  font-size: 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 23px;\r\n  letter-spacing: 0.08rem;\r\n}\r\n\r\n.category_item input {\r\n  margin-right: 10px;\r\n  cursor: pointer;\r\n}\r\n\r\n.item_count {\r\n  color: #888;\r\n}\r\n\r\n.filter_title_row {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  gap: 0.5rem;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  padding: 2.5rem;\r\n  padding-top: 1.8rem;\r\n  padding-bottom: 3rem;\r\n}\r\n.search_drawer.filter_title_row {\r\n  justify-content: end;\r\n}\r\n#search_drawer {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 0.5rem;\r\n  border-bottom: 1px solid var(--chip-color);\r\n}\r\n#search_drawer input {\r\n  width: 100%;\r\n  padding: 10px;\r\n  background-color: transparent;\r\n  border: none;\r\n  outline: none;\r\n  font-size: 1rem;\r\n}\r\n#search_drawer input::placeholder {\r\n  font-size: 1rem;\r\n}\r\n#search_drawer svg {\r\n  font-size: 1.6rem;\r\n}\r\n.search_chipContainer .border_btn_button {\r\n  border: 1px solid var(--chip-color);\r\n  color: var(--chip-color);\r\n}\r\n.search_chipContainer .border_btn_button:hover{\r\n  color: var(--body-bg-color);\r\n}\r\n.drawer_body {\r\n  position: relative;\r\n  width: 100%;\r\n  height: calc(100% - (108px + 140px));\r\n  overflow-y: scroll;\r\n  padding: 0 2.5rem;\r\n}\r\n.drawer_body_search::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n.drawer_footer {\r\n  width: 100%;\r\n  position: relative;\r\n  padding: 2.5rem;\r\n}\r\n.drawer_footer .button_base {\r\n  width: 100%;\r\n}\r\n.close_icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.close_icon svg {\r\n  font-size: 35px;\r\n  color: var(--text-color);\r\n  font-family: rocky, sans-serif;\r\n  line-height: 1.1;\r\n  cursor: pointer;\r\n}\r\n.accordion_label {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.arrow_icon {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.arrow_icon.rotated {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n@media screen and (max-width: 767px) {\r\n  #list_container {\r\n    width: 100%;\r\n    padding: 1rem 1rem;\r\n  }\r\n  .Search_drawer #list_container {\r\n    width: 100%;\r\n}\r\n  .drawer_body {\r\n    padding: 0;\r\n  }\r\n  .filter_title_row {\r\n    padding: 0 0 4rem;\r\n  }\r\n  .drawer_footer {\r\n    padding: 2.5rem 0;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 960px) and (min-width: 767px) {\r\n  #list_container {\r\n    width: 55%;\r\n  }\r\n}\r\n@media screen and (max-width: 1300px) and (min-width: 960px) {\r\n  #list_container {\r\n    width: 42%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAaA;;;;;;;;;AAQA;;;;AAGA;;;;AAKA;;;;;AAIA;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;AAeA;;;;AAGA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;AAOA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;AAIA;EACE;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAKF;EACE;;;;;AAIF;EACE", "debugId": null}}, {"offset": {"line": 5665, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}