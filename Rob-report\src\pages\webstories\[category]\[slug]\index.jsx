import React from "react";
// import { getWebStories } from "@/pages/api/WebStoriesApi"; // TODO: Uncomment when API is ready
import Head from "next/head";

import { dateFormateWithTimeShort } from "@/utils/Util";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import ImageGallerySchema from "@/components/seo/ImageGallerySchema";
import MediaGallerySchema from "@/components/seo/MediaGallerySchema";
import { Const } from "@/utils/Constants";
import { webStoryDetailCSS } from "@/components/amp/ampCss";

export const config = { amp: true };

// DUMMY DATA - TODO: Remove when API is ready
const DUMMY_WEB_STORIES = {
	"luxury-cars-2024": {
		title: "Top 5 Luxury Cars of 2024",
		coverImg: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
		altName: "Luxury Cars 2024",
		timestamp: "2024-01-15T10:00:00Z",
		slides: [
			{
				title: "Ferrari SF90 Stradale",
				description:
					"<p>The pinnacle of Ferrari engineering, combining hybrid technology with pure performance.</p>",
				image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
				altName: "Ferrari SF90 Stradale",
				contributor: ["Ferrari Press"],
				timestamp: "2024-01-15T10:00:00Z",
			},
			{
				title: "Lamborghini Revuelto",
				description:
					"<p>The new flagship from Lamborghini featuring a revolutionary V12 hybrid powertrain.</p>",
				image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
				altName: "Lamborghini Revuelto",
				contributor: ["Lamborghini Media"],
				timestamp: "2024-01-15T10:00:00Z",
			},
			{
				title: "Rolls-Royce Spectre",
				description:
					"<p>The first fully electric Rolls-Royce, maintaining the brand's legendary luxury.</p>",
				image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
				altName: "Rolls-Royce Spectre",
				contributor: ["Rolls-Royce Press"],
				timestamp: "2024-01-15T10:00:00Z",
			},
			{
				title: "Bentley Continental GT Speed",
				description:
					"<p>The most powerful Continental GT ever, with unmatched grand touring capabilities.</p>",
				image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
				altName: "Bentley Continental GT Speed",
				contributor: ["Bentley Motors"],
				timestamp: "2024-01-15T10:00:00Z",
			},
			{
				title: "McLaren 750S",
				description:
					"<p>The latest in McLaren's Super Series, delivering track-focused performance for the road.</p>",
				image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
				altName: "McLaren 750S",
				contributor: ["McLaren Automotive"],
				timestamp: "2024-01-15T10:00:00Z",
			},
		],
	},
	"luxury-watches-2024": {
		title: "Exquisite Timepieces of 2024",
		coverImg: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
		altName: "Luxury Watches 2024",
		timestamp: "2024-01-20T14:00:00Z",
		slides: [
			{
				title: "Patek Philippe Nautilus",
				description: "<p>The iconic sports watch that defines luxury horology excellence.</p>",
				image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
				altName: "Patek Philippe Nautilus",
				contributor: ["Patek Philippe"],
				timestamp: "2024-01-20T14:00:00Z",
			},
			{
				title: "Rolex Daytona",
				description:
					"<p>The legendary chronograph that has become synonymous with racing heritage.</p>",
				image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
				altName: "Rolex Daytona",
				contributor: ["Rolex SA"],
				timestamp: "2024-01-20T14:00:00Z",
			},
			{
				title: "Audemars Piguet Royal Oak",
				description: "<p>The revolutionary design that changed luxury sports watches forever.</p>",
				image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
				altName: "Audemars Piguet Royal Oak",
				contributor: ["Audemars Piguet"],
				timestamp: "2024-01-20T14:00:00Z",
			},
			{
				title: "Vacheron Constantin Overseas",
				description:
					"<p>Swiss craftsmanship meets contemporary design in this exceptional timepiece.</p>",
				image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
				altName: "Vacheron Constantin Overseas",
				contributor: ["Vacheron Constantin"],
				timestamp: "2024-01-20T14:00:00Z",
			},
			{
				title: "Richard Mille RM 11-03",
				description:
					"<p>Cutting-edge materials and innovative design define this modern masterpiece.</p>",
				image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
				altName: "Richard Mille RM 11-03",
				contributor: ["Richard Mille"],
				timestamp: "2024-01-20T14:00:00Z",
			},
		],
	},
	"luxury-yachts-2024": {
		title: "Magnificent Yachts of 2024",
		coverImg: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
		altName: "Luxury Yachts 2024",
		timestamp: "2024-01-25T16:00:00Z",
		slides: [
			{
				title: "Azzam Superyacht",
				description:
					"<p>The world's largest private yacht, a floating palace of unprecedented luxury.</p>",
				image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
				altName: "Azzam Superyacht",
				contributor: ["Lürssen Yachts"],
				timestamp: "2024-01-25T16:00:00Z",
			},
			{
				title: "Eclipse Yacht",
				description:
					"<p>A masterpiece of naval architecture with unparalleled amenities and security.</p>",
				image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
				altName: "Eclipse Yacht",
				contributor: ["Blohm+Voss"],
				timestamp: "2024-01-25T16:00:00Z",
			},
			{
				title: "Dilbar Superyacht",
				description:
					"<p>The largest yacht by gross tonnage, featuring extraordinary interior volume.</p>",
				image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
				altName: "Dilbar Superyacht",
				contributor: ["Lürssen Yachts"],
				timestamp: "2024-01-25T16:00:00Z",
			},
			{
				title: "Sailing Yacht A",
				description:
					"<p>The world's largest sailing yacht, combining traditional sailing with modern luxury.</p>",
				image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
				altName: "Sailing Yacht A",
				contributor: ["Nobiskrug"],
				timestamp: "2024-01-25T16:00:00Z",
			},
			{
				title: "Octopus Yacht",
				description:
					"<p>An explorer yacht designed for adventure and luxury in the world's most remote locations.</p>",
				image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
				altName: "Octopus Yacht",
				contributor: ["Lürssen Yachts"],
				timestamp: "2024-01-25T16:00:00Z",
			},
		],
	},
	"luxury-real-estate-2024": {
		title: "Extraordinary Properties of 2024",
		coverImg: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
		altName: "Luxury Real Estate 2024",
		timestamp: "2024-02-01T12:00:00Z",
		slides: [
			{
				title: "Penthouse in Manhattan",
				description:
					"<p>A sky-high sanctuary offering breathtaking views of the New York City skyline.</p>",
				image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
				altName: "Manhattan Penthouse",
				contributor: ["Sotheby's Realty"],
				timestamp: "2024-02-01T12:00:00Z",
			},
			{
				title: "Villa in French Riviera",
				description:
					"<p>Mediterranean elegance meets modern luxury in this stunning coastal estate.</p>",
				image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
				altName: "French Riviera Villa",
				contributor: ["Christie's Real Estate"],
				timestamp: "2024-02-01T12:00:00Z",
			},
			{
				title: "Estate in Aspen",
				description:
					"<p>A mountain retreat offering world-class skiing and year-round luxury amenities.</p>",
				image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
				altName: "Aspen Estate",
				contributor: ["Douglas Elliman"],
				timestamp: "2024-02-01T12:00:00Z",
			},
			{
				title: "Mansion in Beverly Hills",
				description:
					"<p>Hollywood glamour and contemporary design converge in this iconic estate.</p>",
				image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
				altName: "Beverly Hills Mansion",
				contributor: ["The Agency"],
				timestamp: "2024-02-01T12:00:00Z",
			},
			{
				title: "Castle in Scotland",
				description:
					"<p>Historic grandeur preserved with modern amenities in the Scottish Highlands.</p>",
				image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
				altName: "Scottish Castle",
				contributor: ["Savills"],
				timestamp: "2024-02-01T12:00:00Z",
			},
		],
	},
	"luxury-jets-2024": {
		title: "Private Jets Redefining Luxury",
		coverImg: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop",
		altName: "Luxury Private Jets 2024",
		timestamp: "2024-02-05T18:00:00Z",
		slides: [
			{
				title: "Gulfstream G700",
				description:
					"<p>The flagship of business aviation, offering unmatched range and luxury.</p>",
				image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop",
				altName: "Gulfstream G700",
				contributor: ["Gulfstream Aerospace"],
				timestamp: "2024-02-05T18:00:00Z",
			},
			{
				title: "Bombardier Global 7500",
				description:
					"<p>The world's largest and longest-range business jet with four living spaces.</p>",
				image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop",
				altName: "Bombardier Global 7500",
				contributor: ["Bombardier"],
				timestamp: "2024-02-05T18:00:00Z",
			},
			{
				title: "Dassault Falcon 10X",
				description:
					"<p>French engineering excellence with the tallest and widest cabin in business aviation.</p>",
				image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop",
				altName: "Dassault Falcon 10X",
				contributor: ["Dassault Aviation"],
				timestamp: "2024-02-05T18:00:00Z",
			},
		],
	},
};

const WebStoryDetail = ({ data, nextData, breadcrumbs, meta, pathname }) => {
	// AMP-compliant CSS without i-amphtml- prefixes

	return (
		<>
			<style jsx>{webStoryDetailCSS}</style>
			<Head>
				<meta name="amp-to-amp-navigation" content="AMP-Redirect" />
				<script
					async
					custom-element="amp-story"
					src="https://cdn.ampproject.org/v0/amp-story-1.0.js"
				></script>
				<script
					async
					custom-element="amp-story-auto-ads"
					src="https://cdn.ampproject.org/v0/amp-story-auto-ads-0.1.js"
				></script>
				{/* <script
					async
					custom-element="amp-analytics"
					src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"
				></script> */}
				<SeoHeader meta={meta} pathname={pathname} />
				<BreadcrumbSchema itemList={breadcrumbs} />
				<ImageGallerySchema
					title={data?.slides?.[0]?.title || ""}
					description={data?.slides?.[0]?.description || ""}
					url={Const.ClientLink + pathname}
					datePublished={data?.timestamp || ""}
					data={data?.slides || []}
				/>
				<MediaGallerySchema
					title={data?.slides?.[0]?.title || ""}
					description={data?.slides?.[0]?.description || ""}
					data={data?.slides || []}
				/>
			</Head>

			<amp-story
				standalone=""
				title={data?.title || ""}
				publisher="Robb Report India"
				publisher-logo-src="/RR final logo.png"
				poster-portrait-src={data?.coverImg || ""}
			>
				{data?.slides?.map((slide, index) => {
					return (
						<React.Fragment key={index}>
							<amp-story-page id={`page-${index}`}>
								<amp-story-grid-layer template="fill">
									<amp-img
										src={slide?.image || ""}
										width="720"
										height="1280"
										layout="fill"
										alt={slide?.altName || ""}
									></amp-img>
								</amp-story-grid-layer>

								{index === 0 && (
									<amp-story-grid-layer template="vertical">
										<div className="brand-logo">
											<amp-img
												src="/RR final logo.png"
												width="200"
												height="80"
												layout="fixed"
												alt="Robb Report India Logo"
											></amp-img>
										</div>
									</amp-story-grid-layer>
								)}

								<amp-story-grid-layer template="vertical" className="story-content">
									<div className="story-text">
										<h1>{slide?.title || ""}</h1>
										{slide?.description && (
											<div dangerouslySetInnerHTML={{ __html: slide.description }} />
										)}
										{slide?.contributor?.length > 0 && (
											<small>Photo Credit: {slide.contributor.join(", ")}</small>
										)}
										{index === 0 && slide?.timestamp && (
											<small>Published: {dateFormateWithTimeShort(slide.timestamp)}</small>
										)}
									</div>
								</amp-story-grid-layer>
							</amp-story-page>
						</React.Fragment>
					);
				})}

				{/* Next Story Preview Page */}
				{nextData?.slug && nextData?.coverImg && (
					<amp-story-page id="next-story-preview">
						<amp-story-grid-layer template="fill">
							<amp-img
								src={nextData.coverImg}
								width="720"
								height="1280"
								layout="fill"
								alt={nextData.altName || "Next Story"}
							></amp-img>
						</amp-story-grid-layer>

						<amp-story-grid-layer template="vertical" className="next-story-overlay story-content">
							<div className="next-story-preview">
								{/* <div className="preview-label">Next Story</div> */}
								<h2>{nextData.title}</h2>
							</div>
						</amp-story-grid-layer>

						<amp-story-cta-layer>
							<a href={nextData.slug} className="next-story-btn">
								Read Next Story
							</a>
						</amp-story-cta-layer>
					</amp-story-page>
				)}

				<amp-story-auto-ads>
					<script
						type="application/json"
						dangerouslySetInnerHTML={{
							__html: JSON.stringify({
								"ad-attributes": {
									type: "doubleclick",
									"data-slot": "/23290324739/RobbReport-AMP-Stories",
								},
							}),
						}}
					/>
				</amp-story-auto-ads>

				{/* TODO: Uncomment when Google Analytics is configured
				<amp-analytics type="gtag" data-credentials="include">
					<script
						type="application/json"
						dangerouslySetInnerHTML={{
							__html: JSON.stringify({
								vars: {
									gtag_id: "G-YOUR_GA_ID",
									config: {
										"G-YOUR_GA_ID": {
											page_title: data?.title || "",
											page_location: Const.ClientLink + pathname
										}
									}
								},
								triggers: {
									storyPageView: {
										on: "story-page-visible",
										request: "event",
										vars: {
											event_name: "story_page_view",
											story_page_id: "${storyPageId}",
											story_page_index: "${storyPageIndex}"
										}
									},
									storyComplete: {
										on: "story-last-page-visible",
										request: "event",
										vars: {
											event_name: "story_complete"
										}
									}
								}
							})
						}}
					/>
				</amp-analytics> */}
			</amp-story>
		</>
	);
};

export default WebStoryDetail;
WebStoryDetail.config = { amp: true };

export async function getServerSideProps(context) {
	const { slug } = context.params;

	// TODO: Replace with actual API call when ready
	// const url = `/${slug}`;
	// try {
	//   const storiesRes = await getWebStories(url);
	//   if (!storiesRes || Object.keys(storiesRes.data).length === 0) {
	//     return { notFound: true };
	//   }
	//   const storyData = storiesRes.data.current.data;
	//   // Add cover slide as first slide
	//   const newObject = {
	//     title: storyData.title,
	//     description: "",
	//     image: storyData.coverImg,
	//     altName: storyData.altName,
	//     sequence: -1,
	//     contributor: [],
	//     timestamp: storyData.timestamp,
	//   };
	//   if (Array.isArray(storyData.slides)) {
	//     storyData.slides.unshift(newObject);
	//   }
	//   return {
	//     props: {
	//       data: storyData ?? {},
	//       previousData: storiesRes.data.previous ?? {},
	//       nextData: storiesRes.data.next ?? {},
	//       breadcrumbs: storiesRes.data.current.breadcrumbs ?? [],
	//       tag: storiesRes.data.current.tag ?? [],
	//       meta: storiesRes.data.current.meta ?? {},
	//       pathname: context.resolvedUrl || context.req.url || "",
	//     },
	//   };
	// } catch (error) {
	//   console.error("Error fetching data:", error.message);
	//   return { notFound: true };
	// }

	// DUMMY DATA IMPLEMENTATION - Remove when API is ready
	try {
		const storyData = DUMMY_WEB_STORIES[slug];

		if (!storyData) {
			return {
				notFound: true,
			};
		}

		// Add cover slide as first slide
		const newObject = {
			title: storyData.title,
			description: "",
			image: storyData.coverImg,
			altName: storyData.altName,
			sequence: -1,
			contributor: [],
			timestamp: storyData.timestamp,
		};

		const slidesWithCover = [newObject, ...storyData.slides];

		// Get next story for preview (simple logic for demo)
		const storyKeys = Object.keys(DUMMY_WEB_STORIES);
		const currentIndex = storyKeys.indexOf(slug);
		const nextIndex = (currentIndex + 1) % storyKeys.length;
		const nextStoryKey = storyKeys[nextIndex];
		const nextStory = DUMMY_WEB_STORIES[nextStoryKey];

		return {
			props: {
				data: {
					...storyData,
					slides: slidesWithCover,
				},
				nextData: {
					title: nextStory.title,
					slug: `/webstories/all/${nextStoryKey}`,
					coverImg: nextStory.coverImg,
					altName: nextStory.altName,
				},
				breadcrumbs: [
					{ name: "Web Stories", slug: "/webstories" },
					{ name: "All", slug: "/webstories/all" },
				],
				tag: ["luxury", "lifestyle"],
				meta: {
					title: storyData.title + " | Robb Report India",
					description: storyData.slides[0]?.description?.replace(/<[^>]*>/g, "") || storyData.title,
					keywords: ["luxury", "lifestyle", "robb report"],
					robots: "index,follow",
				},
				pathname: context.resolvedUrl || context.req.url || "",
			},
		};
	} catch (error) {
		console.error("Error with dummy data:", error.message);
		return {
			notFound: true,
		};
	}
}
