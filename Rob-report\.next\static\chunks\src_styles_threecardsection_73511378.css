/* [project]/src/styles/threecardsection.css [client] (css) */
.editor-picks__secondary-wrapper {
  padding: 4rem 0 0;
}

.editor-picks__secondary-pick {
  text-align: center;
  margin-bottom: 1.25rem;
  margin-top: 1.5625rem;
}

.editor-picks__secondary-wrapper .entry__heading {
  color: var(--text-color);
  transition: color .1s ease-in .5s;
}

.editor-picks__secondary-wrapper .featured-image:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 56.25%;
}

.editor-picks__secondary-wrapper .featured-image .image-wrapper {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.editor-picks__secondary-wrapper .editor-picks__secondary-pick .featured-image {
  max-height: 300px !important;
}

@media only screen and (width <= 41.6875rem) {
  .editor-picks__secondary-wrapper {
    padding: 0;
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick a {
    display: flex;
    border-top: 1px solid #e8e8e8;
    padding-top: 25px;
    box-sizing: border-box;
    margin: 10px;
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {
    text-align: start;
    margin-left: 10px;
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {
    display: flex;
    flex-direction: column;
  }

  .editor-picks__secondary-pick .entry .entry__category {
    font-size: 11px;
    order: 1;
    margin-bottom: 5px;
  }

  .editor-picks__secondary-pick .entry .entry__heading {
    font-size: 17px;
    line-height: 22px;
    order: 2;
    margin-bottom: 5px;
  }

  .editor-picks__secondary-pick .entry .post-meta {
    justify-content: flex-end;
    order: 3;
  }

  .editor-picks__secondary-pick .entry .post-meta .post-meta__author {
    font-size: 12px;
  }

  .editor-picks__secondary-pick .entry .post-meta .post-meta__timestamp {
    display: none;
  }

  .editor-picks__primary-pick .entry {
    padding: .8rem .9375rem 0;
    display: flex;
    flex-direction: column;
  }

  .editor-picks__primary-pick .entry .entry__category {
    order: 1;
  }

  .editor-picks__primary-pick .entry .entry__heading {
    order: 2;
  }

  .editor-picks__primary-pick .entry__excerpt {
    display: none;
  }

  .editor-picks__primary-pick .entry .entry__excerpt {
    order: 3;
  }

  .editor-picks__primary-pick .entry .post-meta {
    order: 4;
    padding-bottom: 0;
  }

  .editor-picks__primary-pick .entry .post-meta .post-meta__author {
    font-size: 12px;
  }

  .editor-picks__primary-pick .entry .post-meta .post-meta__timestamp {
    display: none;
  }
}

@media only screen and (width <= 61.1875rem) {
  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .featured-image {
    max-width: 100%;
    min-width: 35%;
    overflow: hidden;
    position: relative;
  }
}

@media only screen and (width <= 450px) {
  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {
    margin-left: 15px;
  }
}

@media only screen and (width >= 41.75rem) {
  .editor-picks__secondary-wrapper .editor-picks__secondary-inner-wrapper {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .editor-picks__secondary-pick {
    width: calc(33.3333% - .83333rem);
    margin-bottom: 0;
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick {
    width: calc(33.3333% - .83333rem);
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick {
    margin-bottom: 1.5625rem;
  }
}

/*# sourceMappingURL=src_styles_threecardsection_73511378.css.map*/