{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/filterDrawer.css"], "sourcesContent": ["#drawer_container {\r\n  width: 100%;\r\n  height: 100dvh;\r\n  background-color: rgba(0, 0, 0, 0.35);\r\n  position: fixed;\r\n  right: 0;\r\n  top: 0;\r\n  z-index: 9999;\r\n  pointer-events: none;\r\n  backdrop-filter: blur(4px);\r\n  opacity: 0;\r\n}\r\n\r\n#list_container {\r\n  position: absolute;\r\n  right: -100%;\r\n  top: 0;\r\n  width: 30%;\r\n  height: 100%;\r\n  background-color: var(--body-bg-color);\r\n}\r\n.Search_drawer #list_container {\r\n  width: 35%;\r\n}\r\n.Search_drawer form svg {\r\n  cursor: pointer;\r\n}\r\n\r\n\r\n.Search_drawer .chipContainer {\r\n  margin: 0;\r\n  margin-top: 3rem;\r\n}\r\n.filter_title {\r\n  font-size: 1.2rem;\r\n  color: var(--text-color);\r\n  font-family: rocky, sans-serif;\r\n  line-height: 1.1;\r\n}\r\n\r\n.accordion {\r\n  margin-top: 10px;\r\n  border-bottom: 1px solid #d2d2d2;\r\n}\r\n\r\n.accordion_header {\r\n  cursor: pointer;\r\n  padding: 20px 0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.accordion_header span {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 1.2rem;\r\n  color: var(--text-color);\r\n  /* font-family: rocky, sans-serif; */\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 1.1;\r\n}\r\n.accordion_header span.clear_text {\r\n  font-size: 0.8rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 23px;\r\n  letter-spacing: 0.08rem;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.accordion_content {\r\n  margin-top: 5px;\r\n  padding-left: 10px;\r\n  padding-bottom: 20px;\r\n  max-height: 250px;\r\n  overflow: auto;\r\n}\r\n\r\n.category_item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 5px;\r\n  font-size: 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 23px;\r\n  letter-spacing: 0.08rem;\r\n}\r\n\r\n.category_item input {\r\n  margin-right: 10px;\r\n  cursor: pointer;\r\n}\r\n\r\n.item_count {\r\n  color: #888;\r\n}\r\n\r\n.filter_title_row {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  gap: 0.5rem;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  padding: 2.5rem;\r\n  padding-top: 1.8rem;\r\n  padding-bottom: 3rem;\r\n}\r\n.search_drawer.filter_title_row {\r\n  justify-content: end;\r\n}\r\n#search_drawer {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 0.5rem;\r\n  border-bottom: 1px solid var(--chip-color);\r\n}\r\n#search_drawer input {\r\n  width: 100%;\r\n  padding: 10px;\r\n  background-color: transparent;\r\n  border: none;\r\n  outline: none;\r\n  font-size: 1rem;\r\n}\r\n#search_drawer input::placeholder {\r\n  font-size: 1rem;\r\n}\r\n#search_drawer svg {\r\n  font-size: 1.6rem;\r\n}\r\n.search_chipContainer .border_btn_button {\r\n  border: 1px solid var(--chip-color);\r\n  color: var(--chip-color);\r\n}\r\n.search_chipContainer .border_btn_button:hover{\r\n  color: var(--body-bg-color);\r\n}\r\n.drawer_body {\r\n  position: relative;\r\n  width: 100%;\r\n  height: calc(100% - (108px + 140px));\r\n  overflow-y: scroll;\r\n  padding: 0 2.5rem;\r\n}\r\n.drawer_body_search::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n.drawer_footer {\r\n  width: 100%;\r\n  position: relative;\r\n  padding: 2.5rem;\r\n}\r\n.drawer_footer .button_base {\r\n  width: 100%;\r\n}\r\n.close_icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.close_icon svg {\r\n  font-size: 35px;\r\n  color: var(--text-color);\r\n  font-family: rocky, sans-serif;\r\n  line-height: 1.1;\r\n  cursor: pointer;\r\n}\r\n.accordion_label {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.arrow_icon {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.arrow_icon.rotated {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n@media screen and (max-width: 767px) {\r\n  #list_container {\r\n    width: 100%;\r\n    padding: 1rem 1rem;\r\n  }\r\n  .Search_drawer #list_container {\r\n    width: 100%;\r\n}\r\n  .drawer_body {\r\n    padding: 0;\r\n  }\r\n  .filter_title_row {\r\n    padding: 0 0 4rem;\r\n  }\r\n  .drawer_footer {\r\n    padding: 2.5rem 0;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 960px) and (min-width: 767px) {\r\n  #list_container {\r\n    width: 55%;\r\n  }\r\n}\r\n@media screen and (max-width: 1300px) and (min-width: 960px) {\r\n  #list_container {\r\n    width: 42%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAaA;;;;;;;;;AAQA;;;;AAGA;;;;AAKA;;;;;AAIA;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;AAeA;;;;AAGA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;AAOA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;AAIA;EACE;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAKF;EACE;;;;;AAIF;EACE"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}