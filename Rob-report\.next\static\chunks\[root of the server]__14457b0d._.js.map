{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\r\n\r\ntype SendMessage = (msg: any) => void;\r\nexport type WebSocketMessage =\r\n  | {\r\n      type: \"turbopack-connected\";\r\n    }\r\n  | {\r\n      type: \"turbopack-message\";\r\n      data: Record<string, any>;\r\n    };\r\n\r\n\r\nexport type ClientOptions = {\r\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void;\r\n  sendMessage: SendMessage;\r\n  onUpdateError: (err: unknown) => void;\r\n};\r\n\r\nexport function connect({\r\n  addMessageListener,\r\n  sendMessage,\r\n  onUpdateError = console.error,\r\n}: ClientOptions) {\r\n  addMessageListener((msg) => {\r\n    switch (msg.type) {\r\n      case \"turbopack-connected\":\r\n        handleSocketConnected(sendMessage);\r\n        break;\r\n      default:\r\n        try {\r\n          if (Array.isArray(msg.data)) {\r\n            for (let i = 0; i < msg.data.length; i++) {\r\n              handleSocketMessage(msg.data[i] as ServerMessage);\r\n            }\r\n          } else {\r\n            handleSocketMessage(msg.data as ServerMessage);\r\n          }\r\n          applyAggregatedUpdates();\r\n        } catch (e: unknown) {\r\n          console.warn(\r\n            \"[Fast Refresh] performing full reload\\n\\n\" +\r\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\r\n              \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" +\r\n              \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" +\r\n              \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" +\r\n              \"Fast Refresh requires at least one parent function component in your React tree.\"\r\n          );\r\n          onUpdateError(e);\r\n          location.reload();\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;\r\n  if (queued != null && !Array.isArray(queued)) {\r\n    throw new Error(\"A separate HMR handler was already registered\");\r\n  }\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\r\n    push: ([chunkPath, callback]: [ChunkPath, UpdateCallback]) => {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    },\r\n  };\r\n\r\n  if (Array.isArray(queued)) {\r\n    for (const [chunkPath, callback] of queued) {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    }\r\n  }\r\n}\r\n\r\ntype UpdateCallbackSet = {\r\n  callbacks: Set<UpdateCallback>;\r\n  unsubscribe: () => void;\r\n};\r\n\r\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map();\r\n\r\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\r\n  sendMessage(JSON.stringify(message));\r\n}\r\n\r\ntype ResourceKey = string;\r\n\r\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\r\n  return JSON.stringify({\r\n    path: resource.path,\r\n    headers: resource.headers || null,\r\n  });\r\n}\r\n\r\nfunction subscribeToUpdates(\r\n  sendMessage: SendMessage,\r\n  resource: ResourceIdentifier\r\n): () => void {\r\n  sendJSON(sendMessage, {\r\n    type: \"turbopack-subscribe\",\r\n    ...resource,\r\n  });\r\n\r\n  return () => {\r\n    sendJSON(sendMessage, {\r\n      type: \"turbopack-unsubscribe\",\r\n      ...resource,\r\n    });\r\n  };\r\n}\r\n\r\nfunction handleSocketConnected(sendMessage: SendMessage) {\r\n  for (const key of updateCallbackSets.keys()) {\r\n    subscribeToUpdates(sendMessage, JSON.parse(key));\r\n  }\r\n}\r\n\r\n// we aggregate all pending updates until the issues are resolved\r\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\r\n  new Map();\r\n\r\nfunction aggregateUpdates(msg: PartialServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  let aggregated = chunkListsWithPendingUpdates.get(key);\r\n\r\n  if (aggregated) {\r\n    aggregated.instruction = mergeChunkListUpdates(\r\n      aggregated.instruction,\r\n      msg.instruction\r\n    );\r\n  } else {\r\n    chunkListsWithPendingUpdates.set(key, msg);\r\n  }\r\n}\r\n\r\nfunction applyAggregatedUpdates() {\r\n  if (chunkListsWithPendingUpdates.size === 0) return;\r\n  hooks.beforeRefresh();\r\n  for (const msg of chunkListsWithPendingUpdates.values()) {\r\n    triggerUpdate(msg);\r\n  }\r\n  chunkListsWithPendingUpdates.clear();\r\n  finalizeUpdate();\r\n}\r\n\r\nfunction mergeChunkListUpdates(\r\n  updateA: ChunkListUpdate,\r\n  updateB: ChunkListUpdate\r\n): ChunkListUpdate {\r\n  let chunks;\r\n  if (updateA.chunks != null) {\r\n    if (updateB.chunks == null) {\r\n      chunks = updateA.chunks;\r\n    } else {\r\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);\r\n    }\r\n  } else if (updateB.chunks != null) {\r\n    chunks = updateB.chunks;\r\n  }\r\n\r\n  let merged;\r\n  if (updateA.merged != null) {\r\n    if (updateB.merged == null) {\r\n      merged = updateA.merged;\r\n    } else {\r\n      // Since `merged` is an array of updates, we need to merge them all into\r\n      // one, consistent update.\r\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\r\n      // no need to key on the `type` field.\r\n      let update = updateA.merged[0];\r\n      for (let i = 1; i < updateA.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateA.merged[i]\r\n        );\r\n      }\r\n\r\n      for (let i = 0; i < updateB.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateB.merged[i]\r\n        );\r\n      }\r\n\r\n      merged = [update];\r\n    }\r\n  } else if (updateB.merged != null) {\r\n    merged = updateB.merged;\r\n  }\r\n\r\n  return {\r\n    type: \"ChunkListUpdate\",\r\n    chunks,\r\n    merged,\r\n  };\r\n}\r\n\r\nfunction mergeChunkListChunks(\r\n  chunksA: Record<ChunkPath, ChunkUpdate>,\r\n  chunksB: Record<ChunkPath, ChunkUpdate>\r\n): Record<ChunkPath, ChunkUpdate> {\r\n  const chunks: Record<ChunkPath, ChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA)) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB)) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeChunkUpdates(\r\n  updateA: ChunkUpdate,\r\n  updateB: ChunkUpdate\r\n): ChunkUpdate | undefined {\r\n  if (\r\n    (updateA.type === \"added\" && updateB.type === \"deleted\") ||\r\n    (updateA.type === \"deleted\" && updateB.type === \"added\")\r\n  ) {\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"partial\") {\r\n    invariant(updateA.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  if (updateB.type === \"partial\") {\r\n    invariant(updateB.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction mergeChunkListEcmascriptMergedUpdates(\r\n  mergedA: EcmascriptMergedUpdate,\r\n  mergedB: EcmascriptMergedUpdate\r\n): EcmascriptMergedUpdate {\r\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);\r\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);\r\n\r\n  return {\r\n    type: \"EcmascriptMergedUpdate\",\r\n    entries,\r\n    chunks,\r\n  };\r\n}\r\n\r\nfunction mergeEcmascriptChunkEntries(\r\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\r\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\r\n): Record<ModuleId, EcmascriptModuleEntry> {\r\n  return { ...entriesA, ...entriesB };\r\n}\r\n\r\nfunction mergeEcmascriptChunksUpdates(\r\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\r\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\r\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\r\n  if (chunksA == null) {\r\n    return chunksB;\r\n  }\r\n\r\n  if (chunksB == null) {\r\n    return chunksA;\r\n  }\r\n\r\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA)) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\r\n        chunkUpdateA,\r\n        chunkUpdateB\r\n      );\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB)) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  if (Object.keys(chunks).length === 0) {\r\n    return undefined;\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeEcmascriptChunkUpdates(\r\n  updateA: EcmascriptMergedChunkUpdate,\r\n  updateB: EcmascriptMergedChunkUpdate\r\n): EcmascriptMergedChunkUpdate | undefined {\r\n  if (updateA.type === \"added\" && updateB.type === \"deleted\") {\r\n    // These two completely cancel each other out.\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"deleted\" && updateB.type === \"added\") {\r\n    const added = [];\r\n    const deleted = [];\r\n    const deletedModules = new Set(updateA.modules ?? []);\r\n    const addedModules = new Set(updateB.modules ?? []);\r\n\r\n    for (const moduleId of addedModules) {\r\n      if (!deletedModules.has(moduleId)) {\r\n        added.push(moduleId);\r\n      }\r\n    }\r\n\r\n    for (const moduleId of deletedModules) {\r\n      if (!addedModules.has(moduleId)) {\r\n        deleted.push(moduleId);\r\n      }\r\n    }\r\n\r\n    if (added.length === 0 && deleted.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added,\r\n      deleted,\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"partial\") {\r\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])]);\r\n    const deleted = new Set([\r\n      ...(updateA.deleted ?? []),\r\n      ...(updateB.deleted ?? []),\r\n    ]);\r\n\r\n    if (updateB.added != null) {\r\n      for (const moduleId of updateB.added) {\r\n        deleted.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    if (updateB.deleted != null) {\r\n      for (const moduleId of updateB.deleted) {\r\n        added.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added: [...added],\r\n      deleted: [...deleted],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"added\" && updateB.type === \"partial\") {\r\n    const modules = new Set([\r\n      ...(updateA.modules ?? []),\r\n      ...(updateB.added ?? []),\r\n    ]);\r\n\r\n    for (const moduleId of updateB.deleted ?? []) {\r\n      modules.delete(moduleId);\r\n    }\r\n\r\n    return {\r\n      type: \"added\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"deleted\") {\r\n    // We could eagerly return `updateB` here, but this would potentially be\r\n    // incorrect if `updateA` has added modules.\r\n\r\n    const modules = new Set(updateB.modules ?? []);\r\n\r\n    if (updateA.added != null) {\r\n      for (const moduleId of updateA.added) {\r\n        modules.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"deleted\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  // Any other update combination is invalid.\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction invariant(_: never, message: string): never {\r\n  throw new Error(`Invariant: ${message}`);\r\n}\r\n\r\nconst CRITICAL = [\"bug\", \"error\", \"fatal\"];\r\n\r\nfunction compareByList(list: any[], a: any, b: any) {\r\n  const aI = list.indexOf(a) + 1 || list.length;\r\n  const bI = list.indexOf(b) + 1 || list.length;\r\n  return aI - bI;\r\n}\r\n\r\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map();\r\n\r\nfunction emitIssues() {\r\n  const issues = [];\r\n  const deduplicationSet = new Set();\r\n\r\n  for (const [_, chunkIssues] of chunksWithIssues) {\r\n    for (const chunkIssue of chunkIssues) {\r\n      if (deduplicationSet.has(chunkIssue.formatted)) continue;\r\n\r\n      issues.push(chunkIssue);\r\n      deduplicationSet.add(chunkIssue.formatted);\r\n    }\r\n  }\r\n\r\n  sortIssues(issues);\r\n\r\n  hooks.issues(issues);\r\n}\r\n\r\nfunction handleIssues(msg: ServerMessage): boolean {\r\n  const key = resourceKey(msg.resource);\r\n  let hasCriticalIssues = false;\r\n\r\n  for (const issue of msg.issues) {\r\n    if (CRITICAL.includes(issue.severity)) {\r\n      hasCriticalIssues = true;\r\n    }\r\n  }\r\n\r\n  if (msg.issues.length > 0) {\r\n    chunksWithIssues.set(key, msg.issues);\r\n  } else if (chunksWithIssues.has(key)) {\r\n    chunksWithIssues.delete(key);\r\n  }\r\n\r\n  emitIssues();\r\n\r\n  return hasCriticalIssues;\r\n}\r\n\r\nconst SEVERITY_ORDER = [\"bug\", \"fatal\", \"error\", \"warning\", \"info\", \"log\"];\r\nconst CATEGORY_ORDER = [\r\n  \"parse\",\r\n  \"resolve\",\r\n  \"code generation\",\r\n  \"rendering\",\r\n  \"typescript\",\r\n  \"other\",\r\n];\r\n\r\nfunction sortIssues(issues: Issue[]) {\r\n  issues.sort((a, b) => {\r\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);\r\n    if (first !== 0) return first;\r\n    return compareByList(CATEGORY_ORDER, a.category, b.category);\r\n  });\r\n}\r\n\r\nconst hooks = {\r\n  beforeRefresh: () => {},\r\n  refresh: () => {},\r\n  buildOk: () => {},\r\n  issues: (_issues: Issue[]) => {},\r\n};\r\n\r\nexport function setHooks(newHooks: typeof hooks) {\r\n  Object.assign(hooks, newHooks);\r\n}\r\n\r\nfunction handleSocketMessage(msg: ServerMessage) {\r\n  sortIssues(msg.issues);\r\n\r\n  handleIssues(msg);\r\n\r\n  switch (msg.type) {\r\n    case \"issues\":\r\n      // issues are already handled\r\n      break;\r\n    case \"partial\":\r\n      // aggregate updates\r\n      aggregateUpdates(msg);\r\n      break;\r\n    default:\r\n      // run single update\r\n      const runHooks = chunkListsWithPendingUpdates.size === 0;\r\n      if (runHooks) hooks.beforeRefresh();\r\n      triggerUpdate(msg);\r\n      if (runHooks) finalizeUpdate();\r\n      break;\r\n  }\r\n}\r\n\r\nfunction finalizeUpdate() {\r\n  hooks.refresh();\r\n  hooks.buildOk();\r\n\r\n  // This is used by the Next.js integration test suite to notify it when HMR\r\n  // updates have been completed.\r\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\r\n  if (globalThis.__NEXT_HMR_CB) {\r\n    globalThis.__NEXT_HMR_CB();\r\n    globalThis.__NEXT_HMR_CB = null;\r\n  }\r\n}\r\n\r\nfunction subscribeToChunkUpdate(\r\n  chunkPath: ChunkPath,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n): () => void {\r\n  return subscribeToUpdate(\r\n    {\r\n      path: chunkPath,\r\n    },\r\n    sendMessage,\r\n    callback\r\n  );\r\n}\r\n\r\nexport function subscribeToUpdate(\r\n  resource: ResourceIdentifier,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n) {\r\n  const key = resourceKey(resource);\r\n  let callbackSet: UpdateCallbackSet;\r\n  const existingCallbackSet = updateCallbackSets.get(key);\r\n  if (!existingCallbackSet) {\r\n    callbackSet = {\r\n      callbacks: new Set([callback]),\r\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\r\n    };\r\n    updateCallbackSets.set(key, callbackSet);\r\n  } else {\r\n    existingCallbackSet.callbacks.add(callback);\r\n    callbackSet = existingCallbackSet;\r\n  }\r\n\r\n  return () => {\r\n    callbackSet.callbacks.delete(callback);\r\n\r\n    if (callbackSet.callbacks.size === 0) {\r\n      callbackSet.unsubscribe();\r\n      updateCallbackSets.delete(key);\r\n    }\r\n  };\r\n}\r\n\r\nfunction triggerUpdate(msg: ServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  const callbackSet = updateCallbackSets.get(key);\r\n  if (!callbackSet) {\r\n    return;\r\n  }\r\n\r\n  for (const callback of callbackSet.callbacks) {\r\n    callback(msg);\r\n  }\r\n\r\n  if (msg.type === \"notFound\") {\r\n    // This indicates that the resource which we subscribed to either does not exist or\r\n    // has been deleted. In either case, we should clear all update callbacks, so if a\r\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\r\n    // message to the server.\r\n    // No need to send an \"unsubscribe\" message to the server, it will have already\r\n    // dropped the update stream before sending the \"notFound\" message.\r\n    updateCallbackSets.delete(key);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAmBtD,SAAS,QAAQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf;IACd,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM,CAAC,CAAC,WAAW,SAAsC;YACvD,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAAU;QAC/D,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAAU;QAC/D,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAAU;QAC/D,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAAU;QAC/D,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,MAAM,iBAAiB,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QACpD,MAAM,eAAe,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,MAAM,QAAQ,IAAI,IAAI;eAAK,QAAQ,KAAK,IAAI,EAAE;eAAO,QAAQ,KAAK,IAAI,EAAE;SAAE;QAC1E,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,OAAO,IAAI,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,KAAK,IAAI,EAAE;SACxB;QAED,KAAK,MAAM,YAAY,QAAQ,OAAO,IAAI,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;AACzC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,SAAoB,EACpB,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/utils/Constants.jsx"], "sourcesContent": ["export const Const = {\r\n  Token: \"token\",\r\n  Session: \"Session\",\r\n  LoggedInRolePermission: \"Role\",\r\n  User: \"User\",\r\n  LoggedIn: \"LoggedIn\",\r\n  LoggedInUser: \"LoggedInUser\",\r\n  STrue: true,\r\n  SFalse: false,\r\n  Success200: 200,\r\n  Created201: 201,\r\n  Invalid400: 400,\r\n  UnAuth401: 401,\r\n  Forbidden403: 403,\r\n  NotFound404: 404,\r\n  ServerError500: 500,\r\n  BadGateway502: 502,\r\n  ServiceUnavailable503: 503,\r\n  GatewayTimeout504: 504,\r\n  Redirect302: 302,\r\n  Inactive: 0,\r\n  Active: 1,\r\n  Trash: 2,\r\n  Draft: 3,\r\n  Scheduled: 4,\r\n  Limit: 20,\r\n  Offset: 0,\r\n  Brand: \"Robb Report India\",\r\n  Link: process.env.NEXT_PUBLIC_BACKEND_URL,\r\n  ClientLink: process.env.NEXT_PUBLIC_CLIENT_URL,\r\n};\r\n\r\nexport const ProcessAPI = async (res) => {\r\n  if (res.status === Const.Success200 || res.status === Const.Created201) {\r\n    const data = await res.json();\r\n    return data;\r\n  } else if (res.status === Const.Redirect302) {\r\n  } else if (res.status === Const.Invalid400) {\r\n  } else if (res.status === Const.UnAuth401) {\r\n    localStorage.clear();\r\n    window.location.href = \"/signin\";\r\n  } else if (res.status === Const.NotFound404) {\r\n    const data = await res.json();\r\n    return data;\r\n    // return {\r\n    //   notFound: true,\r\n    // };\r\n  } else {\r\n    throw new Error(\"Some error occurred\");\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AA4BQ;AA5BD,MAAM,QAAQ;IACnB,OAAO;IACP,SAAS;IACT,wBAAwB;IACxB,MAAM;IACN,UAAU;IACV,cAAc;IACd,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,cAAc;IACd,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,uBAAuB;IACvB,mBAAmB;IACnB,aAAa;IACb,UAAU;IACV,QAAQ;IACR,OAAO;IACP,OAAO;IACP,WAAW;IACX,OAAO;IACP,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,UAAU;AACZ;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI,IAAI,MAAM,KAAK,MAAM,UAAU,IAAI,IAAI,MAAM,KAAK,MAAM,UAAU,EAAE;QACtE,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,OAAO;IACT,OAAO,IAAI,IAAI,MAAM,KAAK,MAAM,WAAW,EAAE,CAC7C,OAAO,IAAI,IAAI,MAAM,KAAK,MAAM,UAAU,EAAE,CAC5C,OAAO,IAAI,IAAI,MAAM,KAAK,MAAM,SAAS,EAAE;QACzC,aAAa,KAAK;QAClB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,OAAO,IAAI,IAAI,MAAM,KAAK,MAAM,WAAW,EAAE;QAC3C,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,OAAO;IACP,WAAW;IACX,oBAAoB;IACpB,KAAK;IACP,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;AACF;KAlBa", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/WebPageSchema.jsx"], "sourcesContent": ["const WebPageSchema = ({ name, description, url }) => {\r\n  const schemaData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"WebPage\",\r\n    name: name,\r\n    description: description,\r\n    speakable: {\r\n      \"@type\": \"SpeakableSpecification\",\r\n      xpath: [\"//title\", \"//meta[@name='description']/@content\"],\r\n    },\r\n    url: url,\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}\r\n    ></script>\r\n  );\r\n};\r\n\r\nexport default WebPageSchema;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;IAC/C,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,aAAa;QACb,WAAW;YACT,SAAS;YACT,OAAO;gBAAC;gBAAW;aAAuC;QAC5D;QACA,KAAK;IACP;IAEA,qBACE,0JAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAY;;;;;;AAGpE;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/NewsMediaOrganizationSchema.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst NewsMediaOrganizationSchema = ({\r\n  name,\r\n  clientLink,\r\n  logoUrl,\r\n  address,\r\n  contact,\r\n  sameAs,\r\n}) => {\r\n  const schemaData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"NewsMediaOrganization\",\r\n    name: name,\r\n    url: clientLink,\r\n    logo: {\r\n      \"@type\": \"ImageObject\",\r\n      url: logoUrl,\r\n    },\r\n    address: {\r\n      \"@type\": \"PostalAddress\",\r\n      streetAddress: address?.streetAddress,\r\n      addressLocality: address?.addressLocality,\r\n      addressRegion: address?.addressRegion,\r\n      postalCode: address?.postalCode,\r\n    },\r\n    contactPoint: {\r\n      \"@type\": \"ContactPoint\",\r\n      telephone: contact?.telephone,\r\n      contactType: contact?.contactType,\r\n      areaServed: contact?.areaServed,\r\n      availableLanguage: contact?.availableLanguage,\r\n      hoursAvailable: {\r\n        opens: contact?.hoursAvailable?.opens,\r\n        closes: contact?.hoursAvailable?.closes,\r\n      },\r\n    },\r\n    sameAs: sameAs,\r\n  };\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}\r\n    ></script>\r\n  );\r\n};\r\n\r\nexport default NewsMediaOrganizationSchema;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,8BAA8B,CAAC,EACnC,IAAI,EACJ,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACP;IACC,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,MAAM;QACN,KAAK;QACL,MAAM;YACJ,SAAS;YACT,KAAK;QACP;QACA,SAAS;YACP,SAAS;YACT,eAAe,SAAS;YACxB,iBAAiB,SAAS;YAC1B,eAAe,SAAS;YACxB,YAAY,SAAS;QACvB;QACA,cAAc;YACZ,SAAS;YACT,WAAW,SAAS;YACpB,aAAa,SAAS;YACtB,YAAY,SAAS;YACrB,mBAAmB,SAAS;YAC5B,gBAAgB;gBACd,OAAO,SAAS,gBAAgB;gBAChC,QAAQ,SAAS,gBAAgB;YACnC;QACF;QACA,QAAQ;IACV;IACA,qBACE,0JAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAY;;;;;;AAGpE;KA3CM;uCA6CS", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/helpers/MenuData.jsx"], "sourcesContent": ["export const menus = [\r\n  {\r\n    name: \"Motoring\",\r\n    link: \"/motoring\",\r\n    submenus: [\r\n      {\r\n        name: \"Cars\",\r\n        link: \"/motoring/cars\",\r\n      },\r\n      {\r\n        name: \"Bikes\",\r\n        link: \"/motoring/bikes\",\r\n      },\r\n      {\r\n        name: \"Vintage & Classics\",\r\n        link: \"/motoring/vintage-and-classics\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Yachting & Aviation\",\r\n    link: \"/yachting-and-aviation\",\r\n    submenus: [\r\n      {\r\n        name: \"Yachting\",\r\n        link: \"/yachting-and-aviation/yachting\",\r\n      },\r\n      {\r\n        name: \"Aviation\",\r\n        link: \"/yachting-and-aviation/aviation\",\r\n      },\r\n      {\r\n        name: \"Cruises & Expeditions\",\r\n        link: \"/yachting-and-aviation/cruises-and-expeditions\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Style\",\r\n    link: \"/style\",\r\n    submenus: [\r\n      {\r\n        name: \"Timepieces\",\r\n        link: \"/style/timepieces\",\r\n      },\r\n\r\n      {\r\n        name: \"Jewellery & Accessories\",\r\n        link: \"/style/jewellery-and-accessories\",\r\n      },\r\n      {\r\n        name: \"Fashion & Beauty\",\r\n        link: \"/style/fashion-and-beauty\",\r\n      },\r\n      {\r\n        name: \"Bespoke\",\r\n        link: \"/style/bespoke\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Home & Design\",\r\n    link: \"/home-and-design\",\r\n    submenus: [\r\n      {\r\n        name: \"Interiors & Architecture\",\r\n        link: \"/home-and-design/interiors-and-architecture\",\r\n      },\r\n      {\r\n        name: \"Real Estate\",\r\n        link: \"/home-and-design/real-estate\",\r\n      },\r\n      {\r\n        name: \"Art\",\r\n        link: \"/home-and-design/art\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Travel\",\r\n    link: \"/travel\",\r\n    submenus: [\r\n      {\r\n        name: \"Wellness & Spas\",\r\n        link: \"/travel/wellness-and-spas\",\r\n      },\r\n      {\r\n        name: \"India\",\r\n        link: \"/travel/india\",\r\n      },\r\n      {\r\n        name: \"International\",\r\n        link: \"/travel/international\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Food & Drink\",\r\n    link: \"/food-and-drink\",\r\n    submenus: [\r\n      {\r\n        name: \"Gastronomy\",\r\n        link: \"/food-and-drink/gastronomy\",\r\n      },\r\n      {\r\n        name: \"Spirits\",\r\n        link: \"/food-and-drink/spirits\",\r\n      },\r\n    ],\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,QAAQ;IACnB;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YAEA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;YACR;SACD;IACH;CACD", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/SiteNavigationSchema.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { menus } from \"@/helpers/MenuData\";\r\nimport { Const } from \"@/utils/Constants\";\r\n\r\nconst SiteNavigationSchema = () => {\r\n  const schemaData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"SiteNavigationElement\",\r\n    name: [],\r\n    url: [],\r\n  };\r\n  menus.forEach((menu) => {\r\n    schemaData.name.push(menu.name);\r\n    schemaData.url.push(`${Const.ClientLink}${menu.link}`);\r\n    if (menu && menu.submenus && menu.submenus.length > 0) {\r\n      menu.submenus.forEach((submenu) => {\r\n        if (submenu.name !== \"All\") {\r\n          schemaData.name.push(submenu.name);\r\n          schemaData.url.push(`${Const.ClientLink}${submenu.link}`);\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}\r\n    ></script>\r\n  );\r\n};\r\n\r\nexport default SiteNavigationSchema;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAuB;IAC3B,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,MAAM,EAAE;QACR,KAAK,EAAE;IACT;IACA,uHAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC;QACb,WAAW,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;QAC9B,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,sHAAA,CAAA,QAAK,CAAC,UAAU,GAAG,KAAK,IAAI,EAAE;QACrD,IAAI,QAAQ,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YACrD,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACrB,IAAI,QAAQ,IAAI,KAAK,OAAO;oBAC1B,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI;oBACjC,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,sHAAA,CAAA,QAAK,CAAC,UAAU,GAAG,QAAQ,IAAI,EAAE;gBAC1D;YACF;QACF;IACF;IAEA,qBACE,0JAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAY;;;;;;AAGpE;KA1BM;uCA4BS", "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/SeoHeader.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Head from \"next/head\";\r\nimport { useRouter } from \"next/router\";\r\nimport { Const } from \"@/utils/Constants\";\r\nimport WebPageSchema from \"@/components/seo/WebPageSchema\";\r\nimport NewsMediaOrganizationSchema from \"@/components/seo/NewsMediaOrganizationSchema\";\r\nimport SiteNavigationSchema from \"@/components/seo/SiteNavigationSchema\";\r\n\r\nconst SeoHeader = ({ meta = {}, type = \"website\" }) => {\r\n  const router = useRouter();\r\n  const defaultImage = `${Const.ClientLink}/favicon/favicon-192x192.png`;\r\n  const canonical = `${Const.ClientLink}/${router.asPath?.slice(1)}`;\r\n  return (\r\n    <Head>\r\n      <title>{meta?.title || \"\"}</title>\r\n      <meta name=\"description\" content={meta?.description || \"\"} />\r\n      <meta name=\"keywords\" content={meta?.keywords || \"\"} />\r\n      {meta?.author && <meta name=\"author\" content={meta?.author || \"\"} />}\r\n      <meta name=\"publisher\" content={Const.Brand} />\r\n      <meta\r\n        name=\"robots\"\r\n        content={\r\n          `${meta?.robots}, max-image-preview:large` ||\r\n          \"noindex,nofollow, max-image-preview:large\"\r\n        }\r\n      />\r\n      <link rel=\"canonical\" href={meta?.canonical || canonical} />\r\n      {/* OG Tags */}\r\n      <meta property=\"fb:app_id\" content=\"446498535209610\" />\r\n      <meta property=\"og:locale\" content=\"en_IN\" />\r\n      <meta property=\"og:type\" content={type} />\r\n      <meta property=\"og:title\" content={meta?.og?.title || \"\"} />\r\n      <meta property=\"og:description\" content={meta?.og?.description || \"\"} />\r\n      <meta property=\"og:url\" content={canonical} />\r\n      <meta property=\"og:site_name\" content={Const.Brand} />\r\n      <meta property=\"og:image\" content={meta?.og?.image || defaultImage} />\r\n      <meta property=\"og:image:width\" content=\"1200\" />\r\n      <meta property=\"og:image:height\" content=\"630\" />\r\n      {/* Twitter Tag */}\r\n      <meta\r\n        name=\"twitter:card\"\r\n        content={meta?.twitter?.card || \"summary_large_image\"}\r\n      />\r\n      <meta\r\n        name=\"twitter:title\"\r\n        content={meta?.twitter?.title || meta?.title}\r\n      />\r\n      <meta\r\n        name=\"twitter:description\"\r\n        content={meta?.twitter?.description || meta?.description}\r\n      />\r\n      <meta name=\"twitter:site\" content={\"@robbreportindia\"} />\r\n      <meta\r\n        name=\"twitter:image\"\r\n        content={meta?.twitter?.image || defaultImage}\r\n      />\r\n      <meta name=\"twitter:creator\" content={\"@robbreportindia\"} />\r\n      <meta charSet=\"UTF-8\" />\r\n      <meta httpEquiv=\"Content-Type\" content=\"text/html;charset=UTF-8\" />\r\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n      <link\r\n        rel=\"icon\"\r\n        type=\"image/png\"\r\n        sizes=\"16x16\"\r\n        href=\"/favicon/favicon-16x16.png\"\r\n      />\r\n      <link\r\n        rel=\"icon\"\r\n        type=\"image/png\"\r\n        sizes=\"32x32\"\r\n        href=\"/favicon/favicon-32x32.png\"\r\n      />\r\n      <link\r\n        rel=\"icon\"\r\n        type=\"image/png\"\r\n        sizes=\"192x192\"\r\n        href=\"/favicon/favicon-192x192.png\"\r\n      />\r\n      <link rel=\"apple-touch-icon\" href=\"/favicon/apple-touch-icon.png\" />\r\n      <link\r\n        rel=\"alternate\"\r\n        hrefLang=\"en-in\"\r\n        href={meta?.canonical || canonical}\r\n      />\r\n      <WebPageSchema\r\n        name={meta?.title || \"\"}\r\n        description={meta?.description || \"\"}\r\n        url={meta?.canonical || canonical}\r\n      />\r\n      <NewsMediaOrganizationSchema\r\n        name={Const.Brand}\r\n        clientLink={`${Const.ClientLink}/`}\r\n        logoUrl={`${Const.ClientLink}/RR final logo.png`}\r\n        address={{\r\n          streetAddress:\r\n            \"RPSG Lifestyle Media, Thapar House, 3rd floor, Janpath Lane\",\r\n          addressLocality: \"New Delhi\",\r\n          addressRegion: \"India\",\r\n          postalCode: \"110 001\",\r\n        }}\r\n        contact={{\r\n          telephone: \"+91–11–23486700\",\r\n          contactType: \"Customer Service\",\r\n          areaServed: \"IN\",\r\n          availableLanguage: \"English\",\r\n          hoursAvailable: {\r\n            opens: \"09:00\",\r\n            closes: \"19:00\",\r\n          },\r\n        }}\r\n        sameAs={[\r\n          \"https://www.facebook.com/robbreporterindia\",\r\n          \"https://www.instagram.com/robbreporterindia/\",\r\n          \"https://twitter.com/robbreportindia\",\r\n          \"https://www.youtube.com/@robbreportIndia\",\r\n        ]}\r\n      />\r\n      <SiteNavigationSchema />\r\n    </Head>\r\n  );\r\n};\r\n\r\nexport default SeoHeader;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,YAAY,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE;;IAChD,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,GAAG,sHAAA,CAAA,QAAK,CAAC,UAAU,CAAC,4BAA4B,CAAC;IACtE,MAAM,YAAY,GAAG,sHAAA,CAAA,QAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE,MAAM,IAAI;IAClE,qBACE,0JAAC,wHAAA,CAAA,UAAI;;0BACH,0JAAC;0BAAO,MAAM,SAAS;;;;;;0BACvB,0JAAC;gBAAK,MAAK;gBAAc,SAAS,MAAM,eAAe;;;;;;0BACvD,0JAAC;gBAAK,MAAK;gBAAW,SAAS,MAAM,YAAY;;;;;;YAChD,MAAM,wBAAU,0JAAC;gBAAK,MAAK;gBAAS,SAAS,MAAM,UAAU;;;;;;0BAC9D,0JAAC;gBAAK,MAAK;gBAAY,SAAS,sHAAA,CAAA,QAAK,CAAC,KAAK;;;;;;0BAC3C,0JAAC;gBACC,MAAK;gBACL,SACE,GAAG,MAAM,OAAO,yBAAyB,CAAC,IAC1C;;;;;;0BAGJ,0JAAC;gBAAK,KAAI;gBAAY,MAAM,MAAM,aAAa;;;;;;0BAE/C,0JAAC;gBAAK,UAAS;gBAAY,SAAQ;;;;;;0BACnC,0JAAC;gBAAK,UAAS;gBAAY,SAAQ;;;;;;0BACnC,0JAAC;gBAAK,UAAS;gBAAU,SAAS;;;;;;0BAClC,0JAAC;gBAAK,UAAS;gBAAW,SAAS,MAAM,IAAI,SAAS;;;;;;0BACtD,0JAAC;gBAAK,UAAS;gBAAiB,SAAS,MAAM,IAAI,eAAe;;;;;;0BAClE,0JAAC;gBAAK,UAAS;gBAAS,SAAS;;;;;;0BACjC,0JAAC;gBAAK,UAAS;gBAAe,SAAS,sHAAA,CAAA,QAAK,CAAC,KAAK;;;;;;0BAClD,0JAAC;gBAAK,UAAS;gBAAW,SAAS,MAAM,IAAI,SAAS;;;;;;0BACtD,0JAAC;gBAAK,UAAS;gBAAiB,SAAQ;;;;;;0BACxC,0JAAC;gBAAK,UAAS;gBAAkB,SAAQ;;;;;;0BAEzC,0JAAC;gBACC,MAAK;gBACL,SAAS,MAAM,SAAS,QAAQ;;;;;;0BAElC,0JAAC;gBACC,MAAK;gBACL,SAAS,MAAM,SAAS,SAAS,MAAM;;;;;;0BAEzC,0JAAC;gBACC,MAAK;gBACL,SAAS,MAAM,SAAS,eAAe,MAAM;;;;;;0BAE/C,0JAAC;gBAAK,MAAK;gBAAe,SAAS;;;;;;0BACnC,0JAAC;gBACC,MAAK;gBACL,SAAS,MAAM,SAAS,SAAS;;;;;;0BAEnC,0JAAC;gBAAK,MAAK;gBAAkB,SAAS;;;;;;0BACtC,0JAAC;gBAAK,SAAQ;;;;;;0BACd,0JAAC;gBAAK,WAAU;gBAAe,SAAQ;;;;;;0BACvC,0JAAC;gBAAK,MAAK;gBAAW,SAAQ;;;;;;0BAC9B,0JAAC;gBACC,KAAI;gBACJ,MAAK;gBACL,OAAM;gBACN,MAAK;;;;;;0BAEP,0JAAC;gBACC,KAAI;gBACJ,MAAK;gBACL,OAAM;gBACN,MAAK;;;;;;0BAEP,0JAAC;gBACC,KAAI;gBACJ,MAAK;gBACL,OAAM;gBACN,MAAK;;;;;;0BAEP,0JAAC;gBAAK,KAAI;gBAAmB,MAAK;;;;;;0BAClC,0JAAC;gBACC,KAAI;gBACJ,UAAS;gBACT,MAAM,MAAM,aAAa;;;;;;0BAE3B,0JAAC,sIAAA,CAAA,UAAa;gBACZ,MAAM,MAAM,SAAS;gBACrB,aAAa,MAAM,eAAe;gBAClC,KAAK,MAAM,aAAa;;;;;;0BAE1B,0JAAC,oJAAA,CAAA,UAA2B;gBAC1B,MAAM,sHAAA,CAAA,QAAK,CAAC,KAAK;gBACjB,YAAY,GAAG,sHAAA,CAAA,QAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBAClC,SAAS,GAAG,sHAAA,CAAA,QAAK,CAAC,UAAU,CAAC,kBAAkB,CAAC;gBAChD,SAAS;oBACP,eACE;oBACF,iBAAiB;oBACjB,eAAe;oBACf,YAAY;gBACd;gBACA,SAAS;oBACP,WAAW;oBACX,aAAa;oBACb,YAAY;oBACZ,mBAAmB;oBACnB,gBAAgB;wBACd,OAAO;wBACP,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN;oBACA;oBACA;oBACA;iBACD;;;;;;0BAEH,0JAAC,6IAAA,CAAA,UAAoB;;;;;;;;;;;AAG3B;GAhHM;;QACW,0HAAA,CAAA,YAAS;;;KADpB;uCAkHS", "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/seo/BreadcrumbSchema.jsx"], "sourcesContent": ["import { Const } from \"@/utils/Constants\";\r\nimport Head from \"next/head\";\r\n\r\nconst BreadcrumbSchema = ({ itemList }) => {\r\n  const breadcrumb = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    itemListElement: [\r\n      {\r\n        \"@type\": \"ListItem\",\r\n        position: 1,\r\n        item: {\r\n          \"@id\": Const.ClientLink,\r\n          name: \"Home\",\r\n        },\r\n      },\r\n    ],\r\n  };\r\n\r\n  if (itemList && itemList.length > 0) {\r\n    itemList.forEach((item, index) => {\r\n      breadcrumb.itemListElement.push({\r\n        \"@type\": \"ListItem\",\r\n        position: index + 2,\r\n        item: {\r\n          \"@id\": Const.ClientLink + item.slug,\r\n          name: item.name,\r\n        },\r\n      });\r\n    });\r\n  }\r\n\r\n  return (\r\n    <Head>\r\n      <script\r\n        type=\"application/ld+json\"\r\n        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumb) }}\r\n      ></script>\r\n    </Head>\r\n  );\r\n};\r\n\r\nexport default BreadcrumbSchema;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE;IACpC,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QACT,iBAAiB;YACf;gBACE,SAAS;gBACT,UAAU;gBACV,MAAM;oBACJ,OAAO,sHAAA,CAAA,QAAK,CAAC,UAAU;oBACvB,MAAM;gBACR;YACF;SACD;IACH;IAEA,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;QACnC,SAAS,OAAO,CAAC,CAAC,MAAM;YACtB,WAAW,eAAe,CAAC,IAAI,CAAC;gBAC9B,SAAS;gBACT,UAAU,QAAQ;gBAClB,MAAM;oBACJ,OAAO,sHAAA,CAAA,QAAK,CAAC,UAAU,GAAG,KAAK,IAAI;oBACnC,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;IACF;IAEA,qBACE,0JAAC,wHAAA,CAAA,UAAI;kBACH,cAAA,0JAAC;YACC,MAAK;YACL,yBAAyB;gBAAE,QAAQ,KAAK,SAAS,CAAC;YAAY;;;;;;;;;;;AAItE;KArCM;uCAuCS", "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/categories/Hero.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/router\";\r\nimport gsap from \"gsap\";\r\nimport ScrollTrigger from \"gsap/dist/ScrollTrigger\";\r\n\r\ngsap.registerPlugin(ScrollTrigger);\r\nconst Hero = ({ title, breadcrumbs = [], classH1 = \"\" }) => {\r\n  const router = useRouter();\r\n  const [text, setText] = useState(\"\");\r\n\r\n  const handleSearch = () => {\r\n    router.push(`/result/${text}/1`);\r\n  };\r\n  return (\r\n    <>\r\n      <div data-scroll-container=\"true\">\r\n        <div>\r\n          <div className=\"page_head_set\">\r\n            <h1 className={`editionTitle ${classH1}`}>{title || \"\"}</h1>\r\n            {breadcrumbs && breadcrumbs?.length > 0 && (\r\n              <div className=\"page_breadcrumb\">\r\n                <ol>\r\n                  {breadcrumbs?.map((item, i) => {\r\n                    return (\r\n                      <li key={`breadcrumbs-${i}`}>\r\n                        <Link href={item?.slug ?? \"#\"}>{item?.name ?? \"\"}</Link>\r\n                      </li>\r\n                    );\r\n                  })}\r\n                </ol>\r\n              </div>\r\n            )}\r\n          </div>\r\n          {/* {true && (\r\n            <div className=\"page_bar\">\r\n              <div className=\"search_bar flexCntr\">\r\n                <div className=\"search_bar_side flexCntr w-100\">\r\n                  <div className=\"bar_icon\" onClick={handleSearch}>\r\n                    <svg\r\n                      width={32}\r\n                      height={33}\r\n                      viewBox=\"0 0 32 33\"\r\n                      fill=\"none\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        fillRule=\"evenodd\"\r\n                        clipRule=\"evenodd\"\r\n                        d=\"M28.1273 26.7563L21.2656 19.8947C22.4799 18.2868 23.2 16.2848 23.2 14.1146C23.2 8.81271 18.9019 4.51465 13.6 4.51465C8.29807 4.51465 4 8.81271 4 14.1146C4 19.4166 8.29807 23.7146 13.6 23.7146C15.7702 23.7146 17.7721 22.9946 19.38 21.7803L26.2417 28.642L28.1273 26.7563ZM20.5333 14.1146C20.5333 17.9438 17.4292 21.048 13.6 21.048C9.77083 21.048 6.66667 17.9438 6.66667 14.1146C6.66667 10.2855 9.77083 7.18131 13.6 7.18131C17.4292 7.18131 20.5333 10.2855 20.5333 14.1146Z\"\r\n                        fill=\"currentColor\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"bar_input\">\r\n                    <form\r\n                      onSubmit={(e) => {\r\n                        e.preventDefault();\r\n                        handleSearch();\r\n                      }}\r\n                    >\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"query\"\r\n                        placeholder=\"What are you looking for?\"\r\n                        value={text || \"\"}\r\n                        onChange={(e) => setText(e.target.value)}\r\n                      />\r\n                    </form>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )} */}\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Hero;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,yIAAA,CAAA,UAAI,CAAC,cAAc,CAAC,yIAAA,CAAA,UAAa;AACjC,MAAM,OAAO,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,UAAU,EAAE,EAAE;;IACrD,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;IACjC;IACA,qBACE;kBACE,cAAA,0JAAC;YAAI,yBAAsB;sBACzB,cAAA,0JAAC;0BACC,cAAA,0JAAC;oBAAI,WAAU;;sCACb,0JAAC;4BAAG,WAAW,CAAC,aAAa,EAAE,SAAS;sCAAG,SAAS;;;;;;wBACnD,eAAe,aAAa,SAAS,mBACpC,0JAAC;4BAAI,WAAU;sCACb,cAAA,0JAAC;0CACE,aAAa,IAAI,CAAC,MAAM;oCACvB,qBACE,0JAAC;kDACC,cAAA,0JAAC,wHAAA,CAAA,UAAI;4CAAC,MAAM,MAAM,QAAQ;sDAAM,MAAM,QAAQ;;;;;;uCADvC,CAAC,YAAY,EAAE,GAAG;;;;;gCAI/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDlB;GAvEM;;QACW,0HAAA,CAAA,YAAS;;;KADpB;uCAyES", "debugId": null}}, {"offset": {"line": 1329, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/utils/Util.jsx"], "sourcesContent": ["import parse from \"html-react-parser\";\r\nimport { Const } from \"./Constants\";\r\n\r\nexport const htmlParser = (data) => {\r\n  return parse(data);\r\n};\r\n\r\nexport const dateFormateWithTime = (dateString) => {\r\n  const date = new Date(dateString);\r\n\r\n  const monthNames = [\r\n    \"JANUARY\",\r\n    \"FEBRUARY\",\r\n    \"MARCH\",\r\n    \"APRIL\",\r\n    \"MAY\",\r\n    \"JUN<PERSON>\",\r\n    \"JULY\",\r\n    \"AUGUST\",\r\n    \"SEPTEMBER\",\r\n    \"OCTOBER\",\r\n    \"NOVEMBER\",\r\n    \"DECEMBER\",\r\n  ];\r\n\r\n  const month = monthNames[date.getMonth()];\r\n  const day = date.getDate();\r\n  const year = date.getFullYear();\r\n\r\n  let hours = date.getHours();\r\n  const minutes = date.getMinutes();\r\n  const ampm = hours >= 12 ? \"PM\" : \"AM\";\r\n\r\n  hours = hours % 12 || 12;\r\n\r\n  const minutesStr = minutes.toString().padStart(2, \"0\");\r\n\r\n  return `${month} ${day}, ${year} ${hours}:${minutesStr}${ampm}`;\r\n};\r\n\r\nexport const formateDateShort = (dateString) => {\r\n  if (!dateString) return;\r\n  const date = new Date(dateString);\r\n  const day = String(date.getUTCDate()).padStart(2, \"0\");\r\n  const month = String(date.getUTCMonth() + 1).padStart(2, \"0\");\r\n  const year = String(date.getUTCFullYear()).slice(-2);\r\n\r\n  return `${day}.${month}.${year}`;\r\n};\r\n\r\nexport const dateFormateWithTimeShort = (dateString) => {\r\n  if (!dateString) return;\r\n  const date = new Date(dateString);\r\n\r\n  const monthNames = [\r\n    \"Jan\",\r\n    \"Feb\",\r\n    \"Mar\",\r\n    \"Apr\",\r\n    \"May\",\r\n    \"Jun\",\r\n    \"Jul\",\r\n    \"Aug\",\r\n    \"Sep\",\r\n    \"Oct\",\r\n    \"Nov\",\r\n    \"Dec\",\r\n  ];\r\n\r\n  const month = monthNames[date.getMonth()];\r\n  const day = date.getDate();\r\n  const year = date.getFullYear();\r\n\r\n  let hours = date.getHours();\r\n  const minutes = date.getMinutes();\r\n  const ampm = hours >= 12 ? \"PM\" : \"AM\";\r\n\r\n  hours = hours % 12 || 12;\r\n\r\n  const minutesStr = minutes.toString().padStart(2, \"0\");\r\n\r\n  return `${month} ${day}, ${year}`;\r\n};\r\n\r\nexport const timeFormate = (date) => {\r\n  const dateObj = new Date(date);\r\n  const options = {\r\n    month: \"long\",\r\n    day: \"2-digit\",\r\n    year: \"numeric\",\r\n    hour: \"numeric\",\r\n    minute: \"2-digit\",\r\n    hour12: true,\r\n  };\r\n  const formattedDate = new Intl.DateTimeFormat(\"en-US\", options).format(\r\n    dateObj\r\n  );\r\n  const getTime = formattedDate.split(\" at \")[1];\r\n  return getTime;\r\n};\r\n\r\nexport const formatDateAndTime = (isoString) => {\r\n  const date = new Date(isoString);\r\n  const now = new Date();\r\n\r\n  const months = [\r\n    \"Jan\",\r\n    \"Feb\",\r\n    \"Mar\",\r\n    \"Apr\",\r\n    \"May\",\r\n    \"Jun\",\r\n    \"Jul\",\r\n    \"Aug\",\r\n    \"Sep\",\r\n    \"Oct\",\r\n    \"Nov\",\r\n    \"Dec\",\r\n  ];\r\n  const formattedDate = `${\r\n    months[date.getUTCMonth()]\r\n  } ${date.getUTCDate()}, ${date.getUTCFullYear()}`;\r\n\r\n  const diffInMilliseconds = now - date;\r\n  const diffInMinutes = Math.floor(diffInMilliseconds / (1000 * 60));\r\n  const diffInHours = Math.floor(diffInMinutes / 60);\r\n  const diffInDays = Math.floor(diffInHours / 24);\r\n\r\n  if (diffInDays >= 10) {\r\n    return formattedDate;\r\n  } else {\r\n    let timeDifference;\r\n    if (diffInDays === 1) {\r\n      timeDifference = `${diffInDays} day ago`;\r\n    } else if (diffInDays > 1) {\r\n      timeDifference = `${diffInDays} days ago`;\r\n    } else if (diffInHours >= 1) {\r\n      timeDifference = `${diffInHours} hours ago`;\r\n    } else {\r\n      timeDifference = `${diffInMinutes} minutes ago`;\r\n    }\r\n    return timeDifference;\r\n  }\r\n};\r\n\r\nexport const formatDateTimeHv = (inputDateTime) => {\r\n  const inputDate = new Date(inputDateTime);\r\n  const currentDate = new Date();\r\n\r\n  const diffTime = Math.abs(currentDate - inputDate);\r\n  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\r\n\r\n  const isSameDay =\r\n    inputDate.getDate() === currentDate.getDate() &&\r\n    inputDate.getMonth() === currentDate.getMonth() &&\r\n    inputDate.getFullYear() === currentDate.getFullYear();\r\n\r\n  if (isSameDay) {\r\n    // Ensuring consistent 12-hour format (with AM/PM) across client and server\r\n    return inputDate.toLocaleTimeString(\"en-US\", {\r\n      hour: \"2-digit\",\r\n      minute: \"2-digit\",\r\n      hour12: true,\r\n    });\r\n  }\r\n\r\n  if (diffDays <= 2) {\r\n    return `${diffDays} day${diffDays > 1 ? \"s\" : \"\"} ago`;\r\n  }\r\n\r\n  // Return in \"SEP 19, 2024\" format for dates older than 2 days\r\n  const options = { year: \"numeric\", month: \"short\", day: \"numeric\" };\r\n  return inputDate.toLocaleDateString(\"en-US\", options);\r\n};\r\n\r\nexport const checkPermission = (value, viewIndex) => {\r\n  if (value && typeof value == \"number\" && value > 0) {\r\n    const permissions = Number(value)\r\n      .toString(2)\r\n      .split(\"\")\r\n      .reverse()\r\n      .map((item) => item === \"1\");\r\n    Object.keys(viewIndex).forEach(function (key, value) {\r\n      if (permissions.length > value) {\r\n        viewIndex[key] = permissions[value];\r\n      } else {\r\n        viewIndex[key] = false;\r\n      }\r\n    });\r\n    return viewIndex;\r\n  } else {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const binaryToNumber = (value) => {\r\n  if (value) {\r\n    const binaryToNumber = parseInt(value, 2);\r\n    return binaryToNumber;\r\n  } else {\r\n    return 0;\r\n  }\r\n};\r\n\r\nexport const permissionCount = (value) => {\r\n  if (value && typeof value == \"number\" && value > 0) {\r\n    const permissions = Number(value).toString(2).split(\"\");\r\n    const total = permissions.length;\r\n    const count = permissions.filter((item) => item === \"1\").length;\r\n    return { count, total };\r\n  }\r\n  return { count: 0, total: 0 };\r\n};\r\n\r\nexport const isValidColor = (input) => {\r\n  try {\r\n    const namedColors = [\r\n      \"black\",\r\n      \"silver\",\r\n      \"gray\",\r\n      \"white\",\r\n      \"maroon\",\r\n      \"red\",\r\n      \"purple\",\r\n      \"fuchsia\",\r\n      \"green\",\r\n      \"lime\",\r\n      \"olive\",\r\n      \"yellow\",\r\n      \"navy\",\r\n      \"blue\",\r\n      \"teal\",\r\n      \"aqua\",\r\n      // Add more color names here\r\n    ];\r\n\r\n    // Case-insensitive match against the list of named colors\r\n    const colorRegex = new RegExp(`^(${namedColors.join(\"|\")})$`, \"i\");\r\n    // let regex = new RegExp(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/);\r\n    let regex = new RegExp(\r\n      /^(#?([a-f\\d]{3,4}|[a-f\\d]{6}|[a-f\\d]{8})|rgb\\((0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d)\\)|rgba\\((0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|255|25[0-4]|2[0-4]\\d|1\\d\\d|0?\\d?\\d),(0|0?\\.\\d|1(\\.0)?)\\)|hsl\\((0|360|35\\d|3[0-4]\\d|[12]\\d\\d|0?\\d?\\d),(0|100|\\d{1,2})%,(0|100|\\d{1,2})%\\)|hsla\\((0|360|35\\d|3[0-4]\\d|[12]\\d\\d|0?\\d?\\d),(0|100|\\d{1,2})%,(0|100|\\d{1,2})%,(0?\\.\\d|1(\\.0)?)\\))$/\r\n    );\r\n    return regex.test(input) || colorRegex.test(input);\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const ga4FormatDate = (dateString) => {\r\n  // Extract year, month, and day from the string\r\n  const dateStringParam = dateString.split(\"-\");\r\n  const year = dateStringParam[0];\r\n  const month = parseInt(dateStringParam[1]);\r\n  const day = dateStringParam[2];\r\n\r\n  // Define an array for month names\r\n  const monthNames = [\r\n    \"January\",\r\n    \"February\",\r\n    \"March\",\r\n    \"April\",\r\n    \"May\",\r\n    \"June\",\r\n    \"July\",\r\n    \"August\",\r\n    \"September\",\r\n    \"October\",\r\n    \"November\",\r\n    \"December\",\r\n  ];\r\n\r\n  const date = new Date(year, month - 1, day);\r\n  const monthName = monthNames[date.getMonth()];\r\n  const formattedDay = day.padStart(2, \"0\");\r\n  return monthName + \" \" + formattedDay;\r\n};\r\n\r\nexport const generateSlug = (title) => {\r\n  let slug = title\r\n    .toString() // Convert to string\r\n    .toLowerCase() // Convert to lowercase\r\n    .trim() // Trim leading/trailing whitespace\r\n    .replace(/\\s+/g, \"-\") // Replace spaces with hyphens\r\n    .replace(/[^\\w-]+/g, \"\") // Remove all non-word characters\r\n    .replace(/--+/g, \"-\"); // Replace multiple hyphens with a single hyphen\r\n  // Ensure the slug starts with a slash\r\n  if (!slug.startsWith(\"/\")) {\r\n    slug = `/${slug}`;\r\n  }\r\n\r\n  return slug;\r\n};\r\n\r\nexport const statusLabel = (value) => {\r\n  let label = \"\";\r\n  if (value === Const.Inactive) {\r\n    label = \"Unpubilled\";\r\n  } else if (value === Const.Active) {\r\n    label = \"Published\";\r\n  } else if (value === Const.Trash) {\r\n    label = \"Trash\";\r\n  } else if (value === Const.Draft) {\r\n    label = \"Draft\";\r\n  } else if (value === Const.Scheduled) {\r\n    label = \"Scheduled\";\r\n  }\r\n  return label;\r\n};\r\n\r\nexport const hasHtmlTags = (str) => {\r\n  const regex = /<\\/?[a-z][\\s\\S]*>/i;\r\n  return regex.test(str);\r\n};\r\n\r\nexport const getEmbedType = (url) => {\r\n  const youtubeRegex =\r\n    /(?:https?:\\/\\/)?(?:www\\.)?(?:youtube\\.com\\/(?:[^\\/\\n\\s]+\\/\\S+\\/|(?:v|e(?:mbed)?)\\/|\\S*?[?&]v=)|youtu\\.be\\/)([a-zA-Z0-9_-]{11})/;\r\n\r\n  const instagramRegex =\r\n    /(?:https?:\\/\\/)?(?:www\\.)?instagram\\.com\\/(?:p|tv|reel)\\/([A-Za-z0-9_-]+)/;\r\n\r\n  const twitterRegex =\r\n    /(?:https?:\\/\\/)?(?:(?:www\\.|platform\\.)?(?:twitter|x)\\.com\\/(?:(?:\\w+\\/status\\/[0-9]+)|(?:embed\\/Tweet\\.html\\?id=[0-9]+)))/;\r\n\r\n  const facebookPostOrVideoRegex =\r\n    /(?:https?:\\/\\/)?(?:www\\.)?facebook\\.com\\/(?:[^\\/\\n\\s]+\\/posts\\/|(?:video\\.php\\?v=|watch\\/))([0-9]+)/;\r\n\r\n  if (youtubeRegex.test(url)) {\r\n    return \"youtube\";\r\n  } else if (instagramRegex.test(url)) {\r\n    return \"instagram\";\r\n  } else if (twitterRegex.test(url)) {\r\n    return \"twitter\";\r\n  } else if (facebookPostOrVideoRegex.test(url)) {\r\n    return \"facebook\";\r\n  }\r\n};\r\n\r\nexport const extractTwitterId = (embedUrl) => {\r\n  const match = embedUrl.split(\"id=\")[1];\r\n  return match;\r\n};\r\n\r\nexport const getTwitterUrl = (embedUrl) => {\r\n  const tweetId = new URL(embedUrl).searchParams.get(\"id\");\r\n  const tweetUrl = `https://twitter.com/i/web/status/${tweetId}`;\r\n  return tweetUrl;\r\n};\r\n\r\nexport const getAuthorText = (prefix = \"By\", author = [], contributor = []) => {\r\n  const list = author.length ? author : contributor;\r\n  const name = list[0]?.name || list[0] || \"\";\r\n  const count = list.length - 1;\r\n\r\n  return name ? `${prefix} ${name}${count ? ` +${count} More` : \"\"}` : \"\";\r\n};\r\n\r\nexport const extractTextFromDoc = (doc) => {\r\n  const output = [];\r\n\r\n  function extractTextFromContent(contentArray) {\r\n    return (\r\n      contentArray\r\n        ?.map((node) => {\r\n          if (node.type === \"text\") return node.text || \"\";\r\n          if (node.content) return extractTextFromContent(node.content);\r\n          return \"\";\r\n        })\r\n        .join(\"\") || \"\"\r\n    );\r\n  }\r\n\r\n  if (Array.isArray(doc.content)) {\r\n    for (const node of doc.content) {\r\n      if (node.type === \"paragraph\" || node.type === \"heading\") {\r\n        const text = extractTextFromContent(node.content || []);\r\n        if (text.trim()) output.push(text.trim());\r\n      }\r\n    }\r\n  }\r\n\r\n  return output.join(\" \");\r\n};\r\n\r\nexport const convertToISTISOString = (utcISOString) => {\r\n  if (!utcISOString) return \"\";\r\n  const date = new Date(utcISOString);\r\n\r\n  const istOffsetMs = 5.5 * 60 * 60 * 1000;\r\n  const istDate = new Date(date.getTime() + istOffsetMs);\r\n\r\n  return istDate.toISOString().replace(\"Z\", \"+05:30\");\r\n};\r\n\r\nexport const escapeXml = (unsafe) => {\r\n  return unsafe\r\n    .replace(/&/g, \"&amp;\")\r\n    .replace(/</g, \"&lt;\")\r\n    .replace(/>/g, \"&gt;\")\r\n    .replace(/\"/g, \"&quot;\")\r\n    .replace(/'/g, \"&apos;\");\r\n};\r\n\r\nexport const convertSlugOrTitle = (input = \"\", toTitle = true) => {\r\n  if (!input) return \"\"\r\n  const trimmedText = input.trim();\r\n  if (toTitle) {\r\n    return trimmedText.split(\"-\").join(\" \");\r\n  } else {\r\n    return trimmedText.toLowerCase().split(\" \").filter(Boolean).join(\"-\");\r\n  }\r\n};\r\n\r\nexport const capitalizeFirstLetter = (str) => {\r\n  if (!str) return '';\r\n  return str.replace(/\\b\\w/g, char => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AACA;;;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAK,AAAD,EAAE;AACf;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ,UAAU,CAAC,KAAK,QAAQ,GAAG;IACzC,MAAM,MAAM,KAAK,OAAO;IACxB,MAAM,OAAO,KAAK,WAAW;IAE7B,IAAI,QAAQ,KAAK,QAAQ;IACzB,MAAM,UAAU,KAAK,UAAU;IAC/B,MAAM,OAAO,SAAS,KAAK,OAAO;IAElC,QAAQ,QAAQ,MAAM;IAEtB,MAAM,aAAa,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAElD,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,aAAa,MAAM;AACjE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,YAAY;IACjB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG;IAClD,MAAM,QAAQ,OAAO,KAAK,WAAW,KAAK,GAAG,QAAQ,CAAC,GAAG;IACzD,MAAM,OAAO,OAAO,KAAK,cAAc,IAAI,KAAK,CAAC,CAAC;IAElD,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AAClC;AAEO,MAAM,2BAA2B,CAAC;IACvC,IAAI,CAAC,YAAY;IACjB,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ,UAAU,CAAC,KAAK,QAAQ,GAAG;IACzC,MAAM,MAAM,KAAK,OAAO;IACxB,MAAM,OAAO,KAAK,WAAW;IAE7B,IAAI,QAAQ,KAAK,QAAQ;IACzB,MAAM,UAAU,KAAK,UAAU;IAC/B,MAAM,OAAO,SAAS,KAAK,OAAO;IAElC,QAAQ,QAAQ,MAAM;IAEtB,MAAM,aAAa,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAElD,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;AACnC;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,UAAU,IAAI,KAAK;IACzB,MAAM,UAAU;QACd,OAAO;QACP,KAAK;QACL,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IACA,MAAM,gBAAgB,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CACpE;IAEF,MAAM,UAAU,cAAc,KAAK,CAAC,OAAO,CAAC,EAAE;IAC9C,OAAO;AACT;AAEO,MAAM,oBAAoB,CAAC;IAChC,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAEhB,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,gBAAgB,GACpB,MAAM,CAAC,KAAK,WAAW,GAAG,CAC3B,CAAC,EAAE,KAAK,UAAU,GAAG,EAAE,EAAE,KAAK,cAAc,IAAI;IAEjD,MAAM,qBAAqB,MAAM;IACjC,MAAM,gBAAgB,KAAK,KAAK,CAAC,qBAAqB,CAAC,OAAO,EAAE;IAChE,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAE5C,IAAI,cAAc,IAAI;QACpB,OAAO;IACT,OAAO;QACL,IAAI;QACJ,IAAI,eAAe,GAAG;YACpB,iBAAiB,GAAG,WAAW,QAAQ,CAAC;QAC1C,OAAO,IAAI,aAAa,GAAG;YACzB,iBAAiB,GAAG,WAAW,SAAS,CAAC;QAC3C,OAAO,IAAI,eAAe,GAAG;YAC3B,iBAAiB,GAAG,YAAY,UAAU,CAAC;QAC7C,OAAO;YACL,iBAAiB,GAAG,cAAc,YAAY,CAAC;QACjD;QACA,OAAO;IACT;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,cAAc,IAAI;IAExB,MAAM,WAAW,KAAK,GAAG,CAAC,cAAc;IACxC,MAAM,WAAW,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE3D,MAAM,YACJ,UAAU,OAAO,OAAO,YAAY,OAAO,MAC3C,UAAU,QAAQ,OAAO,YAAY,QAAQ,MAC7C,UAAU,WAAW,OAAO,YAAY,WAAW;IAErD,IAAI,WAAW;QACb,2EAA2E;QAC3E,OAAO,UAAU,kBAAkB,CAAC,SAAS;YAC3C,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,IAAI,YAAY,GAAG;QACjB,OAAO,GAAG,SAAS,IAAI,EAAE,WAAW,IAAI,MAAM,GAAG,IAAI,CAAC;IACxD;IAEA,8DAA8D;IAC9D,MAAM,UAAU;QAAE,MAAM;QAAW,OAAO;QAAS,KAAK;IAAU;IAClE,OAAO,UAAU,kBAAkB,CAAC,SAAS;AAC/C;AAEO,MAAM,kBAAkB,CAAC,OAAO;IACrC,IAAI,SAAS,OAAO,SAAS,YAAY,QAAQ,GAAG;QAClD,MAAM,cAAc,OAAO,OACxB,QAAQ,CAAC,GACT,KAAK,CAAC,IACN,OAAO,GACP,GAAG,CAAC,CAAC,OAAS,SAAS;QAC1B,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,SAAU,GAAG,EAAE,KAAK;YACjD,IAAI,YAAY,MAAM,GAAG,OAAO;gBAC9B,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC,MAAM;YACrC,OAAO;gBACL,SAAS,CAAC,IAAI,GAAG;YACnB;QACF;QACA,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,OAAO;QACT,MAAM,iBAAiB,SAAS,OAAO;QACvC,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,SAAS,OAAO,SAAS,YAAY,QAAQ,GAAG;QAClD,MAAM,cAAc,OAAO,OAAO,QAAQ,CAAC,GAAG,KAAK,CAAC;QACpD,MAAM,QAAQ,YAAY,MAAM;QAChC,MAAM,QAAQ,YAAY,MAAM,CAAC,CAAC,OAAS,SAAS,KAAK,MAAM;QAC/D,OAAO;YAAE;YAAO;QAAM;IACxB;IACA,OAAO;QAAE,OAAO;QAAG,OAAO;IAAE;AAC9B;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAI;QACF,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SAED;QAED,0DAA0D;QAC1D,MAAM,aAAa,IAAI,OAAO,CAAC,EAAE,EAAE,YAAY,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE;QAC9D,gEAAgE;QAChE,IAAI,QAAQ,IAAI,OACd;QAEF,OAAO,MAAM,IAAI,CAAC,UAAU,WAAW,IAAI,CAAC;IAC9C,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,+CAA+C;IAC/C,MAAM,kBAAkB,WAAW,KAAK,CAAC;IACzC,MAAM,OAAO,eAAe,CAAC,EAAE;IAC/B,MAAM,QAAQ,SAAS,eAAe,CAAC,EAAE;IACzC,MAAM,MAAM,eAAe,CAAC,EAAE;IAE9B,kCAAkC;IAClC,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,OAAO,IAAI,KAAK,MAAM,QAAQ,GAAG;IACvC,MAAM,YAAY,UAAU,CAAC,KAAK,QAAQ,GAAG;IAC7C,MAAM,eAAe,IAAI,QAAQ,CAAC,GAAG;IACrC,OAAO,YAAY,MAAM;AAC3B;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAI,OAAO,MACR,QAAQ,GAAG,oBAAoB;KAC/B,WAAW,GAAG,uBAAuB;KACrC,IAAI,GAAG,mCAAmC;KAC1C,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,iCAAiC;KACzD,OAAO,CAAC,QAAQ,MAAM,gDAAgD;IACzE,sCAAsC;IACtC,IAAI,CAAC,KAAK,UAAU,CAAC,MAAM;QACzB,OAAO,CAAC,CAAC,EAAE,MAAM;IACnB;IAEA,OAAO;AACT;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,QAAQ;IACZ,IAAI,UAAU,sHAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;QAC5B,QAAQ;IACV,OAAO,IAAI,UAAU,sHAAA,CAAA,QAAK,CAAC,MAAM,EAAE;QACjC,QAAQ;IACV,OAAO,IAAI,UAAU,sHAAA,CAAA,QAAK,CAAC,KAAK,EAAE;QAChC,QAAQ;IACV,OAAO,IAAI,UAAU,sHAAA,CAAA,QAAK,CAAC,KAAK,EAAE;QAChC,QAAQ;IACV,OAAO,IAAI,UAAU,sHAAA,CAAA,QAAK,CAAC,SAAS,EAAE;QACpC,QAAQ;IACV;IACA,OAAO;AACT;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC;AACpB;AAEO,MAAM,eAAe,CAAC;IAC3B,MAAM,eACJ;IAEF,MAAM,iBACJ;IAEF,MAAM,eACJ;IAEF,MAAM,2BACJ;IAEF,IAAI,aAAa,IAAI,CAAC,MAAM;QAC1B,OAAO;IACT,OAAO,IAAI,eAAe,IAAI,CAAC,MAAM;QACnC,OAAO;IACT,OAAO,IAAI,aAAa,IAAI,CAAC,MAAM;QACjC,OAAO;IACT,OAAO,IAAI,yBAAyB,IAAI,CAAC,MAAM;QAC7C,OAAO;IACT;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,QAAQ,SAAS,KAAK,CAAC,MAAM,CAAC,EAAE;IACtC,OAAO;AACT;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,UAAU,IAAI,IAAI,UAAU,YAAY,CAAC,GAAG,CAAC;IACnD,MAAM,WAAW,CAAC,iCAAiC,EAAE,SAAS;IAC9D,OAAO;AACT;AAEO,MAAM,gBAAgB,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,EAAE,cAAc,EAAE;IACxE,MAAM,OAAO,OAAO,MAAM,GAAG,SAAS;IACtC,MAAM,OAAO,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI;IACzC,MAAM,QAAQ,KAAK,MAAM,GAAG;IAE5B,OAAO,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG;AACvE;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,SAAS,EAAE;IAEjB,SAAS,uBAAuB,YAAY;QAC1C,OACE,cACI,IAAI,CAAC;YACL,IAAI,KAAK,IAAI,KAAK,QAAQ,OAAO,KAAK,IAAI,IAAI;YAC9C,IAAI,KAAK,OAAO,EAAE,OAAO,uBAAuB,KAAK,OAAO;YAC5D,OAAO;QACT,GACC,KAAK,OAAO;IAEnB;IAEA,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO,GAAG;QAC9B,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAE;YAC9B,IAAI,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,WAAW;gBACxD,MAAM,OAAO,uBAAuB,KAAK,OAAO,IAAI,EAAE;gBACtD,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,IAAI;YACxC;QACF;IACF;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,cAAc,OAAO;IAC1B,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAM,cAAc,MAAM,KAAK,KAAK;IACpC,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO,KAAK;IAE1C,OAAO,QAAQ,WAAW,GAAG,OAAO,CAAC,KAAK;AAC5C;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,OACJ,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM;AACnB;AAEO,MAAM,qBAAqB,CAAC,QAAQ,EAAE,EAAE,UAAU,IAAI;IAC3D,IAAI,CAAC,OAAO,OAAO;IACnB,MAAM,cAAc,MAAM,IAAI;IAC9B,IAAI,SAAS;QACX,OAAO,YAAY,KAAK,CAAC,KAAK,IAAI,CAAC;IACrC,OAAO;QACL,OAAO,YAAY,WAAW,GAAG,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS,IAAI,CAAC;IACnE;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IAAI,OAAO,CAAC,SAAS,CAAA,OAAQ,KAAK,WAAW;AACtD", "debugId": null}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/cards/FeaturedCard.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport { dateFormateWithTimeShort } from \"@/utils/Util\";\r\n\r\nconst FeaturedCard = ({\r\n  title = \"\",\r\n  description = \"\",\r\n  category = \"\",\r\n  image = \"\",\r\n  altName = \"\",\r\n  author = \"\",\r\n  timestamp = \"\",\r\n}) => {\r\n\r\n  return (\r\n    <>\r\n      <div className=\"featured-image\">\r\n        <div className=\"image-wrapper\">\r\n          <Image\r\n            width={480}\r\n            height={270}\r\n            src={image}\r\n            className=\"attachment-featured-image-480x270 size-featured-image-480x270 wp-post-image\"\r\n            alt={altName}\r\n            data-lazy-loaded=\"1\"\r\n            sizes=\"(min-width: 87.5rem) 1000px, (min-width: 78.75rem) 681px, (min-width: 48rem) 450px, (max-width: 48rem) 250px\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"entry\">\r\n        {category && <span className=\"entry__category\">{category}</span>}\r\n        <h3 className=\"entry__heading\">{title}</h3>\r\n        {description && <div className=\"entry__excerpt\">{description}</div>}\r\n        <div className=\"post-meta\">\r\n          <>\r\n            {author && (\r\n              <span\r\n                className=\"post-meta__author\"\r\n                aria-label={`Author: ${author}`}\r\n                itemProp=\"author\"\r\n                itemScope=\"\"\r\n                itemType=\"\"\r\n              >\r\n                <span className=\"by_author\">By</span>{\" \"}\r\n                <span className=\"author_name\">\r\n                  {author?.replace(/^By\\s+/i, \"\")}\r\n                </span>\r\n              </span>\r\n            )}\r\n            {/* {timestamp && (\r\n                <span className=\"post-meta__timestamp\">\r\n                  {dateFormateWithTimeShort(timestamp || \"\")}\r\n                </span>\r\n              )} */}\r\n          </>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default FeaturedCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAC,EACpB,QAAQ,EAAE,EACV,cAAc,EAAE,EAChB,WAAW,EAAE,EACb,QAAQ,EAAE,EACV,UAAU,EAAE,EACZ,SAAS,EAAE,EACX,YAAY,EAAE,EACf;IAEC,qBACE;;0BACE,0JAAC;gBAAI,WAAU;0BACb,cAAA,0JAAC;oBAAI,WAAU;8BACb,cAAA,0JAAC,yHAAA,CAAA,UAAK;wBACJ,OAAO;wBACP,QAAQ;wBACR,KAAK;wBACL,WAAU;wBACV,KAAK;wBACL,oBAAiB;wBACjB,OAAM;;;;;;;;;;;;;;;;0BAIZ,0JAAC;gBAAI,WAAU;;oBACZ,0BAAY,0JAAC;wBAAK,WAAU;kCAAmB;;;;;;kCAChD,0JAAC;wBAAG,WAAU;kCAAkB;;;;;;oBAC/B,6BAAe,0JAAC;wBAAI,WAAU;kCAAkB;;;;;;kCACjD,0JAAC;wBAAI,WAAU;kCACb,cAAA;sCACG,wBACC,0JAAC;gCACC,WAAU;gCACV,cAAY,CAAC,QAAQ,EAAE,QAAQ;gCAC/B,UAAS;gCACT,WAAU;gCACV,UAAS;;kDAET,0JAAC;wCAAK,WAAU;kDAAY;;;;;;oCAAU;kDACtC,0JAAC;wCAAK,WAAU;kDACb,QAAQ,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;AAc9C;KAvDM;uCAyDS", "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1842, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/buttons/Button.jsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport React from \"react\";\r\n\r\nconst Button = ({\r\n  href = \"#\",\r\n  target = null,\r\n  onClick = null,\r\n  className=\"\",\r\n  children,\r\n}) => {\r\n  return (\r\n    <>\r\n      {onClick ? (\r\n        <button\r\n          onClick={onClick}\r\n          className={`button_base black w-inline-block ${className}`}\r\n        >\r\n          {children}\r\n        </button>\r\n      ) : (\r\n        <Link\r\n          href={href}\r\n          target={target}\r\n          className={`button_base black w-inline-block ${className}`}\r\n        >\r\n          {children}\r\n        </Link>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Button;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS,CAAC,EACd,OAAO,GAAG,EACV,SAAS,IAAI,EACb,UAAU,IAAI,EACd,YAAU,EAAE,EACZ,QAAQ,EACT;IACC,qBACE;kBACG,wBACC,0JAAC;YACC,SAAS;YACT,WAAW,CAAC,iCAAiC,EAAE,WAAW;sBAEzD;;;;;iCAGH,0JAAC,wHAAA,CAAA,UAAI;YACH,MAAM;YACN,QAAQ;YACR,WAAW,CAAC,iCAAiC,EAAE,WAAW;sBAEzD;;;;;;;AAKX;KA3BM;uCA6BS", "debugId": null}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/categories/SubListSection.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport Link from \"next/link\";\r\nimport FeaturedCard from \"@/components/cards/FeaturedCard\";\r\nimport Button from \"../buttons/Button\";\r\nimport { getAuthorText } from \"@/utils/Util\";\r\nconst SubListSection = ({ data = [], hasMore = false, handleShowMore }) => {\r\n  return (\r\n    <>\r\n      {data && data.length > 0 && (\r\n        <section id=\"list-section\">\r\n          <div className=\"containerWrapper\">\r\n            <div className=\"sectioner sectioner--featured-category sectioner--the-latest-posts mb-0\">\r\n              <div className=\"featured-category__secondary-wrapper the-latest__secondary-wrapper category_space\">\r\n                {data?.map((item, index) => {\r\n                  const authorCount =\r\n                    item?.author && item?.author?.length > 1\r\n                      ? item?.author.length - 1\r\n                      : 0;\r\n                  return (\r\n                    <Link\r\n                      href={item?.slug || \"#\"}\r\n                      key={`featured-card-${index}`}\r\n                      className=\"featured-category__story the-latest__story\"\r\n                    >\r\n                      <FeaturedCard\r\n                        title={item?.title || \"\"}\r\n                        // category={item?.category || \"\"}\r\n                        image={\r\n                          item?.croppedImg\r\n                            ? item?.croppedImg\r\n                            : item?.coverImg\r\n                            ? item?.coverImg\r\n                            : \"\"\r\n                        }\r\n                        altName={item?.altName || \"\"}\r\n                        author={getAuthorText(\r\n                          \"By\",\r\n                          item?.author,\r\n                          item?.contributor\r\n                        )}\r\n                        timestamp={item?.timestamp || \"\"}\r\n                      />\r\n                    </Link>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n            {hasMore && (\r\n              <div className=\"hasMore_btn_wrap\">\r\n                <Button onClick={handleShowMore}>See More</Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </section>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default SubListSection;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AACA,MAAM,iBAAiB,CAAC,EAAE,OAAO,EAAE,EAAE,UAAU,KAAK,EAAE,cAAc,EAAE;IACpE,qBACE;kBACG,QAAQ,KAAK,MAAM,GAAG,mBACrB,0JAAC;YAAQ,IAAG;sBACV,cAAA,0JAAC;gBAAI,WAAU;;kCACb,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC,MAAM;gCAChB,MAAM,cACJ,MAAM,UAAU,MAAM,QAAQ,SAAS,IACnC,MAAM,OAAO,SAAS,IACtB;gCACN,qBACE,0JAAC,wHAAA,CAAA,UAAI;oCACH,MAAM,MAAM,QAAQ;oCAEpB,WAAU;8CAEV,cAAA,0JAAC,uIAAA,CAAA,UAAY;wCACX,OAAO,MAAM,SAAS;wCACtB,kCAAkC;wCAClC,OACE,MAAM,aACF,MAAM,aACN,MAAM,WACN,MAAM,WACN;wCAEN,SAAS,MAAM,WAAW;wCAC1B,QAAQ,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAClB,MACA,MAAM,QACN,MAAM;wCAER,WAAW,MAAM,aAAa;;;;;;mCAnB3B,CAAC,cAAc,EAAE,OAAO;;;;;4BAuBnC;;;;;;;;;;;oBAGH,yBACC,0JAAC;wBAAI,WAAU;kCACb,cAAA,0JAAC,mIAAA,CAAA,UAAM;4BAAC,SAAS;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAQjD;KApDM;uCAsDS", "debugId": null}}, {"offset": {"line": 1980, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/pages/api/Headers.jsx"], "sourcesContent": ["export default function Headers(method, body) {\r\n  this.method = method;\r\n  this.headers = {\r\n    \"Access-Control-Allow-Origin\": \"*\",\r\n    \"Content-Type\": \"application/json\",\r\n  };\r\n  this.headers.dbtoken = \"Bearer \" + process.env.NEXT_PUBLIC_CLIENT_ID;\r\n  if (body) {\r\n    this.body = JSON.stringify(body);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAMqC;AANtB,SAAS,QAAQ,MAAM,EAAE,IAAI;IAC1C,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,OAAO,GAAG;QACb,+BAA+B;QAC/B,gBAAgB;IAClB;IACA,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG;IACvB,IAAI,MAAM;QACR,IAAI,CAAC,IAAI,GAAG,KAAK,SAAS,CAAC;IAC7B;AACF;KAVwB", "debugId": null}}, {"offset": {"line": 2007, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2013, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/pages/api/CategoryApi.jsx"], "sourcesContent": ["import Headers from \"./Headers\";\r\nimport { ProcessAPI, Const } from \"@/utils/Constants\";\r\n\r\n// Get Category List\r\nexport const getCategory = async (body) => {\r\n  const res = await fetch(Const.Link + `/api/tenant/category-list?slug=${body.slug}&limit=${body.limit}&offset=${body.offset}`, new Headers(\"GET\")\r\n  );\r\n  return ProcessAPI(res);\r\n};\r\n\r\nexport const getSubmenus = async (slug) => {\r\n  const res = await fetch(Const.Link + `/api/tenant/category-submenus?slug=${slug}`, new Headers(\"GET\")\r\n  );\r\n  return ProcessAPI(res);\r\n}\r\n\r\nexport const getCategoryLatestAricles = async (body) => {\r\n  const res = await fetch(Const.Link + `/api/tenant/category-latest?slug=${body.slug}`, new Headers(\"GET\")\r\n  );\r\n  return ProcessAPI(res);\r\n};\r\n\r\n// Get Category Sitemap\r\nexport const getCategorySitemap = async (slug) => {\r\n  const res = await fetch(Const.Link + \"/api/tenant/category-sitemap\", new Headers(\"GET\"));\r\n  return ProcessAPI(res);\r\n};\r\n\r\nexport const getCategoryVideos = async (body) => {\r\n  const res = await fetch(Const.Link + `/api/tenant/category-video?slug=${body.slug}&shorts=${body.shorts}&limit=${body.limit}&offset=${body.offset}`, new Headers(\"GET\")\r\n  );\r\n  return ProcessAPI(res);\r\n};\r\n\r\n// Get Video Category Sitemap\r\nexport const getVideoCategorySitemap = async (slug) => {\r\n  const res = await fetch(Const.Link + \"/api/tenant/video-category-sitemap\", new Headers(\"GET\"));\r\n  return ProcessAPI(res);\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGO,MAAM,cAAc,OAAO;IAChC,MAAM,MAAM,MAAM,MAAM,sHAAA,CAAA,QAAK,CAAC,IAAI,GAAG,CAAC,+BAA+B,EAAE,KAAK,IAAI,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,EAAE,IAAI,2HAAA,CAAA,UAAO,CAAC;IAE1I,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AAEO,MAAM,cAAc,OAAO;IAChC,MAAM,MAAM,MAAM,MAAM,sHAAA,CAAA,QAAK,CAAC,IAAI,GAAG,CAAC,mCAAmC,EAAE,MAAM,EAAE,IAAI,2HAAA,CAAA,UAAO,CAAC;IAE/F,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AAEO,MAAM,2BAA2B,OAAO;IAC7C,MAAM,MAAM,MAAM,MAAM,sHAAA,CAAA,QAAK,CAAC,IAAI,GAAG,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE,EAAE,IAAI,2HAAA,CAAA,UAAO,CAAC;IAElG,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AAGO,MAAM,qBAAqB,OAAO;IACvC,MAAM,MAAM,MAAM,MAAM,sHAAA,CAAA,QAAK,CAAC,IAAI,GAAG,gCAAgC,IAAI,2HAAA,CAAA,UAAO,CAAC;IACjF,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AAEO,MAAM,oBAAoB,OAAO;IACtC,MAAM,MAAM,MAAM,MAAM,sHAAA,CAAA,QAAK,CAAC,IAAI,GAAG,CAAC,gCAAgC,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,EAAE,IAAI,2HAAA,CAAA,UAAO,CAAC;IAEjK,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AAGO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,MAAM,MAAM,MAAM,sHAAA,CAAA,QAAK,CAAC,IAAI,GAAG,sCAAsC,IAAI,2HAAA,CAAA,UAAO,CAAC;IACvF,OAAO,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;AACpB", "debugId": null}}, {"offset": {"line": 2052, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2058, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/pages/%5Bcategory%5D/%5Bsubcategory%5D/index.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useRouter } from \"next/router\";\r\nimport SeoHeader from \"@/components/seo/SeoHeader\";\r\nimport BreadcrumbSchema from \"@/components/seo/BreadcrumbSchema\";\r\nimport Hero from \"@/components/categories/Hero\";\r\nimport SubListSection from \"@/components/categories/SubListSection\";\r\nimport { Const } from \"@/utils/Constants\";\r\nimport { getCategory } from \"@/pages/api/CategoryApi\";\r\n\r\nconst Subcategory = ({\r\n  title,\r\n  breadcrumbs,\r\n  initialData,\r\n  initialCount,\r\n  meta,\r\n}) => {\r\n  const router = useRouter();\r\n  const [data, setData] = useState(initialData);\r\n  const [count, setCount] = useState(initialCount);\r\n  const [offset, setOffset] = useState(Const.Limit);\r\n  const [hasMore, setHasMore] = useState(initialCount > initialData.length);\r\n\r\n  useEffect(() => {\r\n    setData(initialData);\r\n    setCount(initialCount);\r\n    setOffset(Const.Limit);\r\n    setHasMore(initialCount > initialData.length);\r\n  }, [router.query.subcategory, initialData]);\r\n\r\n  const handleShowMore = async () => {\r\n    const newOffset = offset + Const.Limit;\r\n    setOffset(newOffset);\r\n\r\n    const payload = {\r\n      slug: `/${router.query.category}/${router.query.subcategory}`,\r\n      limit: Const.Limit,\r\n      offset: offset,\r\n    };\r\n\r\n    try {\r\n      const response = await getCategory(payload);\r\n      if (!response?.data?.data.length) {\r\n        setHasMore(false);\r\n        return;\r\n      }\r\n      setData((prev) => [...prev, ...response.data.data]);\r\n      setHasMore(response.data.count > newOffset);\r\n    } catch (e) {\r\n      console.error(\"Error fetching more categories:\", e);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <SeoHeader meta={meta} />\r\n      <BreadcrumbSchema itemList={breadcrumbs?.slice(0, 1) ?? []} />\r\n      <div className=\"categoryWrapper\">\r\n        <Hero\r\n          title={breadcrumbs[breadcrumbs.length - 1]?.name || title}\r\n          breadcrumbs={breadcrumbs}\r\n          classH1={\"line-height-10\"}\r\n        />\r\n        <SubListSection\r\n          data={data}\r\n          hasMore={hasMore}\r\n          handleShowMore={handleShowMore}\r\n        />\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Subcategory;\r\n\r\nexport async function getServerSideProps(context) {\r\n  const url = `/${context.params.category}/${context.params.subcategory}`;\r\n  try {\r\n    const payload = {\r\n      slug: url,\r\n      limit: Const.Limit,\r\n      offset: Const.Offset,\r\n    };\r\n\r\n    const response = await getCategory(payload);\r\n    if (response.status !== \"success\" || !response?.data?.isExists) {\r\n      return {\r\n        notFound: true,\r\n      };\r\n    }\r\n    \r\n    const { data, title, breadcrumbs, meta, count } = response.data;\r\n    return {\r\n      props: {\r\n        title: title ?? \"\",\r\n        breadcrumbs: breadcrumbs ?? [],\r\n        initialData: data ?? [],\r\n        initialCount: count ?? 0,\r\n        meta: meta ?? {},\r\n      },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching data:\", error.message);\r\n    return {\r\n      props: {\r\n        title: \"\",\r\n        breadcrumbs: [],\r\n        initialData: [],\r\n        initialCount: 0,\r\n        meta: {},\r\n      },\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,cAAc,CAAC,EACnB,KAAK,EACL,WAAW,EACX,WAAW,EACX,YAAY,EACZ,IAAI,EACL;;IACC,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,sHAAA,CAAA,QAAK,CAAC,KAAK;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY,MAAM;IAExE,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;iCAAE;YACR,QAAQ;YACR,SAAS;YACT,UAAU,sHAAA,CAAA,QAAK,CAAC,KAAK;YACrB,WAAW,eAAe,YAAY,MAAM;QAC9C;gCAAG;QAAC,OAAO,KAAK,CAAC,WAAW;QAAE;KAAY;IAE1C,MAAM,iBAAiB;QACrB,MAAM,YAAY,SAAS,sHAAA,CAAA,QAAK,CAAC,KAAK;QACtC,UAAU;QAEV,MAAM,UAAU;YACd,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,WAAW,EAAE;YAC7D,OAAO,sHAAA,CAAA,QAAK,CAAC,KAAK;YAClB,QAAQ;QACV;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE;YACnC,IAAI,CAAC,UAAU,MAAM,KAAK,QAAQ;gBAChC,WAAW;gBACX;YACF;YACA,QAAQ,CAAC,OAAS;uBAAI;uBAAS,SAAS,IAAI,CAAC,IAAI;iBAAC;YAClD,WAAW,SAAS,IAAI,CAAC,KAAK,GAAG;QACnC,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,qBACE;;0BACE,0JAAC,kIAAA,CAAA,UAAS;gBAAC,MAAM;;;;;;0BACjB,0JAAC,yIAAA,CAAA,UAAgB;gBAAC,UAAU,aAAa,MAAM,GAAG,MAAM,EAAE;;;;;;0BAC1D,0JAAC;gBAAI,WAAU;;kCACb,0JAAC,oIAAA,CAAA,UAAI;wBACH,OAAO,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,EAAE,QAAQ;wBACpD,aAAa;wBACb,SAAS;;;;;;kCAEX,0JAAC,8IAAA,CAAA,UAAc;wBACb,MAAM;wBACN,SAAS;wBACT,gBAAgB;;;;;;;;;;;;;;AAK1B;GA7DM;;QAOW,0HAAA,CAAA,YAAS;;;KAPpB;;uCA+DS", "debugId": null}}, {"offset": {"line": 2181, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2186, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/[category]/[subcategory]\";\n\n/// <reference types=\"next/client\" />\r\n\r\n// inserted by rust code\r\ndeclare const PAGE_PATH: string\r\n\r\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\r\n;(window.__NEXT_P = window.__NEXT_P || []).push([\r\n  PAGE_PATH,\r\n  () => {\r\n    return require('PAGE')\r\n  },\r\n])\r\n// @ts-expect-error module.hot exists\r\nif (module.hot) {\r\n  // @ts-expect-error module.hot exists\r\n  module.hot.dispose(function () {\r\n    window.__NEXT_P.push([PAGE_PATH])\r\n  })\r\n}\r\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}