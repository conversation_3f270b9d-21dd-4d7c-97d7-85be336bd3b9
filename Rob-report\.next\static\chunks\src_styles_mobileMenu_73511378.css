/* [project]/src/styles/mobileMenu.css [client] (css) */
.menu-cont {
  position: fixed;
  top: 0;
  height: 100dvh;
  width: 100vw;
  z-index: 999;
  transition: all .4s;
  display: none;
}

.menu-inner {
  width: 100%;
  margin-left: auto;
  background-color: #000;
}

.manu-inner-block, .menu-inner {
  height: 100dvh;
  transition: all .3s ease-out;
}

.manu-inner-block {
  width: 100%;
}

.menu-top {
  display: flex;
  height: 80px;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  padding: 3vh 4vw;
}

.flex-all {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.menu-back {
  position: relative;
  width: 40px;
  height: 40px;
  font-size: 20px;
  background-color: #fff;
  transition: all .3s;
  border-radius: 50%;
}

.menu-close {
  width: 40px;
  height: 40px;
  font-size: 19px;
  background-color: #fff;
  transition: all .3s;
  border-radius: 50%;
}

.menu-close svg {
  color: #000 !important;
}

.menu-back svg {
  color: #000 !important;
}

.menu-main {
  position: relative;
  flex-direction: column;
  align-items: normal;
  height: calc(100dvh - 140px);
  width: 100%;
  overflow: auto;
  padding: 3vh 4vw;
}

.menu-extras, .menu-main {
  display: flex;
  justify-content: space-between;
}

.menu-items {
  height: 50px;
  color: #fff;
  width: 100%;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.menu-name {
  font-size: 1.25rem;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
  width: 100%;
  display: inline-block;
}

.menu-arrow {
  position: absolute;
  right: 0;
  font-size: 18px;
  transition: all .3s;
}

.menu-extras {
  align-items: flex-end;
}

.menu-ext {
  display: flex;
  align-items: center;
  color: #ffffffe6;
  font-size: 16px;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 400;
  letter-spacing: 1px;
  margin: 8px 0;
  transition: all .3s;
}

.menu-btm {
  width: 100%;
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
  margin: auto;
  gap: 25px;
  padding: 0 4vw;
  border-top: 1px solid #fff;
}

.menu-follows {
  margin-left: 10px;
}

.menu-follows .menu-follows-items {
  display: flex;
  align-items: center;
  gap: 10px;
}

.menu-follows .menu-follows-items a {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
}

.menu-follows .menu-follows-items svg {
  font-size: 18px;
}

.menu-follows-text {
}

.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.submenu-head, .submenu-items {
  color: #fff;
  width: 100%;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.submenu-head {
}

.submenu-items {
  margin-bottom: 16px;
}

.submenu-name, .submenu-title {
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 500;
  letter-spacing: 1px;
  line-height: 1.5;
  text-transform: uppercase;
}

.submenu-name {
  font-size: 20px;
  gap: 15px;
  width: 100%;
  display: inline-block;
  color: #e0e0e0bf !important;
}

.submenu-title {
  font-size: 26px;
  width: 100%;
  line-height: 1.2;
  margin-bottom: 20px;
  color: #fff !important;
}

@media (width <= 61.1875rem) {
  .menu-cont {
    display: flex;
  }
}

/*# sourceMappingURL=src_styles_mobileMenu_73511378.css.map*/