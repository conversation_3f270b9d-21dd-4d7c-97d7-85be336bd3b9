<!DOCTYPE HTML>
<html lang="en-us">
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title>$script.js - benchmarks</title>
    <style type="text/css">
      body {
        width: 650px;
        margin: 10px auto;
        font: 300 16px 'helvetica neue', helvetica, arial;
      }
      h1 {
        border-bottom: 3px solid #333;
      }

      strong {
        font-weight: bold;
      }
      #window-ms,
      #dom-ms {
        font-weight: bold;
        font-size: 20px;
      }
    </style>
    <script src="../dist/script.js"></script>
    <script type="text/javascript">
      var start = (+new Date),
          onloadTime = 0,
          libsReady = 0;
      window.onload = function() {
        document.getElementById('window-ms').innerHTML = (+new Date) - start;
      };
      // load a bunch of libraries
      $script('../vendor/yui-utilities.js?' + start, 'yui', function() {
        YAHOO.util.Dom.get('yui').innerHTML = '✓';
      });
      $script('../vendor/jquery.js?' + start, 'jquery', function() {
        $('#jquery').html('✓');
      });
      $script('../vendor/mootools.js?' + start, 'moo', function() {
        document.id('mootools').innerHTML = '✓';
      });
      $script.ready(['moo', 'jquery', 'yui'], function() {
        document.getElementById('total').innerHTML = (+new Date) - start;
      });
    </script>
  </head>
  <body>
    <h1>Benchmark Test</h1>
    <div id="ready">
      <img src="../vendor/bird.png">
      <p>window.onload time: <span id="window-ms">0</span>ms</p>
      <ul>
        <li>YUI: <span id="yui"></span></li>
        <li>jQuery: <span id="jquery"></span></li>
        <li>Mootools: <span id="mootools"></span></li>
        <li>Total: <strong id="total">0</strong>ms using $script.js</li>
      </ul>
    </div>
  </body>
</html>
