/* [project]/src/styles/navbar.css [client] (css) */
.nav {
  position: relative;
  padding: 2rem 3rem;
  color: var(--text-color);
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s, color .45s ease-in .5s;
  position: sticky;
  top: 0;
  z-index: 999;
}

.brand {
  cursor: pointer;
  justify-self: left;
  font-size: 2vw;
  font-family: rocky, sans-serif;
  display: flex;
}

.brand img {
  width: 200px;
  height: auto;
  filter: var(--filterblack);
  transition: filter .45s ease-in .5s;
}

.nav-content {
  align-items: center;
  display: flex;
  justify-content: space-between;
  height: 2.5rem;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 1;
  overflow: hidden;
}

.links.nav-items {
  cursor: pointer;
  text-transform: uppercase;
  color: var(--text-color);
  transition: color .45s ease-in;
  display: none;
}

.links.nav-items ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.links.nav-items .menu-linksul {
  display: flex;
  text-transform: uppercase;
  width: 100%;
  flex-direction: column;
  padding: .625rem;
}

.links.nav-items li:not(:last-of-type) {
  margin-bottom: .625rem;
}

.links.nav-items .menu-linksul a {
  font-size: 1vw;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 23px;
  letter-spacing: .08rem;
}

.menu {
  display: none;
  font-family: sweet-sans-pro, sans-serif;
  transition: color .45s ease-in;
}

.toggle_cntr {
  position: relative;
  cursor: pointer;
  transition: all .3s;
  width: 40px;
  height: 40px;
}

.toggle_cntr:hover {
  scale: .92;
}

.toggle_cntr svg {
  transition: all .3s;
}

.icon {
  display: none;
  transition: transform .45s;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 8px;
  border: 1px solid #000;
  font-size: 1rem;
  cursor: pointer;
  background-color: #fff;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
}

.toggle_cntr .icon:hover svg {
  rotate: 25deg;
}

.icon.scale-0 {
  transform: scale(0);
}

.icon.scale-1 {
  transform: scale(1);
}

.menu-item {
  position: relative;
}

.nav_active {
  position: relative;
}

.menu-item a:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 1px;
  background-color: red;
  transition: width .45s;
}

.menu-item a.nav_active:after {
  width: 100% !important;
}

.menu-item a:hover:after {
  width: 100%;
}

@media only screen and (width >= 61.25rem) {
  .nav {
    padding: 2rem;
    border-bottom: none;
  }

  .links.nav-items {
    display: flex;
    flex-basis: 80%;
  }

  .links.nav-items .menu-linksul {
    align-items: center;
    justify-content: center;
    flex-direction: row;
    padding: 0;
    gap: 1.5rem;
  }

  .links.nav-items li:not(:last-of-type) {
    margin: 0;
  }

  .brand img {
    width: 200px;
    height: auto;
  }

  .toggle_cntr .icon svg {
    font-size: 25px;
  }

  .toggle_cntr .icon:first-child svg {
    font-size: 30px;
  }
}

@media only screen and (width <= 61.1875rem) {
  .nav {
    padding: 1rem;
  }

  .menu {
    display: flex;
  }

  .brand img {
    width: 160px;
    height: auto;
  }

  .brand {
    order: 2;
  }

  .material-icons.menu {
    font-size: 25px;
    cursor: pointer;
    line-height: 1;
    order: 3;
  }

  .toggle_cntr svg {
    font-size: 25px;
  }

  .toggle_cntr {
    order: 1;
    justify-content: flex-start;
  }
}

@media screen and (width <= 1200px) {
  .links.nav-items .menu-linksul a {
    font-size: 1.2vw;
  }
}

@media only screen and (width <= 768px) {
  
}

/*# sourceMappingURL=src_styles_navbar_73511378.css.map*/