/* [project]/node_modules/react-tweet/dist/twitter-theme/theme.css [client] (css) */
.react-tweet-theme {
  --tweet-container-margin: 1.5rem 0;
  --tweet-header-font-size: .9375rem;
  --tweet-header-line-height: 1.25rem;
  --tweet-body-font-size: 1.25rem;
  --tweet-body-font-weight: 400;
  --tweet-body-line-height: 1.5rem;
  --tweet-body-margin: 0;
  --tweet-quoted-container-margin: .75rem 0;
  --tweet-quoted-body-font-size: .938rem;
  --tweet-quoted-body-font-weight: 400;
  --tweet-quoted-body-line-height: 1.25rem;
  --tweet-quoted-body-margin: .25rem 0 .75rem 0;
  --tweet-info-font-size: .9375rem;
  --tweet-info-line-height: 1.25rem;
  --tweet-actions-font-size: .875rem;
  --tweet-actions-line-height: 1rem;
  --tweet-actions-font-weight: 700;
  --tweet-actions-icon-size: 1.25em;
  --tweet-actions-icon-wrapper-size: calc(var(--tweet-actions-icon-size)  + .75em);
  --tweet-replies-font-size: .875rem;
  --tweet-replies-line-height: 1rem;
  --tweet-replies-font-weight: 700;
}

:where(.react-tweet-theme) * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:is([data-theme="light"], .light) :where(.react-tweet-theme), :where(.react-tweet-theme) {
  --tweet-skeleton-gradient: linear-gradient(270deg, #fafafa, #eaeaea, #eaeaea, #fafafa);
  --tweet-border: 1px solid #cfd9de;
  --tweet-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --tweet-font-color: #0f1419;
  --tweet-font-color-secondary: #536471;
  --tweet-bg-color: #fff;
  --tweet-bg-color-hover: #f7f9f9;
  --tweet-quoted-bg-color-hover: #00000008;
  --tweet-color-blue-primary: #1d9bf0;
  --tweet-color-blue-primary-hover: #1a8cd8;
  --tweet-color-blue-secondary: #006fd6;
  --tweet-color-blue-secondary-hover: #006fd61a;
  --tweet-color-red-primary: #f91880;
  --tweet-color-red-primary-hover: #f918801a;
  --tweet-color-green-primary: #00ba7c;
  --tweet-color-green-primary-hover: #00ba7c1a;
  --tweet-twitter-icon-color: var(--tweet-font-color);
  --tweet-verified-old-color: #829aab;
  --tweet-verified-blue-color: var(--tweet-color-blue-primary);
}

:is([data-theme="dark"], .dark) :where(.react-tweet-theme) {
  --tweet-skeleton-gradient: linear-gradient(270deg, #15202b, #1e2732, #1e2732, #15202b);
  --tweet-border: 1px solid #425364;
  --tweet-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --tweet-font-color: #f7f9f9;
  --tweet-font-color-secondary: #8b98a5;
  --tweet-bg-color: #15202b;
  --tweet-bg-color-hover: #1e2732;
  --tweet-quoted-bg-color-hover: #ffffff08;
  --tweet-color-blue-primary: #1d9bf0;
  --tweet-color-blue-primary-hover: #1a8cd8;
  --tweet-color-blue-secondary: #6bc9fb;
  --tweet-color-blue-secondary-hover: #6bc9fb1a;
  --tweet-color-red-primary: #f91880;
  --tweet-color-red-primary-hover: #f918801a;
  --tweet-color-green-primary: #00ba7c;
  --tweet-color-green-primary-hover: #00ba7c1a;
  --tweet-twitter-icon-color: var(--tweet-font-color);
  --tweet-verified-old-color: #829aab;
  --tweet-verified-blue-color: #fff;
}

@media (prefers-color-scheme: dark) {
  :where(.react-tweet-theme) {
    --tweet-skeleton-gradient: linear-gradient(270deg, #15202b, #1e2732, #1e2732, #15202b);
    --tweet-border: 1px solid #425364;
    --tweet-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --tweet-font-color: #f7f9f9;
    --tweet-font-color-secondary: #8b98a5;
    --tweet-bg-color: #15202b;
    --tweet-bg-color-hover: #1e2732;
    --tweet-color-blue-primary: #1d9bf0;
    --tweet-color-blue-primary-hover: #1a8cd8;
    --tweet-color-blue-secondary: #6bc9fb;
    --tweet-color-blue-secondary-hover: #6bc9fb1a;
    --tweet-color-red-primary: #f91880;
    --tweet-color-red-primary-hover: #f918801a;
    --tweet-color-green-primary: #00ba7c;
    --tweet-color-green-primary-hover: #00ba7c1a;
    --tweet-twitter-icon-color: var(--tweet-font-color);
    --tweet-verified-old-color: #829aab;
    --tweet-verified-blue-color: #fff;
  }
}

/*# sourceMappingURL=node_modules_react-tweet_dist_twitter-theme_theme_73511378.css.map*/