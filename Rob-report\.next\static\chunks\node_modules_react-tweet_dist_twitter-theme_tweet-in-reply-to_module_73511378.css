/* [project]/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css [client] (css) */
.tweet-in-reply-to-module__Lv55MG__root {
  text-decoration: none;
  color: var(--tweet-font-color-secondary);
  font-size: .9375rem;
  line-height: 1.25rem;
  margin-bottom: .25rem;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.tweet-in-reply-to-module__Lv55MG__root:hover {
  text-decoration-thickness: 1px;
  text-decoration-line: underline;
}

/*# sourceMappingURL=node_modules_react-tweet_dist_twitter-theme_tweet-in-reply-to_module_73511378.css.map*/