(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/[root of the server]__2dfa5594._.js", {

"[turbopack]/browser/dev/hmr-client/hmr-client.ts [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/// <reference path="../../../shared/runtime-types.d.ts" />
/// <reference path="../../runtime/base/dev-globals.d.ts" />
/// <reference path="../../runtime/base/dev-protocol.d.ts" />
/// <reference path="../../runtime/base/dev-extensions.ts" />
__turbopack_context__.s({
    "connect": (()=>connect),
    "setHooks": (()=>setHooks),
    "subscribeToUpdate": (()=>subscribeToUpdate)
});
function connect({ addMessageListener, sendMessage, onUpdateError = console.error }) {
    addMessageListener((msg)=>{
        switch(msg.type){
            case "turbopack-connected":
                handleSocketConnected(sendMessage);
                break;
            default:
                try {
                    if (Array.isArray(msg.data)) {
                        for(let i = 0; i < msg.data.length; i++){
                            handleSocketMessage(msg.data[i]);
                        }
                    } else {
                        handleSocketMessage(msg.data);
                    }
                    applyAggregatedUpdates();
                } catch (e) {
                    console.warn("[Fast Refresh] performing full reload\n\n" + "Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\n" + "You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\n" + "Consider migrating the non-React component export to a separate file and importing it into both files.\n\n" + "It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\n" + "Fast Refresh requires at least one parent function component in your React tree.");
                    onUpdateError(e);
                    location.reload();
                }
                break;
        }
    });
    const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;
    if (queued != null && !Array.isArray(queued)) {
        throw new Error("A separate HMR handler was already registered");
    }
    globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {
        push: ([chunkPath, callback])=>{
            subscribeToChunkUpdate(chunkPath, sendMessage, callback);
        }
    };
    if (Array.isArray(queued)) {
        for (const [chunkPath, callback] of queued){
            subscribeToChunkUpdate(chunkPath, sendMessage, callback);
        }
    }
}
const updateCallbackSets = new Map();
function sendJSON(sendMessage, message) {
    sendMessage(JSON.stringify(message));
}
function resourceKey(resource) {
    return JSON.stringify({
        path: resource.path,
        headers: resource.headers || null
    });
}
function subscribeToUpdates(sendMessage, resource) {
    sendJSON(sendMessage, {
        type: "turbopack-subscribe",
        ...resource
    });
    return ()=>{
        sendJSON(sendMessage, {
            type: "turbopack-unsubscribe",
            ...resource
        });
    };
}
function handleSocketConnected(sendMessage) {
    for (const key of updateCallbackSets.keys()){
        subscribeToUpdates(sendMessage, JSON.parse(key));
    }
}
// we aggregate all pending updates until the issues are resolved
const chunkListsWithPendingUpdates = new Map();
function aggregateUpdates(msg) {
    const key = resourceKey(msg.resource);
    let aggregated = chunkListsWithPendingUpdates.get(key);
    if (aggregated) {
        aggregated.instruction = mergeChunkListUpdates(aggregated.instruction, msg.instruction);
    } else {
        chunkListsWithPendingUpdates.set(key, msg);
    }
}
function applyAggregatedUpdates() {
    if (chunkListsWithPendingUpdates.size === 0) return;
    hooks.beforeRefresh();
    for (const msg of chunkListsWithPendingUpdates.values()){
        triggerUpdate(msg);
    }
    chunkListsWithPendingUpdates.clear();
    finalizeUpdate();
}
function mergeChunkListUpdates(updateA, updateB) {
    let chunks;
    if (updateA.chunks != null) {
        if (updateB.chunks == null) {
            chunks = updateA.chunks;
        } else {
            chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);
        }
    } else if (updateB.chunks != null) {
        chunks = updateB.chunks;
    }
    let merged;
    if (updateA.merged != null) {
        if (updateB.merged == null) {
            merged = updateA.merged;
        } else {
            // Since `merged` is an array of updates, we need to merge them all into
            // one, consistent update.
            // Since there can only be `EcmascriptMergeUpdates` in the array, there is
            // no need to key on the `type` field.
            let update = updateA.merged[0];
            for(let i = 1; i < updateA.merged.length; i++){
                update = mergeChunkListEcmascriptMergedUpdates(update, updateA.merged[i]);
            }
            for(let i = 0; i < updateB.merged.length; i++){
                update = mergeChunkListEcmascriptMergedUpdates(update, updateB.merged[i]);
            }
            merged = [
                update
            ];
        }
    } else if (updateB.merged != null) {
        merged = updateB.merged;
    }
    return {
        type: "ChunkListUpdate",
        chunks,
        merged
    };
}
function mergeChunkListChunks(chunksA, chunksB) {
    const chunks = {};
    for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA)){
        const chunkUpdateB = chunksB[chunkPath];
        if (chunkUpdateB != null) {
            const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);
            if (mergedUpdate != null) {
                chunks[chunkPath] = mergedUpdate;
            }
        } else {
            chunks[chunkPath] = chunkUpdateA;
        }
    }
    for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB)){
        if (chunks[chunkPath] == null) {
            chunks[chunkPath] = chunkUpdateB;
        }
    }
    return chunks;
}
function mergeChunkUpdates(updateA, updateB) {
    if (updateA.type === "added" && updateB.type === "deleted" || updateA.type === "deleted" && updateB.type === "added") {
        return undefined;
    }
    if (updateA.type === "partial") {
        invariant(updateA.instruction, "Partial updates are unsupported");
    }
    if (updateB.type === "partial") {
        invariant(updateB.instruction, "Partial updates are unsupported");
    }
    return undefined;
}
function mergeChunkListEcmascriptMergedUpdates(mergedA, mergedB) {
    const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);
    const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);
    return {
        type: "EcmascriptMergedUpdate",
        entries,
        chunks
    };
}
function mergeEcmascriptChunkEntries(entriesA, entriesB) {
    return {
        ...entriesA,
        ...entriesB
    };
}
function mergeEcmascriptChunksUpdates(chunksA, chunksB) {
    if (chunksA == null) {
        return chunksB;
    }
    if (chunksB == null) {
        return chunksA;
    }
    const chunks = {};
    for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA)){
        const chunkUpdateB = chunksB[chunkPath];
        if (chunkUpdateB != null) {
            const mergedUpdate = mergeEcmascriptChunkUpdates(chunkUpdateA, chunkUpdateB);
            if (mergedUpdate != null) {
                chunks[chunkPath] = mergedUpdate;
            }
        } else {
            chunks[chunkPath] = chunkUpdateA;
        }
    }
    for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB)){
        if (chunks[chunkPath] == null) {
            chunks[chunkPath] = chunkUpdateB;
        }
    }
    if (Object.keys(chunks).length === 0) {
        return undefined;
    }
    return chunks;
}
function mergeEcmascriptChunkUpdates(updateA, updateB) {
    if (updateA.type === "added" && updateB.type === "deleted") {
        // These two completely cancel each other out.
        return undefined;
    }
    if (updateA.type === "deleted" && updateB.type === "added") {
        const added = [];
        const deleted = [];
        const deletedModules = new Set(updateA.modules ?? []);
        const addedModules = new Set(updateB.modules ?? []);
        for (const moduleId of addedModules){
            if (!deletedModules.has(moduleId)) {
                added.push(moduleId);
            }
        }
        for (const moduleId of deletedModules){
            if (!addedModules.has(moduleId)) {
                deleted.push(moduleId);
            }
        }
        if (added.length === 0 && deleted.length === 0) {
            return undefined;
        }
        return {
            type: "partial",
            added,
            deleted
        };
    }
    if (updateA.type === "partial" && updateB.type === "partial") {
        const added = new Set([
            ...updateA.added ?? [],
            ...updateB.added ?? []
        ]);
        const deleted = new Set([
            ...updateA.deleted ?? [],
            ...updateB.deleted ?? []
        ]);
        if (updateB.added != null) {
            for (const moduleId of updateB.added){
                deleted.delete(moduleId);
            }
        }
        if (updateB.deleted != null) {
            for (const moduleId of updateB.deleted){
                added.delete(moduleId);
            }
        }
        return {
            type: "partial",
            added: [
                ...added
            ],
            deleted: [
                ...deleted
            ]
        };
    }
    if (updateA.type === "added" && updateB.type === "partial") {
        const modules = new Set([
            ...updateA.modules ?? [],
            ...updateB.added ?? []
        ]);
        for (const moduleId of updateB.deleted ?? []){
            modules.delete(moduleId);
        }
        return {
            type: "added",
            modules: [
                ...modules
            ]
        };
    }
    if (updateA.type === "partial" && updateB.type === "deleted") {
        // We could eagerly return `updateB` here, but this would potentially be
        // incorrect if `updateA` has added modules.
        const modules = new Set(updateB.modules ?? []);
        if (updateA.added != null) {
            for (const moduleId of updateA.added){
                modules.delete(moduleId);
            }
        }
        return {
            type: "deleted",
            modules: [
                ...modules
            ]
        };
    }
    // Any other update combination is invalid.
    return undefined;
}
function invariant(_, message) {
    throw new Error(`Invariant: ${message}`);
}
const CRITICAL = [
    "bug",
    "error",
    "fatal"
];
function compareByList(list, a, b) {
    const aI = list.indexOf(a) + 1 || list.length;
    const bI = list.indexOf(b) + 1 || list.length;
    return aI - bI;
}
const chunksWithIssues = new Map();
function emitIssues() {
    const issues = [];
    const deduplicationSet = new Set();
    for (const [_, chunkIssues] of chunksWithIssues){
        for (const chunkIssue of chunkIssues){
            if (deduplicationSet.has(chunkIssue.formatted)) continue;
            issues.push(chunkIssue);
            deduplicationSet.add(chunkIssue.formatted);
        }
    }
    sortIssues(issues);
    hooks.issues(issues);
}
function handleIssues(msg) {
    const key = resourceKey(msg.resource);
    let hasCriticalIssues = false;
    for (const issue of msg.issues){
        if (CRITICAL.includes(issue.severity)) {
            hasCriticalIssues = true;
        }
    }
    if (msg.issues.length > 0) {
        chunksWithIssues.set(key, msg.issues);
    } else if (chunksWithIssues.has(key)) {
        chunksWithIssues.delete(key);
    }
    emitIssues();
    return hasCriticalIssues;
}
const SEVERITY_ORDER = [
    "bug",
    "fatal",
    "error",
    "warning",
    "info",
    "log"
];
const CATEGORY_ORDER = [
    "parse",
    "resolve",
    "code generation",
    "rendering",
    "typescript",
    "other"
];
function sortIssues(issues) {
    issues.sort((a, b)=>{
        const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);
        if (first !== 0) return first;
        return compareByList(CATEGORY_ORDER, a.category, b.category);
    });
}
const hooks = {
    beforeRefresh: ()=>{},
    refresh: ()=>{},
    buildOk: ()=>{},
    issues: (_issues)=>{}
};
function setHooks(newHooks) {
    Object.assign(hooks, newHooks);
}
function handleSocketMessage(msg) {
    sortIssues(msg.issues);
    handleIssues(msg);
    switch(msg.type){
        case "issues":
            break;
        case "partial":
            // aggregate updates
            aggregateUpdates(msg);
            break;
        default:
            // run single update
            const runHooks = chunkListsWithPendingUpdates.size === 0;
            if (runHooks) hooks.beforeRefresh();
            triggerUpdate(msg);
            if (runHooks) finalizeUpdate();
            break;
    }
}
function finalizeUpdate() {
    hooks.refresh();
    hooks.buildOk();
    // This is used by the Next.js integration test suite to notify it when HMR
    // updates have been completed.
    // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)
    if (globalThis.__NEXT_HMR_CB) {
        globalThis.__NEXT_HMR_CB();
        globalThis.__NEXT_HMR_CB = null;
    }
}
function subscribeToChunkUpdate(chunkPath, sendMessage, callback) {
    return subscribeToUpdate({
        path: chunkPath
    }, sendMessage, callback);
}
function subscribeToUpdate(resource, sendMessage, callback) {
    const key = resourceKey(resource);
    let callbackSet;
    const existingCallbackSet = updateCallbackSets.get(key);
    if (!existingCallbackSet) {
        callbackSet = {
            callbacks: new Set([
                callback
            ]),
            unsubscribe: subscribeToUpdates(sendMessage, resource)
        };
        updateCallbackSets.set(key, callbackSet);
    } else {
        existingCallbackSet.callbacks.add(callback);
        callbackSet = existingCallbackSet;
    }
    return ()=>{
        callbackSet.callbacks.delete(callback);
        if (callbackSet.callbacks.size === 0) {
            callbackSet.unsubscribe();
            updateCallbackSets.delete(key);
        }
    };
}
function triggerUpdate(msg) {
    const key = resourceKey(msg.resource);
    const callbackSet = updateCallbackSets.get(key);
    if (!callbackSet) {
        return;
    }
    for (const callback of callbackSet.callbacks){
        callback(msg);
    }
    if (msg.type === "notFound") {
        // This indicates that the resource which we subscribed to either does not exist or
        // has been deleted. In either case, we should clear all update callbacks, so if a
        // new subscription is created for the same resource, it will send a new "subscribe"
        // message to the server.
        // No need to send an "unsubscribe" message to the server, it will have already
        // dropped the update stream before sending the "notFound" message.
        updateCallbackSets.delete(key);
    }
}
}}),
"[project]/src/utils/Constants.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Const": (()=>Const),
    "ProcessAPI": (()=>ProcessAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [client] (ecmascript)");
const Const = {
    Token: "token",
    Session: "Session",
    LoggedInRolePermission: "Role",
    User: "User",
    LoggedIn: "LoggedIn",
    LoggedInUser: "LoggedInUser",
    STrue: true,
    SFalse: false,
    Success200: 200,
    Created201: 201,
    Invalid400: 400,
    UnAuth401: 401,
    Forbidden403: 403,
    NotFound404: 404,
    ServerError500: 500,
    BadGateway502: 502,
    ServiceUnavailable503: 503,
    GatewayTimeout504: 504,
    Redirect302: 302,
    Inactive: 0,
    Active: 1,
    Trash: 2,
    Draft: 3,
    Scheduled: 4,
    Limit: 20,
    Offset: 0,
    Brand: "Robb Report India",
    Link: ("TURBOPACK compile-time value", "https://backend.bms-rpsg-media.com"),
    ClientLink: ("TURBOPACK compile-time value", "https://www.robbreportindia.com")
};
const ProcessAPI = async (res)=>{
    if (res.status === Const.Success200 || res.status === Const.Created201) {
        const data = await res.json();
        return data;
    } else if (res.status === Const.Redirect302) {} else if (res.status === Const.Invalid400) {} else if (res.status === Const.UnAuth401) {
        localStorage.clear();
        window.location.href = "/signin";
    } else if (res.status === Const.NotFound404) {
        const data = await res.json();
        return data;
    // return {
    //   notFound: true,
    // };
    } else {
        throw new Error("Some error occurred");
    }
};
_c = ProcessAPI;
var _c;
__turbopack_context__.k.register(_c, "ProcessAPI");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/Util.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "binaryToNumber": (()=>binaryToNumber),
    "capitalizeFirstLetter": (()=>capitalizeFirstLetter),
    "checkPermission": (()=>checkPermission),
    "convertSlugOrTitle": (()=>convertSlugOrTitle),
    "convertToISTISOString": (()=>convertToISTISOString),
    "dateFormateWithTime": (()=>dateFormateWithTime),
    "dateFormateWithTimeShort": (()=>dateFormateWithTimeShort),
    "escapeXml": (()=>escapeXml),
    "extractTextFromDoc": (()=>extractTextFromDoc),
    "extractTwitterId": (()=>extractTwitterId),
    "formatDateAndTime": (()=>formatDateAndTime),
    "formatDateTimeHv": (()=>formatDateTimeHv),
    "formateDateShort": (()=>formateDateShort),
    "ga4FormatDate": (()=>ga4FormatDate),
    "generateSlug": (()=>generateSlug),
    "getAuthorText": (()=>getAuthorText),
    "getEmbedType": (()=>getEmbedType),
    "getTwitterUrl": (()=>getTwitterUrl),
    "hasHtmlTags": (()=>hasHtmlTags),
    "htmlParser": (()=>htmlParser),
    "isValidColor": (()=>isValidColor),
    "permissionCount": (()=>permissionCount),
    "statusLabel": (()=>statusLabel),
    "timeFormate": (()=>timeFormate)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$react$2d$parser$2f$esm$2f$index$2e$mjs__$5b$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/html-react-parser/esm/index.mjs [client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$react$2d$parser$2f$esm$2f$index$2e$mjs__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/html-react-parser/esm/index.mjs [client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [client] (ecmascript)");
;
;
const htmlParser = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html$2d$react$2d$parser$2f$esm$2f$index$2e$mjs__$5b$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(data);
};
const dateFormateWithTime = (dateString)=>{
    const date = new Date(dateString);
    const monthNames = [
        "JANUARY",
        "FEBRUARY",
        "MARCH",
        "APRIL",
        "MAY",
        "JUNE",
        "JULY",
        "AUGUST",
        "SEPTEMBER",
        "OCTOBER",
        "NOVEMBER",
        "DECEMBER"
    ];
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;
    const minutesStr = minutes.toString().padStart(2, "0");
    return `${month} ${day}, ${year} ${hours}:${minutesStr}${ampm}`;
};
const formateDateShort = (dateString)=>{
    if (!dateString) return;
    const date = new Date(dateString);
    const day = String(date.getUTCDate()).padStart(2, "0");
    const month = String(date.getUTCMonth() + 1).padStart(2, "0");
    const year = String(date.getUTCFullYear()).slice(-2);
    return `${day}.${month}.${year}`;
};
const dateFormateWithTimeShort = (dateString)=>{
    if (!dateString) return;
    const date = new Date(dateString);
    const monthNames = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec"
    ];
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;
    const minutesStr = minutes.toString().padStart(2, "0");
    return `${month} ${day}, ${year}`;
};
const timeFormate = (date)=>{
    const dateObj = new Date(date);
    const options = {
        month: "long",
        day: "2-digit",
        year: "numeric",
        hour: "numeric",
        minute: "2-digit",
        hour12: true
    };
    const formattedDate = new Intl.DateTimeFormat("en-US", options).format(dateObj);
    const getTime = formattedDate.split(" at ")[1];
    return getTime;
};
const formatDateAndTime = (isoString)=>{
    const date = new Date(isoString);
    const now = new Date();
    const months = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec"
    ];
    const formattedDate = `${months[date.getUTCMonth()]} ${date.getUTCDate()}, ${date.getUTCFullYear()}`;
    const diffInMilliseconds = now - date;
    const diffInMinutes = Math.floor(diffInMilliseconds / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays >= 10) {
        return formattedDate;
    } else {
        let timeDifference;
        if (diffInDays === 1) {
            timeDifference = `${diffInDays} day ago`;
        } else if (diffInDays > 1) {
            timeDifference = `${diffInDays} days ago`;
        } else if (diffInHours >= 1) {
            timeDifference = `${diffInHours} hours ago`;
        } else {
            timeDifference = `${diffInMinutes} minutes ago`;
        }
        return timeDifference;
    }
};
const formatDateTimeHv = (inputDateTime)=>{
    const inputDate = new Date(inputDateTime);
    const currentDate = new Date();
    const diffTime = Math.abs(currentDate - inputDate);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const isSameDay = inputDate.getDate() === currentDate.getDate() && inputDate.getMonth() === currentDate.getMonth() && inputDate.getFullYear() === currentDate.getFullYear();
    if (isSameDay) {
        // Ensuring consistent 12-hour format (with AM/PM) across client and server
        return inputDate.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true
        });
    }
    if (diffDays <= 2) {
        return `${diffDays} day${diffDays > 1 ? "s" : ""} ago`;
    }
    // Return in "SEP 19, 2024" format for dates older than 2 days
    const options = {
        year: "numeric",
        month: "short",
        day: "numeric"
    };
    return inputDate.toLocaleDateString("en-US", options);
};
const checkPermission = (value, viewIndex)=>{
    if (value && typeof value == "number" && value > 0) {
        const permissions = Number(value).toString(2).split("").reverse().map((item)=>item === "1");
        Object.keys(viewIndex).forEach(function(key, value) {
            if (permissions.length > value) {
                viewIndex[key] = permissions[value];
            } else {
                viewIndex[key] = false;
            }
        });
        return viewIndex;
    } else {
        return false;
    }
};
const binaryToNumber = (value)=>{
    if (value) {
        const binaryToNumber = parseInt(value, 2);
        return binaryToNumber;
    } else {
        return 0;
    }
};
const permissionCount = (value)=>{
    if (value && typeof value == "number" && value > 0) {
        const permissions = Number(value).toString(2).split("");
        const total = permissions.length;
        const count = permissions.filter((item)=>item === "1").length;
        return {
            count,
            total
        };
    }
    return {
        count: 0,
        total: 0
    };
};
const isValidColor = (input)=>{
    try {
        const namedColors = [
            "black",
            "silver",
            "gray",
            "white",
            "maroon",
            "red",
            "purple",
            "fuchsia",
            "green",
            "lime",
            "olive",
            "yellow",
            "navy",
            "blue",
            "teal",
            "aqua"
        ];
        // Case-insensitive match against the list of named colors
        const colorRegex = new RegExp(`^(${namedColors.join("|")})$`, "i");
        // let regex = new RegExp(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/);
        let regex = new RegExp(/^(#?([a-f\d]{3,4}|[a-f\d]{6}|[a-f\d]{8})|rgb\((0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d)\)|rgba\((0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|0?\.\d|1(\.0)?)\)|hsl\((0|360|35\d|3[0-4]\d|[12]\d\d|0?\d?\d),(0|100|\d{1,2})%,(0|100|\d{1,2})%\)|hsla\((0|360|35\d|3[0-4]\d|[12]\d\d|0?\d?\d),(0|100|\d{1,2})%,(0|100|\d{1,2})%,(0?\.\d|1(\.0)?)\))$/);
        return regex.test(input) || colorRegex.test(input);
    } catch (error) {
        return false;
    }
};
const ga4FormatDate = (dateString)=>{
    // Extract year, month, and day from the string
    const dateStringParam = dateString.split("-");
    const year = dateStringParam[0];
    const month = parseInt(dateStringParam[1]);
    const day = dateStringParam[2];
    // Define an array for month names
    const monthNames = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December"
    ];
    const date = new Date(year, month - 1, day);
    const monthName = monthNames[date.getMonth()];
    const formattedDay = day.padStart(2, "0");
    return monthName + " " + formattedDay;
};
const generateSlug = (title)=>{
    let slug = title.toString() // Convert to string
    .toLowerCase() // Convert to lowercase
    .trim() // Trim leading/trailing whitespace
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/[^\w-]+/g, "") // Remove all non-word characters
    .replace(/--+/g, "-"); // Replace multiple hyphens with a single hyphen
    // Ensure the slug starts with a slash
    if (!slug.startsWith("/")) {
        slug = `/${slug}`;
    }
    return slug;
};
const statusLabel = (value)=>{
    let label = "";
    if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].Inactive) {
        label = "Unpubilled";
    } else if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].Active) {
        label = "Published";
    } else if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].Trash) {
        label = "Trash";
    } else if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].Draft) {
        label = "Draft";
    } else if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].Scheduled) {
        label = "Scheduled";
    }
    return label;
};
const hasHtmlTags = (str)=>{
    const regex = /<\/?[a-z][\s\S]*>/i;
    return regex.test(str);
};
const getEmbedType = (url)=>{
    const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
    const instagramRegex = /(?:https?:\/\/)?(?:www\.)?instagram\.com\/(?:p|tv|reel)\/([A-Za-z0-9_-]+)/;
    const twitterRegex = /(?:https?:\/\/)?(?:(?:www\.|platform\.)?(?:twitter|x)\.com\/(?:(?:\w+\/status\/[0-9]+)|(?:embed\/Tweet\.html\?id=[0-9]+)))/;
    const facebookPostOrVideoRegex = /(?:https?:\/\/)?(?:www\.)?facebook\.com\/(?:[^\/\n\s]+\/posts\/|(?:video\.php\?v=|watch\/))([0-9]+)/;
    if (youtubeRegex.test(url)) {
        return "youtube";
    } else if (instagramRegex.test(url)) {
        return "instagram";
    } else if (twitterRegex.test(url)) {
        return "twitter";
    } else if (facebookPostOrVideoRegex.test(url)) {
        return "facebook";
    }
};
const extractTwitterId = (embedUrl)=>{
    const match = embedUrl.split("id=")[1];
    return match;
};
const getTwitterUrl = (embedUrl)=>{
    const tweetId = new URL(embedUrl).searchParams.get("id");
    const tweetUrl = `https://twitter.com/i/web/status/${tweetId}`;
    return tweetUrl;
};
const getAuthorText = (prefix = "By", author = [], contributor = [])=>{
    const list = author.length ? author : contributor;
    const name = list[0]?.name || list[0] || "";
    const count = list.length - 1;
    return name ? `${prefix} ${name}${count ? ` +${count} More` : ""}` : "";
};
const extractTextFromDoc = (doc)=>{
    const output = [];
    function extractTextFromContent(contentArray) {
        return contentArray?.map((node)=>{
            if (node.type === "text") return node.text || "";
            if (node.content) return extractTextFromContent(node.content);
            return "";
        }).join("") || "";
    }
    if (Array.isArray(doc.content)) {
        for (const node of doc.content){
            if (node.type === "paragraph" || node.type === "heading") {
                const text = extractTextFromContent(node.content || []);
                if (text.trim()) output.push(text.trim());
            }
        }
    }
    return output.join(" ");
};
const convertToISTISOString = (utcISOString)=>{
    if (!utcISOString) return "";
    const date = new Date(utcISOString);
    const istOffsetMs = 5.5 * 60 * 60 * 1000;
    const istDate = new Date(date.getTime() + istOffsetMs);
    return istDate.toISOString().replace("Z", "+05:30");
};
const escapeXml = (unsafe)=>{
    return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
};
const convertSlugOrTitle = (input = "", toTitle = true)=>{
    if (!input) return "";
    const trimmedText = input.trim();
    if (toTitle) {
        return trimmedText.split("-").join(" ");
    } else {
        return trimmedText.toLowerCase().split(" ").filter(Boolean).join("-");
    }
};
const capitalizeFirstLetter = (str)=>{
    if (!str) return '';
    return str.replace(/\b\w/g, (char)=>char.toUpperCase());
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/seo/WebPageSchema.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/jsx-dev-runtime.js [client] (ecmascript)");
;
const WebPageSchema = ({ name, description, url })=>{
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: name,
        description: description,
        speakable: {
            "@type": "SpeakableSpecification",
            xpath: [
                "//title",
                "//meta[@name='description']/@content"
            ]
        },
        url: url
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(schemaData)
        }
    }, void 0, false, {
        fileName: "[project]/src/components/seo/WebPageSchema.jsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
};
_c = WebPageSchema;
const __TURBOPACK__default__export__ = WebPageSchema;
var _c;
__turbopack_context__.k.register(_c, "WebPageSchema");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/seo/NewsMediaOrganizationSchema.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/jsx-dev-runtime.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
;
;
const NewsMediaOrganizationSchema = ({ name, clientLink, logoUrl, address, contact, sameAs })=>{
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "NewsMediaOrganization",
        name: name,
        url: clientLink,
        logo: {
            "@type": "ImageObject",
            url: logoUrl
        },
        address: {
            "@type": "PostalAddress",
            streetAddress: address?.streetAddress,
            addressLocality: address?.addressLocality,
            addressRegion: address?.addressRegion,
            postalCode: address?.postalCode
        },
        contactPoint: {
            "@type": "ContactPoint",
            telephone: contact?.telephone,
            contactType: contact?.contactType,
            areaServed: contact?.areaServed,
            availableLanguage: contact?.availableLanguage,
            hoursAvailable: {
                opens: contact?.hoursAvailable?.opens,
                closes: contact?.hoursAvailable?.closes
            }
        },
        sameAs: sameAs
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(schemaData)
        }
    }, void 0, false, {
        fileName: "[project]/src/components/seo/NewsMediaOrganizationSchema.jsx",
        lineNumber: 41,
        columnNumber: 5
    }, this);
};
_c = NewsMediaOrganizationSchema;
const __TURBOPACK__default__export__ = NewsMediaOrganizationSchema;
var _c;
__turbopack_context__.k.register(_c, "NewsMediaOrganizationSchema");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/helpers/MenuData.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "menus": (()=>menus)
});
const menus = [
    {
        name: "Motoring",
        link: "/motoring",
        submenus: [
            {
                name: "Cars",
                link: "/motoring/cars"
            },
            {
                name: "Bikes",
                link: "/motoring/bikes"
            },
            {
                name: "Vintage & Classics",
                link: "/motoring/vintage-and-classics"
            }
        ]
    },
    {
        name: "Yachting & Aviation",
        link: "/yachting-and-aviation",
        submenus: [
            {
                name: "Yachting",
                link: "/yachting-and-aviation/yachting"
            },
            {
                name: "Aviation",
                link: "/yachting-and-aviation/aviation"
            },
            {
                name: "Cruises & Expeditions",
                link: "/yachting-and-aviation/cruises-and-expeditions"
            }
        ]
    },
    {
        name: "Style",
        link: "/style",
        submenus: [
            {
                name: "Timepieces",
                link: "/style/timepieces"
            },
            {
                name: "Jewellery & Accessories",
                link: "/style/jewellery-and-accessories"
            },
            {
                name: "Fashion & Beauty",
                link: "/style/fashion-and-beauty"
            },
            {
                name: "Bespoke",
                link: "/style/bespoke"
            }
        ]
    },
    {
        name: "Home & Design",
        link: "/home-and-design",
        submenus: [
            {
                name: "Interiors & Architecture",
                link: "/home-and-design/interiors-and-architecture"
            },
            {
                name: "Real Estate",
                link: "/home-and-design/real-estate"
            },
            {
                name: "Art",
                link: "/home-and-design/art"
            }
        ]
    },
    {
        name: "Travel",
        link: "/travel",
        submenus: [
            {
                name: "Wellness & Spas",
                link: "/travel/wellness-and-spas"
            },
            {
                name: "India",
                link: "/travel/india"
            },
            {
                name: "International",
                link: "/travel/international"
            }
        ]
    },
    {
        name: "Food & Drink",
        link: "/food-and-drink",
        submenus: [
            {
                name: "Gastronomy",
                link: "/food-and-drink/gastronomy"
            },
            {
                name: "Spirits",
                link: "/food-and-drink/spirits"
            }
        ]
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/seo/SiteNavigationSchema.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/jsx-dev-runtime.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$MenuData$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/helpers/MenuData.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [client] (ecmascript)");
;
;
;
;
const SiteNavigationSchema = ()=>{
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "SiteNavigationElement",
        name: [],
        url: []
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$MenuData$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["menus"].forEach((menu)=>{
        schemaData.name.push(menu.name);
        schemaData.url.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink}${menu.link}`);
        if (menu && menu.submenus && menu.submenus.length > 0) {
            menu.submenus.forEach((submenu)=>{
                if (submenu.name !== "All") {
                    schemaData.name.push(submenu.name);
                    schemaData.url.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink}${submenu.link}`);
                }
            });
        }
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(schemaData)
        }
    }, void 0, false, {
        fileName: "[project]/src/components/seo/SiteNavigationSchema.jsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
};
_c = SiteNavigationSchema;
const __TURBOPACK__default__export__ = SiteNavigationSchema;
var _c;
__turbopack_context__.k.register(_c, "SiteNavigationSchema");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/seo/SeoHeader.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/jsx-dev-runtime.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/head.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/router.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$WebPageSchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/WebPageSchema.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$NewsMediaOrganizationSchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/NewsMediaOrganizationSchema.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$SiteNavigationSchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/SiteNavigationSchema.jsx [client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
const SeoHeader = ({ meta = {}, type = "website" })=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const defaultImage = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink}/favicon/favicon-192x192.png`;
    const canonical = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink}/${router.asPath?.slice(1)}`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("title", {
                children: meta?.title || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "description",
                content: meta?.description || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 16,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "keywords",
                content: meta?.keywords || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            meta?.author && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "author",
                content: meta?.author || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 18,
                columnNumber: 24
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "publisher",
                content: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].Brand
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "robots",
                content: `${meta?.robots}, max-image-preview:large` || "noindex,nofollow, max-image-preview:large"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 20,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "canonical",
                href: meta?.canonical || canonical
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 27,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "fb:app_id",
                content: "446498535209610"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 29,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:locale",
                content: "en_IN"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 30,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:type",
                content: type
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 31,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:title",
                content: meta?.og?.title || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 32,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:description",
                content: meta?.og?.description || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:url",
                content: canonical
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:site_name",
                content: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].Brand
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:image",
                content: meta?.og?.image || defaultImage
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 36,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:image:width",
                content: "1200"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                property: "og:image:height",
                content: "630"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "twitter:card",
                content: meta?.twitter?.card || "summary_large_image"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 40,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "twitter:title",
                content: meta?.twitter?.title || meta?.title
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 44,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "twitter:description",
                content: meta?.twitter?.description || meta?.description
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 48,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "twitter:site",
                content: "@robbreportindia"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 52,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "twitter:image",
                content: meta?.twitter?.image || defaultImage
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 53,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "twitter:creator",
                content: "@robbreportindia"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 57,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                charSet: "UTF-8"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 58,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                httpEquiv: "Content-Type",
                content: "text/html;charset=UTF-8"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                name: "viewport",
                content: "width=device-width, initial-scale=1.0"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 60,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "icon",
                type: "image/png",
                sizes: "16x16",
                href: "/favicon/favicon-16x16.png"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 61,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "icon",
                type: "image/png",
                sizes: "32x32",
                href: "/favicon/favicon-32x32.png"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "icon",
                type: "image/png",
                sizes: "192x192",
                href: "/favicon/favicon-192x192.png"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 73,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "apple-touch-icon",
                href: "/favicon/apple-touch-icon.png"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 79,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                rel: "alternate",
                hrefLang: "en-in",
                href: meta?.canonical || canonical
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 80,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$WebPageSchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["default"], {
                name: meta?.title || "",
                description: meta?.description || "",
                url: meta?.canonical || canonical
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 85,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$NewsMediaOrganizationSchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["default"], {
                name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].Brand,
                clientLink: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink}/`,
                logoUrl: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink}/RR final logo.png`,
                address: {
                    streetAddress: "RPSG Lifestyle Media, Thapar House, 3rd floor, Janpath Lane",
                    addressLocality: "New Delhi",
                    addressRegion: "India",
                    postalCode: "110 001"
                },
                contact: {
                    telephone: "+91–11–23486700",
                    contactType: "Customer Service",
                    areaServed: "IN",
                    availableLanguage: "English",
                    hoursAvailable: {
                        opens: "09:00",
                        closes: "19:00"
                    }
                },
                sameAs: [
                    "https://www.facebook.com/robbreporterindia",
                    "https://www.instagram.com/robbreporterindia/",
                    "https://twitter.com/robbreportindia",
                    "https://www.youtube.com/@robbreportIndia"
                ]
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 90,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$SiteNavigationSchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 118,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/seo/SeoHeader.jsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
};
_s(SeoHeader, "fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = SeoHeader;
const __TURBOPACK__default__export__ = SeoHeader;
var _c;
__turbopack_context__.k.register(_c, "SeoHeader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/seo/BreadcrumbSchema.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/jsx-dev-runtime.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/head.js [client] (ecmascript)");
;
;
;
const BreadcrumbSchema = ({ itemList })=>{
    const breadcrumb = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                position: 1,
                item: {
                    "@id": __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink,
                    name: "Home"
                }
            }
        ]
    };
    if (itemList && itemList.length > 0) {
        itemList.forEach((item, index)=>{
            breadcrumb.itemListElement.push({
                "@type": "ListItem",
                position: index + 2,
                item: {
                    "@id": __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink + item.slug,
                    name: item.name
                }
            });
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
            type: "application/ld+json",
            dangerouslySetInnerHTML: {
                __html: JSON.stringify(breadcrumb)
            }
        }, void 0, false, {
            fileName: "[project]/src/components/seo/BreadcrumbSchema.jsx",
            lineNumber: 35,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/seo/BreadcrumbSchema.jsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
};
_c = BreadcrumbSchema;
const __TURBOPACK__default__export__ = BreadcrumbSchema;
var _c;
__turbopack_context__.k.register(_c, "BreadcrumbSchema");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/seo/ImageGallerySchema.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/jsx-dev-runtime.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/head.js [client] (ecmascript)");
;
;
const ImageGallerySchema = ({ title, description, url, datePublished, data })=>{
    const images = data?.map((item)=>({
            "@type": "ImageObject",
            url: item?.image || ""
        }));
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "ImageGallery",
        url: url,
        datePublished: datePublished,
        mainEntityOfPage: {
            "@type": "WebPage",
            "@id": url,
            headline: title,
            description: description
        },
        image: images
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
            type: "application/ld+json",
            dangerouslySetInnerHTML: {
                __html: JSON.stringify(schemaData)
            }
        }, void 0, false, {
            fileName: "[project]/src/components/seo/ImageGallerySchema.jsx",
            lineNumber: 30,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/seo/ImageGallerySchema.jsx",
        lineNumber: 29,
        columnNumber: 5
    }, this);
};
_c = ImageGallerySchema;
const __TURBOPACK__default__export__ = ImageGallerySchema;
var _c;
__turbopack_context__.k.register(_c, "ImageGallerySchema");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/seo/MediaGallerySchema.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/jsx-dev-runtime.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/head.js [client] (ecmascript)");
;
;
const MediaGallerySchema = ({ title, description, data })=>{
    const associatedMedia = data?.map((item)=>({
            "@type": "ImageObject",
            name: item?.title || "",
            thumbnailUrl: item?.image || ""
        }));
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "MediaGallery",
        headline: title,
        description: description,
        mainEntityOfPage: {
            "@type": "ImageGallery",
            associatedMedia: associatedMedia
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
            type: "application/ld+json",
            dangerouslySetInnerHTML: {
                __html: JSON.stringify(schemaData)
            }
        }, void 0, false, {
            fileName: "[project]/src/components/seo/MediaGallerySchema.jsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/seo/MediaGallerySchema.jsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
};
_c = MediaGallerySchema;
const __TURBOPACK__default__export__ = MediaGallerySchema;
var _c;
__turbopack_context__.k.register(_c, "MediaGallerySchema");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/amp/ampCss.js [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ampCSS": (()=>ampCSS),
    "ampNavbarCSS": (()=>ampNavbarCSS),
    "webStoryDetailCSS": (()=>webStoryDetailCSS)
});
const ampCSS = `
.story-top {
    padding: 2rem 0 0;
    position: sticky;
    top: 50px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    z-index: 9;
}
.container {
    padding-right: 20px;
    padding-left: 20px;
}
.story-top h1 {
    font-size: 30px;
    line-height: 1;
    margin-bottom: 0;
    font-weight: 400;
    display: block;
    color: #000;
    font-style: normal;
}
`;
const ampNavbarCSS = `
    .nav-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background: white;
      position: relative;
    }
    .logo {
      font-size: 1.5rem;
      font-weight: bold;
      text-decoration: none;
      color: #000;
    }
    .hamburger {
      display: flex;
      flex-direction: column;
      cursor: pointer;
      padding: 0.5rem;
    }
    .hamburger .line {
      width: 25px;
      height: 3px;
      background-color: #000;
      margin: 3px 0;
      transition: 0.3s;
    }
    .hamburger.is-active .line:nth-child(1) {
      transform: rotate(-45deg) translate(-5px, 6px);
    }
    .hamburger.is-active .line:nth-child(2) {
      opacity: 0;
    }
    .hamburger.is-active .line:nth-child(3) {
      transform: rotate(45deg) translate(-5px, -6px);
    }
    .mob-menu {
      background: white;
      padding: 1rem;
      width: 300px;
    }
    .mob-menu ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .mob-menu li {
      margin: 0.5rem 0;
    }
    .mob-menu a {
      text-decoration: none;
      color: #000;
      font-size: 1rem;
    }
`;
const webStoryDetailCSS = `
				/* AMP Web Stories Font Configuration */
				@font-face {
					font-family: "NeueHaasDisplayBold";
					src: url(/Assets/NeueHaasDisplayBold.ttf);
					font-display: swap;
				}
				@font-face {
					font-family: "Bitter";
					src: url(/Assets/Bitter-VariableFont_wght.ttf);
					font-display: swap;
				}
				
				.brand-logo {
					position: absolute;
					top: 15px;
					left: 15px;
					color: white;
					text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
					z-index: 10;
				}

				.back-button {
					position: absolute;
					top: 20px;
					right: 20px;
					z-index: 10;
				}

				.back-link {
					color: white;
					text-decoration: none;
					background: rgba(0, 0, 0, 0.5);
					padding: 8px 12px;
					border-radius: 20px;
					font-size: 14px;
					text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
					transition: background 0.3s ease;
					border: none;
					cursor: pointer;
					font-family: "Bitter", serif;
				}
        .next-story-preview{
          padding-bottom: 4rem;
        }
        .next-story-preview h2{
          text-align: center;
          font-size: 24px;
          letter-spacing: 1px;
					font-family: "NeueHaasDisplayBold", sans-serif;
					font-weight: 700;
        }
				.back-link:hover {
					background: rgba(0, 0, 0, 0.7);
				}
				.story-content {
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					padding: 15px;
					background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.8) 100%);
				}
				.story-text {
					color: white;
					text-align: left;
				}
				/* Headlines use primary font (NeueHaasDisplayBold) */
				.story-text h1 {
					font-size: 24px;
					font-family: "NeueHaasDisplayBold", sans-serif;
					margin-bottom: 10px;
					font-weight: 700;
          letter-spacing: 1px;
					line-height: 1.1;
				}
				/* Body text uses secondary font (Bitter) */
				.story-text div {
					font-family: "Bitter", serif;
					font-size: 16px;
					line-height: 1.3;
					font-weight: 400;
				}
				.story-text p {
					font-family: "Bitter", serif;
					font-size: 16px;
					line-height: 1.3;
					font-weight: 400;
				}
        .story-text p a{
        color: #fff;
        }
				.story-text small {
					display: block;
					margin-top: 8px;
					opacity: 0.8;
					font-family: "Bitter", serif;
					font-size: 12px;
					font-weight: 300;
				}
                [template=vertical]{
                align-content: end;
                }

        .next-story-preview {
          color: #fff
        }
				/* Force show AMP story navigation buttons on mobile */
				:global(.amphtml-story-button-container) {
					display: block;
					visibility: visible;
					opacity: 1;
				}

				:global(.amphtml-story-button-move) {
					display: block;
					visibility: visible;
					opacity: 1;
				}

				@media (max-width: 768px) {
					:global(.amphtml-story-button-container) {
						display: block;
						visibility: visible;
						opacity: 1;
					}
				}
			
			`;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/pages/webstories/[category]/[slug]/index.jsx [client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "__N_SSP": (()=>__N_SSP),
    "config": (()=>config),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/jsx-dev-runtime.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react/index.js [client] (ecmascript)");
// import { getWebStories } from "@/pages/api/WebStoriesApi"; // TODO: Uncomment when API is ready
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/head.js [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Util.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$SeoHeader$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/SeoHeader.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$BreadcrumbSchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/BreadcrumbSchema.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$ImageGallerySchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/ImageGallerySchema.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$MediaGallerySchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/MediaGallerySchema.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/amp/ampCss.js [client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
var __N_SSP = true;
const config = {
    amp: true
};
const WebStoryDetail = ({ data, nextData, breadcrumbs, meta, pathname })=>{
    // AMP-compliant CSS without i-amphtml- prefixes
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"], {
                id: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash,
                children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"]
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$head$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "amp-to-amp-navigation",
                        content: "AMP-Redirect",
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 271,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                        async: true,
                        "custom-element": "amp-story",
                        src: "https://cdn.ampproject.org/v0/amp-story-1.0.js",
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 272,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                        async: true,
                        "custom-element": "amp-story-auto-ads",
                        src: "https://cdn.ampproject.org/v0/amp-story-auto-ads-0.1.js",
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 277,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$SeoHeader$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["default"], {
                        meta: meta,
                        pathname: pathname
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 287,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$BreadcrumbSchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["default"], {
                        itemList: breadcrumbs
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 288,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$ImageGallerySchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["default"], {
                        title: data?.slides?.[0]?.title || "",
                        description: data?.slides?.[0]?.description || "",
                        url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["Const"].ClientLink + pathname,
                        datePublished: data?.timestamp || "",
                        data: data?.slides || []
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 289,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$MediaGallerySchema$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["default"], {
                        title: data?.slides?.[0]?.title || "",
                        description: data?.slides?.[0]?.description || "",
                        data: data?.slides || []
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 296,
                        columnNumber: 5
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                lineNumber: 270,
                columnNumber: 4
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story", {
                standalone: "",
                title: data?.title || "",
                publisher: "Robb Report India",
                "publisher-logo-src": "/RR final logo.png",
                "poster-portrait-src": data?.coverImg || "",
                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                children: [
                    data?.slides?.map((slide, index)=>{
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$index$2e$js__$5b$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-page", {
                                id: `page-${index}`,
                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-grid-layer", {
                                        template: "fill",
                                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-img", {
                                            src: slide?.image || "",
                                            width: "720",
                                            height: "1280",
                                            layout: "fill",
                                            alt: slide?.altName || "",
                                            className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                                        }, void 0, false, {
                                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                            lineNumber: 315,
                                            columnNumber: 10
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                        lineNumber: 314,
                                        columnNumber: 9
                                    }, this),
                                    index === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-grid-layer", {
                                        template: "vertical",
                                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "brand-logo",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-img", {
                                                src: "/RR final logo.png",
                                                width: "200",
                                                height: "80",
                                                layout: "fixed",
                                                alt: "Robb Report India Logo",
                                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                                            }, void 0, false, {
                                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                lineNumber: 327,
                                                columnNumber: 12
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                            lineNumber: 326,
                                            columnNumber: 11
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                        lineNumber: 325,
                                        columnNumber: 10
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-grid-layer", {
                                        template: "vertical",
                                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "story-content",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "story-text",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                                    children: slide?.title || ""
                                                }, void 0, false, {
                                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                    lineNumber: 340,
                                                    columnNumber: 11
                                                }, this),
                                                slide?.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    dangerouslySetInnerHTML: {
                                                        __html: slide.description
                                                    },
                                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                                                }, void 0, false, {
                                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                    lineNumber: 342,
                                                    columnNumber: 12
                                                }, this),
                                                slide?.contributor?.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("small", {
                                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                                    children: [
                                                        "Photo Credit: ",
                                                        slide.contributor.join(", ")
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                    lineNumber: 345,
                                                    columnNumber: 12
                                                }, this),
                                                index === 0 && slide?.timestamp && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("small", {
                                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                                    children: [
                                                        "Published: ",
                                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$client$5d$__$28$ecmascript$29$__["dateFormateWithTimeShort"])(slide.timestamp)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                    lineNumber: 348,
                                                    columnNumber: 12
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                            lineNumber: 339,
                                            columnNumber: 10
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                        lineNumber: 338,
                                        columnNumber: 9
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                lineNumber: 313,
                                columnNumber: 8
                            }, this)
                        }, index, false, {
                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                            lineNumber: 312,
                            columnNumber: 7
                        }, this);
                    }),
                    nextData?.slug && nextData?.coverImg && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-page", {
                        id: "next-story-preview",
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-grid-layer", {
                                template: "fill",
                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-img", {
                                    src: nextData.coverImg,
                                    width: "720",
                                    height: "1280",
                                    layout: "fill",
                                    alt: nextData.altName || "Next Story",
                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                                }, void 0, false, {
                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                    lineNumber: 361,
                                    columnNumber: 8
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                lineNumber: 360,
                                columnNumber: 7
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-grid-layer", {
                                template: "vertical",
                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "next-story-overlay story-content",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "next-story-preview",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                        children: nextData.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                        lineNumber: 373,
                                        columnNumber: 9
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                    lineNumber: 371,
                                    columnNumber: 8
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                lineNumber: 370,
                                columnNumber: 7
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-cta-layer", {
                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                    href: nextData.slug,
                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "next-story-btn",
                                    children: "Read Next Story"
                                }, void 0, false, {
                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                    lineNumber: 378,
                                    columnNumber: 8
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                lineNumber: 377,
                                columnNumber: 7
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 359,
                        columnNumber: 6
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("amp-story-auto-ads", {
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$client$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                            type: "application/json",
                            dangerouslySetInnerHTML: {
                                __html: JSON.stringify({
                                    "ad-attributes": {
                                        type: "doubleclick",
                                        "data-slot": "/23290324739/RobbReport-AMP-Stories"
                                    }
                                })
                            },
                            className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$client$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                        }, void 0, false, {
                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                            lineNumber: 386,
                            columnNumber: 6
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 385,
                        columnNumber: 5
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                lineNumber: 303,
                columnNumber: 4
            }, this)
        ]
    }, void 0, true);
};
_c = WebStoryDetail;
const __TURBOPACK__default__export__ = WebStoryDetail;
WebStoryDetail.config = {
    amp: true
};
var _c;
__turbopack_context__.k.register(_c, "WebStoryDetail");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[next]/entry/page-loader.ts { PAGE => \"[project]/src/pages/webstories/[category]/[slug]/index.jsx [client] (ecmascript)\" } [client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const PAGE_PATH = "/webstories/[category]/[slug]";
(window.__NEXT_P = window.__NEXT_P || []).push([
    PAGE_PATH,
    ()=>{
        return __turbopack_context__.r("[project]/src/pages/webstories/[category]/[slug]/index.jsx [client] (ecmascript)");
    }
]);
// @ts-expect-error module.hot exists
if (module.hot) {
    // @ts-expect-error module.hot exists
    module.hot.dispose(function() {
        window.__NEXT_P.push([
            PAGE_PATH
        ]);
    });
}
}}),
"[project]/src/pages/webstories/[category]/[slug]/index.jsx (hmr-entry)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, m: module } = __turbopack_context__;
{
__turbopack_context__.r("[next]/entry/page-loader.ts { PAGE => \"[project]/src/pages/webstories/[category]/[slug]/index.jsx [client] (ecmascript)\" } [client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__2dfa5594._.js.map