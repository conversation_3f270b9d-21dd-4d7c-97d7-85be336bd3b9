{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/homeHero.css"], "sourcesContent": [".specials-section {\r\n  z-index: 3;\r\n  background-color: var(--black);\r\n  padding-top: 0.625rem;\r\n  padding-bottom: 6.5625rem;\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n.specials-slider-wrap {\r\n  position: relative;\r\n  /* margin-top: 3.75rem; */\r\n}\r\n.swiper-horizontal {\r\n  touch-action: pan-y;\r\n}\r\n\r\n.swiper-wrapper.specials_swiper_wrapper {\r\n  z-index: -1;\r\n  margin-left: 2.25rem;\r\n  display: flex;\r\n}\r\n.swiper-slide.specials_swiper_slide {\r\n  z-index: -1;\r\n  cursor: grabbing;\r\n  flex: none;\r\n  width: 83rem;\r\n  /* height: 45.875rem; */\r\n  /* height: 100vh; */\r\n  aspect-ratio: 4/2;\r\n}\r\n.swiper-backface-hidden .swiper-slide {\r\n  transform: translateZ(0);\r\n  -webkit-backface-visibility: hidden;\r\n  backface-visibility: hidden;\r\n}\r\n.special-stories-slider-block {\r\n  z-index: 11;\r\n  color: var(--black);\r\n  /* background-image: url(https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg); */\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  background-size: cover;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n  margin: 0 auto;\r\n}\r\n.special-stories-slider-block img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n.film-overlay-wrap {\r\n  background-image: linear-gradient(\r\n    221.52deg,\r\n    #0e101033 33.53%,\r\n    #0e1010cc 63.89%,\r\n    #0e1010fa 85.9%\r\n  );\r\n  justify-content: flex-start;\r\n  align-items: flex-end;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: absolute;\r\n  z-index: 99;\r\n  top: 0;\r\n  padding-left: 2.5rem;\r\n  padding: 0 5rem;\r\n  padding-bottom: 4.375rem;\r\n  padding-right: 2.5rem;\r\n  display: flex;\r\n}\r\n.heroSliderContentWrapper {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  display: flex;\r\n}\r\n.video-stories-slider-inner-block {\r\n  z-index: 1;\r\n  grid-column-gap: 1.375rem;\r\n  grid-row-gap: 1.375rem;\r\n  border-radius: 0.3125rem;\r\n  flex-flow: column;\r\n  justify-content: flex-end;\r\n  align-items: flex-start;\r\n  /* width: 100%; */\r\n  height: 100%;\r\n  max-width: 35.5rem;\r\n  display: flex;\r\n  position: relative;\r\n  /* padding-bottom: 4.375rem; */\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n\r\n.heading-h2 {\r\n  font-size: var(--desktop-font-heading--d-h2);\r\n  letter-spacing: -0.16px;\r\n  margin-top: 0;\r\n  margin-bottom: 0;\r\n  font-weight: 400;\r\n  line-height: 120%;\r\n  font-family: rocky, sans-serif;\r\n}\r\n.special-stories-summary {\r\n  color: var(--dark-gray);\r\n  font-size: var(--mobile-font-size--subtitle-15);\r\n  letter-spacing: 0.32px;\r\n  width: 100%;\r\n  max-width: 31rem;\r\n  line-height: 1.40625rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.text-color-white {\r\n  color: var(--white);\r\n}\r\n.special-stories-date-block {\r\n  grid-column-gap: 0.75rem;\r\n  grid-row-gap: 0.75rem;\r\n  color: var(--red);\r\n  /*  */\r\n  font-size: var(--desktop-paragraph-font--small-p);\r\n  letter-spacing: 0.16px;\r\n  text-transform: uppercase;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  display: flex;\r\n}\r\n.special-stories-date-block.color-white,\r\n.special-stories-title.letter-animation.color-white {\r\n  color: var(--white);\r\n}\r\n.swiper-arrow-wrap {\r\n  z-index: 5;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  max-width: 95%;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  display: flex;\r\n  position: absolute;\r\n  inset: 45% 0% auto;\r\n  transform: translateY(-50%);\r\n}\r\n.play-films-html {\r\n  color: var(--white);\r\n  justify-content: center;\r\n  align-items: center;\r\n  display: flex;\r\n}\r\n.w-embed:before,\r\n.w-embed:after {\r\n  content: \" \";\r\n  grid-area: 1 / 1 / 2 / 2;\r\n  display: table;\r\n}\r\n.swiper-button-prev:after,\r\n.swiper-rtl .swiper-button-next:after,\r\n.swiper-button-next:after,\r\n.swiper-rtl .swiper-button-prev:after {\r\n  content: \"\" !important;\r\n}\r\n\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  position: absolute;\r\n  top: var(--swiper-navigation-top-offset, 50%);\r\n  width: calc(var(--swiper-navigation-size) / 44 * 27);\r\n  height: var(--swiper-navigation-size);\r\n  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));\r\n  z-index: 10;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: var(--swiper-navigation-color, var(--swiper-theme-color));\r\n}\r\n\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  position: static !important;\r\n}\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  color: #fff;\r\n}\r\n.swiper-button-prev,\r\n.swiper-button-next {\r\n  border: 1px solid var(--white);\r\n  color: var(--white);\r\n  cursor: pointer;\r\n  border-radius: 2.5rem;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 3.125rem;\r\n  min-width: 3.125rem;\r\n  height: 3.125rem;\r\n  min-height: 3.125rem;\r\n  transition: all 0.3s ease-in-out;\r\n  display: flex;\r\n}\r\n/* .swiper-button-prev, */\r\n.swiper-button-prev:hover {\r\n  background-color: #fff;\r\n}\r\n.swiper-button-next:hover {\r\n  background-color: #fff;\r\n}\r\n.swiper-button-next:hover svg:not(.swiper- button-prev) {\r\n  fill: var(--black);\r\n}\r\n.swiper-button-prev:hover svg:not(.swiper- button-prev) {\r\n  fill: var(--black);\r\n}\r\n.controls-wrapper {\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  max-width: 80%;\r\n  margin-top: 2.25rem;\r\n  padding-left: 2.125rem;\r\n  display: flex;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.next-up-text {\r\n  color: var(--white);\r\n\r\n  font-size: var(--desktop-font-size-normal--tag-14);\r\n  letter-spacing: 0.16px;\r\n  text-transform: uppercase;\r\n  flex: none;\r\n  position: relative;\r\n}\r\n\r\n.controls-row {\r\n  grid-column-gap: 1rem;\r\n  grid-row-gap: 1rem;\r\n  flex-flow: column;\r\n  flex: none;\r\n  width: 100%;\r\n  max-width: 15rem;\r\n  display: flex;\r\n  position: absolute;\r\n  inset: auto 10.0625rem 0% auto;\r\n}\r\n\r\n.bullet-pagination {\r\n  color: var(--white);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n.bullet-pagination .bullet {\r\n  font-size: 15px;\r\n  width: 100%;\r\n  display: flex;\r\n}\r\n.swiper-paginationn {\r\n  position: relative !important;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n.customscrollbar {\r\n  background-color: #fff3;\r\n  width: 100%;\r\n  height: 3px;\r\n  position: relative;\r\n}\r\n.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {\r\n  background-color: #fff !important;\r\n}\r\n/* .scrollfill {\r\n  width: 0%;\r\n  height: 100%;\r\n  transition: opacity 0.2scubic-bezier (0.257, 0.235, 0.632, 0.621);\r\n  position: absolute;\r\n  inset: 0% auto auto 0%;\r\n} */\r\n@media screen and (min-width: 1920px) {\r\n  .swiper-slide.specials_swiper_slide {\r\n    width: 96%;\r\n  }\r\n  .specials-section {\r\n  }\r\n}\r\n@media screen and (max-width: 991px) {\r\n  .specials-section {\r\n    padding-top: 0.3125rem;\r\n    padding-bottom: 7rem;\r\n  }\r\n  .swiper-slide.specials_swiper_slide {\r\n    width: 100%;\r\n    /* height: 45.9375rem; */\r\n  }\r\n  .swiper-wrapper.specials_swiper_wrapper {\r\n    margin-left: auto;\r\n  }\r\n  .special-stories-summary {\r\n    max-width: 33rem;\r\n  }\r\n  .swiper-arrow-wrap {\r\n    grid-column-gap: 0.8125rem;\r\n    grid-row-gap: 0.8125rem;\r\n    justify-content: flex-end;\r\n    align-items: center;\r\n    display: flex;\r\n    inset: auto 2% -3% auto;\r\n  }\r\n  .controls-wrapper {\r\n    margin-top: 2.875rem;\r\n  }\r\n\r\n  .controls-row {\r\n    max-width: 13.25rem;\r\n    position: absolute;\r\n    inset: auto 17% 1% auto;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .specials-section {\r\n    padding-bottom: 6.25rem;\r\n  }\r\n  .swiper-slide.specials_swiper_slide {\r\n    width: 100%;\r\n    /* height: 40rem; */\r\n  }\r\n  .special-stories-slider-block {\r\n    justify-content: flex-start;\r\n    align-items: flex-end;\r\n  }\r\n  .special-stories-slider-block img {\r\n    /* height: auto; */\r\n  }\r\n  .film-overlay-wrap {\r\n    /* position: relative;\r\n    z-index: 9; */\r\n    padding-left: 1rem;\r\n    padding-right: 1rem;\r\n    padding-bottom: 0;\r\n    /* height: 40%; */\r\n  }\r\n  .remove-margin-bottom {\r\n    width: 100%;\r\n  }\r\n  .controls-wrapper {\r\n    margin-top: 6.25rem;\r\n    padding-left: 1.25rem;\r\n  }\r\n\r\n  .controls-row {\r\n    inset: auto auto 10% 3%;\r\n  }\r\n  .swiper-arrow-wrap {\r\n    max-width: 100%;\r\n    bottom: 3%;\r\n    right: 5%;\r\n  }\r\n  .swiper-button-prev,\r\n  .swiper-button-next {\r\n    width: 2.8rem !important;\r\n    min-width: 2.8rem !important;\r\n    height: 2.8rem !important;\r\n    min-height: 2.8rem !important;\r\n    /* border-radius: 1.5rem; */\r\n  }\r\n}\r\n@media screen and (max-width: 600px) {\r\n  .specials-section {\r\n    background-color: var(--black-dark);\r\n  }\r\n  .swiper-slide.specials_swiper_slide {\r\n    height: auto;\r\n  }\r\n  .special-stories-slider-block img {\r\n    /* height: auto; */\r\n  }\r\n\r\n  .film-overlay-wrap {\r\n    padding-left: 1rem;\r\n    padding-right: 1rem;\r\n    position: relative;\r\n  }\r\n  .video-stories-slider-inner-block {\r\n    max-width: 100%;\r\n    grid-column-gap: 0.7rem;\r\n    grid-row-gap: 0.7rem;\r\n  }\r\n  .special-stories-date-block {\r\n    grid-column-gap: 0.7rem;\r\n    grid-row-gap: 0.7rem;\r\n    font-size: var(--desktop-font-size-normal--tag-14);\r\n  }\r\n  .heading-h2 {\r\n    font-size: var(--mobile-font-heading--m-h2);\r\n  }\r\n  .controls-wrapper {\r\n    max-width: 100%;\r\n  }\r\n\r\n  .controls-row {\r\n    max-width: 11rem;\r\n    left: 5%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;AAKA;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAKA;;;;;;;;;;;;;;;;AAoBA;;;;;;;AAMA;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;;;;;;;;AAaA;;;;;;;AAMA;;;;;;AAMA;;;;AAOA;;;;;;;;;;;;;;AAeA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;AAgBA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;;;AAMA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;;;;AAUA;EACE;;;;EAGA;;;;AAGF;EACE;;;;;EAIA;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;EAIA;;;;;;;AAMF;EACE;;;;EAGA;;;;EAIA;;;;;EAIA;;;EAGA;;;;;;EAQA;;;;EAGA;;;;;EAKA;;;;EAGA;;;;;;EAKA;;;;;;;;AASF;EACE;;;;EAGA;;;;EAGA;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAIA"}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}