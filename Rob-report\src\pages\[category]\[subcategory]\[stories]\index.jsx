import React, { useState } from "react";
import { usePathname } from "next/navigation";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import NewsArticleSchema from "@/components/seo/NewsArticleSchema";
import Hero from "@/components/stories/Hero";
import MainContent from "@/components/stories/MainContent";
import SideContent from "@/components/stories/SideContent";
import Social from "@/components/stories/Social";
import RelatedStories from "@/components/stories/RelatedStories";
import ShareModal from "@/components/stories/ShareModal";
import {
	getStories,
	getLatestStories,
	getStoriesAuthor,
	getYouMayAlsoLike,
} from "@/pages/api/ArticleApi";

const Stories = ({ meta, data, breadcrumbs, author, tag, latest, related }) => {
	const pathname = usePathname();
	const [show, setShow] = useState(false);
	const content = data && data.content ? JSON.parse(data.content) : {};

	return (
		<div id="story_wrapper">
			<SeoHeader meta={meta} type="article" />
			<BreadcrumbSchema itemList={breadcrumbs} />
			<NewsArticleSchema
				headline={meta?.title || ""}
				datePublished={data?.timestamp || ""}
				dateModified={data?.updatedAt || ""}
				articleSection={breadcrumbs?.[0]?.name || ""}
				keywords={meta?.keywords || []}
				description={meta?.description || ""}
				url={pathname}
				content={content || {}}
				author={author?.[0] || {}}
				image={data?.coverImg || ""}
			/>
			<Hero
				title={data?.title || ""}
				description={data?.excerpt || ""}
				image={data?.coverImg || ""}
				altName={data?.altName || ""}
				caption={data?.caption || ""}
				courtesy={data?.courtesy || ""}
				author={author}
				category={breadcrumbs[breadcrumbs?.length - 1 ?? 1]}
				timestamp={data?.timestamp || ""}
				setShow={setShow}
			/>
			<div className="containerWrapper mob-py-0 pb-0">
				<div className="d-flex">
					<MainContent data={content || {}} />
					<SideContent data={latest} />
					<Social tag={tag} author={author || []} />
				</div>
				{/* <Social tag={tag} author={author || []} /> */}
				{/* <div className="container_fullImage">
          <div className="full-image-wrapper">
            <Image
              width={1000}
              height={1000}
              src={data?.coverImg || ""}
              loading="lazy"
              altName={data?.altName || ""}
            />
          </div>
        </div> */}
			</div>
			<RelatedStories data={related} />
			<ShareModal show={show} setShow={setShow} />
		</div>
	);
};

export default Stories;

export async function getServerSideProps({ params, res }) {
	const { category, subcategory, stories } = params;
	const url = `/${category}/${subcategory}/${stories}`;
	const LIMIT = 5;
	const storiesUrl = `/${stories}`;
	const latestStoriesPayload = {
		slug: url,
		limit: LIMIT,
	};
	try {
		const [storiesRes, authorRes, latestStoriesRes, youMayLikeRes] = await Promise.all([
			getStories(url),
			getStoriesAuthor(storiesUrl),
			getLatestStories(latestStoriesPayload),
			getYouMayAlsoLike(storiesUrl),
		]);

		if (storiesRes?.data?.isURLCorrect === false) {
			res.writeHead(301, { Location: storiesRes?.data?.correctUrl });
			res.end();
		}
		if (!storiesRes || Object.keys(storiesRes.data.articleData).length === 0) {
			return {
				notFound: true,
			};
		}

		return {
			props: {
				data: storiesRes?.data?.articleData?.data ?? {},
				breadcrumbs: storiesRes?.data?.articleData?.breadcrumbs ?? [],
				author: authorRes?.data && authorRes?.data?.length > 0 ? authorRes?.data : [],
				tag: storiesRes?.data?.articleData?.tag ?? [],
				latest: latestStoriesRes?.data ?? [],
				related: youMayLikeRes?.data?.slice(0, 6) ?? [],
				meta: storiesRes?.data?.articleData?.meta ?? {},
			},
		};
	} catch (error) {
		console.error("Error fetching data:", error.message);
		return {
			notFound: true,
		};
	}
}
