# AMP Web Stories Implementation

## 🚀 Overview

This implementation adds AMP Web Stories functionality to the Rob-report project, following the same pattern as the Manifest project. The web stories are accessible at `/webstories/[category]/[slug]` routes.

## 📁 File Structure

```
src/
├── pages/
│   ├── webstories/
│   │   ├── index.jsx                    # Web stories listing page
│   │   ├── [category]/
│   │   │   ├── index.jsx               # Category listing page
│   │   │   └── [slug]/
│   │   │       └── index.jsx           # Individual web story (AMP)
│   └── api/
│       └── WebStoriesApi.jsx           # API functions for web stories
├── components/
│   ├── amp/
│   │   └── ampCss.js                   # AMP CSS styles
│   └── seo/
│       ├── ImageGallerySchema.jsx      # SEO schema for image galleries
│       └── MediaGallerySchema.jsx      # SEO schema for media galleries
```

## 🎯 Available Routes

### Demo URLs (with dummy data):
- `/webstories` - Main web stories listing
- `/webstories/all` - All web stories in "all" category
- `/webstories/all/luxury-cars-2024` - Individual web story
- `/webstories/all/luxury-watches-2024` - Individual web story
- `/webstories/all/luxury-yachts-2024` - Individual web story
- `/webstories/all/luxury-real-estate-2024` - Individual web story
- `/webstories/all/luxury-jets-2024` - Individual web story

## 📊 Dummy Data

Currently includes 5 web stories with dummy data:

1. **luxury-cars-2024** - Top 5 Luxury Cars (5 slides)
2. **luxury-watches-2024** - Exquisite Timepieces (5 slides)
3. **luxury-yachts-2024** - Magnificent Yachts (5 slides)
4. **luxury-real-estate-2024** - Extraordinary Properties (5 slides)
5. **luxury-jets-2024** - Private Jets (3 slides)

Each story includes:
- Cover slide with brand logo
- Multiple content slides with images and descriptions
- Photo credits and publication dates
- Next story preview

## 🔧 Implementation Details

### AMP Configuration
- Full AMP compliance with `export const config = { amp: true }`
- Required AMP scripts: `amp-story-1.0.js`, `amp-story-auto-ads-0.1.js`
- Custom CSS following AMP guidelines

### SEO Optimization
- Complete structured data with ImageGallery and MediaGallery schemas
- Proper meta tags and Open Graph data
- Breadcrumb navigation schema

### Ad Integration
- DoubleClick ad integration with Rob-report specific slot: `/23290324739/RobbReport-AMP-Stories`
- Automatic ad insertion between story slides

### Branding
- Publisher: "Robb Report India"
- Logo: "/RR final logo.png"
- Custom fonts and styling

## 🚧 TODO: API Integration

### Current State (Dummy Data)
The implementation currently uses dummy data for testing. All API calls are commented out with proper instructions.

### When API is Ready:

1. **Uncomment API calls** in the following files:
   ```javascript
   // In /webstories/[category]/[slug]/index.jsx
   const storiesRes = await getWebStories(url);
   
   // In /webstories/[category]/index.jsx
   const storiesRes = await getWebStoriesCategory({...});
   
   // In /webstories/index.jsx
   const storiesRes = await getWebStoriesCategory({...});
   ```

2. **Remove dummy data** constants:
   ```javascript
   // Remove DUMMY_WEB_STORIES object
   // Remove dummy data implementation in getServerSideProps
   ```

3. **Update API endpoints** in `WebStoriesApi.jsx` if needed

### API Expected Response Format:
```javascript
// getWebStories(slug) should return:
{
  data: {
    current: {
      data: {
        title: "Story Title",
        coverImg: "image-url",
        altName: "Alt text",
        timestamp: "2024-01-15T10:00:00Z",
        slides: [
          {
            title: "Slide Title",
            description: "<p>HTML content</p>",
            image: "slide-image-url",
            altName: "Slide alt text",
            contributor: ["Credit Name"],
            timestamp: "2024-01-15T10:00:00Z"
          }
        ]
      },
      breadcrumbs: [...],
      tag: [...],
      meta: {...}
    },
    next: {
      title: "Next Story",
      slug: "/next-story-url",
      coverImg: "next-image-url",
      altName: "Next alt text"
    },
    previous: {...}
  }
}
```

## 🧪 Testing

### Local Testing:
1. Start development server: `npm run dev`
2. Visit: `http://localhost:3000/webstories/all/luxury-cars-2024`
3. Add `#development=1` to URL for AMP validation messages

### AMP Validation:
- Use AMP Validator: https://validator.ampproject.org/
- Check browser console for AMP validation errors
- Test on mobile devices for proper touch navigation

### Ad Testing:
- Ensure DoubleClick ad slot is configured in your ad network
- Test ad loading and placement between slides

## 🎨 Customization

### Styling:
- Modify `webStoryDetailCSS` in `/components/amp/ampCss.js`
- Update brand colors, fonts, and layouts

### Content:
- Add more dummy stories to `DUMMY_WEB_STORIES` object
- Customize slide templates and layouts

### Analytics:
- Uncomment Google Analytics code in web story component
- Update GA tracking ID

## 📱 Features

- ✅ Full AMP compliance
- ✅ Touch navigation and gestures
- ✅ Automatic ad insertion
- ✅ SEO optimization with structured data
- ✅ Next story preview and navigation
- ✅ Responsive design
- ✅ Brand logo and styling
- ✅ Photo credits and timestamps
- ✅ Social sharing ready
- ✅ Analytics ready (commented)

## 🔗 Related Files

- Original stories route: `/pages/[category]/[subcategory]/[stories]/index.jsx` (restored to original)
- API utilities: `/utils/Constants.jsx`, `/utils/Util.jsx`
- SEO components: `/components/seo/`

---

**Note**: This implementation is ready for production once the API integration is completed. Simply uncomment the API calls and remove the dummy data as instructed above.
