{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/swiperCardSection.css"], "sourcesContent": [".swipperCardsectionlatest {\r\n  position: relative;\r\n  padding: 5rem 0 0 0;\r\n}\r\n@media screen and (width <= 767px) {\r\n  .swipperCardsectionlatest {\r\n    padding: 2rem 0 0 0;\r\n  }\r\n}\r\n.latestCardPostHeading {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n.editor-picks__secondary-inner-wrapper {\r\n  /* padding: 1.25rem 0; */\r\n  /* margin-bottom: 1.9375rem; */\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.editor-picks__secondary-pick {\r\n  text-align: center;\r\n  /* margin-bottom: 1.25rem; */\r\n  margin-top: 1.5625rem;\r\n  position: relative;\r\n  /* margin-right: 0; */\r\n}\r\n.editor-picks__secondary-pick .featured-image {\r\n  overflow: hidden;\r\n  max-height: 260px !important;\r\n}\r\n.editor-picks__secondary-pick .featured-image img {\r\n  aspect-ratio: 16/9;\r\n}\r\n.editor-picks__secondary-pick .entry__category {\r\n  font-size: 11px;\r\n  line-height: 15px;\r\n  /* letter-spacing: 0.35px; */\r\n}\r\n.sectioner--editor-picks .entry {\r\n  color: #000;\r\n}\r\n.editor-picks__secondary-pick .entry__heading {\r\n  font-size: 23px;\r\n  line-height: 29px;\r\n}\r\n.entry__heading {\r\n  transition: opacity 0.3s ease-in;\r\n}\r\n.arrows_arrow {\r\n  position: relative;\r\n  z-index: 0;\r\n  display: inline-flex;\r\n  width: 45px;\r\n  height: 45px;\r\n  flex-shrink: 0;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 50%;\r\n  text-overflow: ellipsis;\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  user-select: none;\r\n  border: 1px solid #737373;\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n  color: #737373;\r\n  background-color: #0000;\r\n  /* transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1); */\r\n}\r\n.arrows_arrow svg {\r\n  position: relative;\r\n  z-index: 1;\r\n  opacity: 1 !important;\r\n  font-size: 16px;\r\n  transition: color 0.3s ease;\r\n}\r\n.arrows_arrow:hover {\r\n  border: 2px solid #000000;\r\n}\r\n.arrows_arrow:hover .arrows_arrow svg {\r\n  color: #000000;\r\n  font-weight: 900;\r\n}\r\n@media only screen and (width >= 70.625rem) {\r\n  .editor-picks__secondary-pick .entry__category {\r\n    font-size: 15px;\r\n    line-height: 22px;\r\n  }\r\n}\r\n/* @media only screen and (min-width: 41.75rem) {\r\n  .editor-picks__secondary-inner-wrapper {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n  }\r\n  .editor-picks__secondary-pick {\r\n    width: 32% !important;\r\n    min-width: 32% !important;\r\n    margin-bottom: 0;\r\n  }\r\n} */\r\n/* @media only screen and (max-width: 61.1875rem) {\r\n  .editor-picks__secondary-pick .featured-image {\r\n    max-width: 100% !important;\r\n    min-width: 35% !important;\r\n    overflow: hidden;\r\n    position: relative;\r\n  }\r\n\r\n  .editor-picks__secondary-pick .featured-image:before {\r\n    position: absolute;\r\n    inset: 0;\r\n    display: block;\r\n    content: \" \";\r\n    width: 100%;\r\n    padding-top: 75%;\r\n  }\r\n\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .editor-picks__secondary-pick {\r\n    display: flex;\r\n    border-top: 1px solid #e8e8e8;\r\n    padding-top: 25px;\r\n    box-sizing: border-box;\r\n   \r\n  }\r\n  .editor-picks__secondary-pick .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .editor-picks__secondary-pick .entry .entry__category {\r\n    font-size: 11px;\r\n    order: 2;\r\n  }\r\n  .editor-picks__secondary-pick .entry .entry__heading {\r\n    order: 1;\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n  }\r\n} */\r\n"], "names": [], "mappings": "AAAA;;;;;AAIA;EACE;;;;;AAIF;;;;;;;AAOA;;;;;AAMA;;;;;;AAOA;;;;;AAIA;;;;AAGA;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;;;;;AAOA;;;;AAGA;;;;;AAIA;EACE"}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}