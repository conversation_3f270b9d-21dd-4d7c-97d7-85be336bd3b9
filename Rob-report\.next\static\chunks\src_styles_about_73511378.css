/* [project]/src/styles/about.css [client] (css) */
#about_wrapper {
  background-color: var(--body-bg-color);
  padding: 60px 0;
}

#about_wrapper h1 {
  font-size: 4rem;
  line-height: 1.1;
  font-family: rocky, sans-serif;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}

.container-wrap {
  width: 100%;
  height: 100%;
  display: flex;
}

.text-container {
  height: 100%;
  padding: 2rem 1rem;
  border-right: 1px solid var(--about-side-border);
  padding-right: 2rem;
}

.text-container p {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: .37px;
  font-weight: 300;
  font-family: Georgia, sans-serif;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.text-container p .bold {
  font-weight: 600;
}

.text-container p.bold-p {
  margin-bottom: .5rem;
}

.mt-top {
  margin-top: 2.5rem;
}

.tel {
  color: #3475de;
}

.text-container ul li {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: .37px;
  font-weight: 300;
  font-family: Georgia, sans-serif;
  color: var(--text-color);
  margin-bottom: .6rem;
  list-style-position: inside;
}

.text-container ul {
  padding-left: 20px;
}

.text-container ul li {
  list-style-position: outside;
  padding-left: 10px;
}

.text-container h2 {
  font-size: 26px;
  line-height: 32px;
  letter-spacing: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: var(--text-color);
  margin-bottom: 1rem;
}

@media screen and (width <= 767px) {
  #about_wrapper h1 {
    font-size: 2rem;
    color: var(--text-color);
    width: 95%;
    line-height: 1;
    font-family: rocky, sans-serif;
    letter-spacing: .37px;
    padding: 0 .625rem;
    margin-bottom: 1rem;
  }

  #about_wrapper .containerWrapper {
    padding: .625rem !important;
  }

  .text-container p, .text-container ul li {
    font-weight: 300;
    margin-bottom: 1.5rem;
    line-height: 26px;
    font-size: 16px;
    font-family: Georgia, sans-serif;
  }

  .text-container h2 {
    font-size: 17px;
    line-height: 22px;
  }

  .text-container ul li {
    margin-bottom: .6rem;
  }

  .text-container {
    height: 100%;
    padding: 1rem .625rem;
    border-right: none;
    border-bottom: 1px solid #00000026;
  }
}

@media screen and (width <= 960px) and (width >= 767px) {
  
}

@media screen and (width <= 1300px) and (width >= 960px) {
  
}

/*# sourceMappingURL=src_styles_about_73511378.css.map*/