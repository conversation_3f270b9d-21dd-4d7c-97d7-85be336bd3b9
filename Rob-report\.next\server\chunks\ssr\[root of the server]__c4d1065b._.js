module.exports = {

"[externals]/gsap/ScrollToPlugin.js [external] (gsap/ScrollToPlugin.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("gsap/ScrollToPlugin.js", () => require("gsap/ScrollToPlugin.js"));

module.exports = mod;
}}),
"[project]/src/components/common/BacktoTop.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/gsap [external] (gsap, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lia$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/lia/index.mjs [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$gsap$2f$ScrollToPlugin$2e$js__$5b$external$5d$__$28$gsap$2f$ScrollToPlugin$2e$js$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/gsap/ScrollToPlugin.js [external] (gsap/ScrollToPlugin.js, cjs)");
;
;
;
;
;
__TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__["default"].registerPlugin(__TURBOPACK__imported__module__$5b$externals$5d2f$gsap$2f$ScrollToPlugin$2e$js__$5b$external$5d$__$28$gsap$2f$ScrollToPlugin$2e$js$2c$__cjs$29$__["ScrollToPlugin"]);
const BackToTop = ()=>{
    const [showButton, setShowButton] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const [isDarkBg, setIsDarkBg] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const buttonRef = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const handleScroll = ()=>{
            const scrollY = window.scrollY;
            setShowButton(scrollY > 300);
            if (buttonRef.current) {
                const { left, top, width, height } = buttonRef.current.getBoundingClientRect();
                const x = left + width / 2;
                const y = top + height / 2;
                const elementBehind = document.elementFromPoint(x, y);
                if (elementBehind) {
                    const bgColor = window.getComputedStyle(elementBehind).backgroundColor;
                    const rgb = bgColor.match(/\d+/g)?.map(Number) || [
                        255,
                        255,
                        255
                    ];
                    const brightness = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;
                    setIsDarkBg(brightness < 128);
                }
            }
        };
        window.addEventListener("scroll", handleScroll);
        handleScroll(); // run initially
        return ()=>{
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);
    const scrollToTop = (e)=>{
        e.preventDefault();
        __TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__["default"].to(window, {
            scrollTo: {
                y: 0
            },
            duration: 1,
            ease: "power2.out"
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        id: "buttonTop",
        ref: buttonRef,
        className: `back-to-top ${showButton ? "show" : ""} ${isDarkBg ? "light" : "dark"}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
            className: "btn-top",
            "aria-label": "Back to Top",
            onClick: scrollToTop,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lia$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["LiaLongArrowAltUpSolid"], {}, void 0, false, {
                fileName: "[project]/src/components/common/BacktoTop.jsx",
                lineNumber: 65,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/BacktoTop.jsx",
            lineNumber: 60,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/BacktoTop.jsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = BackToTop;
}}),
"[project]/src/components/common/BacktoTop.jsx [ssr] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/components/common/BacktoTop.jsx [ssr] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__c4d1065b._.js.map