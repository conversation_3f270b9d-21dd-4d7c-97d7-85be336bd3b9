{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/react-tweet/dist/twitter-theme/theme.css"], "sourcesContent": [".react-tweet-theme {\n  --tweet-container-margin: 1.5rem 0;\n\n  /* Header */\n  --tweet-header-font-size: 0.9375rem;\n  --tweet-header-line-height: 1.25rem;\n\n  /* Text */\n  --tweet-body-font-size: 1.25rem;\n  --tweet-body-font-weight: 400;\n  --tweet-body-line-height: 1.5rem;\n  --tweet-body-margin: 0;\n\n  /* Quoted Tweet */\n  --tweet-quoted-container-margin: 0.75rem 0;\n  --tweet-quoted-body-font-size: 0.938rem;\n  --tweet-quoted-body-font-weight: 400;\n  --tweet-quoted-body-line-height: 1.25rem;\n  --tweet-quoted-body-margin: 0.25rem 0 0.75rem 0;\n\n  /* Info */\n  --tweet-info-font-size: 0.9375rem;\n  --tweet-info-line-height: 1.25rem;\n\n  /* Actions like the like, reply and copy buttons */\n  --tweet-actions-font-size: 0.875rem;\n  --tweet-actions-line-height: 1rem;\n  --tweet-actions-font-weight: 700;\n  --tweet-actions-icon-size: 1.25em;\n  --tweet-actions-icon-wrapper-size: calc(\n    var(--tweet-actions-icon-size) + 0.75em\n  );\n\n  /* Reply button */\n  --tweet-replies-font-size: 0.875rem;\n  --tweet-replies-line-height: 1rem;\n  --tweet-replies-font-weight: 700;\n}\n\n:where(.react-tweet-theme) * {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n:is([data-theme='light'], .light) :where(.react-tweet-theme),\n:where(.react-tweet-theme) {\n  --tweet-skeleton-gradient: linear-gradient(\n    270deg,\n    #fafafa,\n    #eaeaea,\n    #eaeaea,\n    #fafafa\n  );\n  --tweet-border: 1px solid rgb(207, 217, 222);\n  --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n    Helvetica, Arial, sans-serif;\n  --tweet-font-color: rgb(15, 20, 25);\n  --tweet-font-color-secondary: rgb(83, 100, 113);\n  --tweet-bg-color: #fff;\n  --tweet-bg-color-hover: rgb(247, 249, 249);\n  --tweet-quoted-bg-color-hover: rgba(0, 0, 0, 0.03);\n  --tweet-color-blue-primary: rgb(29, 155, 240);\n  --tweet-color-blue-primary-hover: rgb(26, 140, 216);\n  --tweet-color-blue-secondary: rgb(0, 111, 214);\n  --tweet-color-blue-secondary-hover: rgba(0, 111, 214, 0.1);\n  --tweet-color-red-primary: rgb(249, 24, 128);\n  --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);\n  --tweet-color-green-primary: rgb(0, 186, 124);\n  --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);\n  --tweet-twitter-icon-color: var(--tweet-font-color);\n  --tweet-verified-old-color: rgb(130, 154, 171);\n  --tweet-verified-blue-color: var(--tweet-color-blue-primary);\n}\n\n:is([data-theme='dark'], .dark) :where(.react-tweet-theme) {\n  --tweet-skeleton-gradient: linear-gradient(\n    270deg,\n    #15202b,\n    rgb(30, 39, 50),\n    rgb(30, 39, 50),\n    rgb(21, 32, 43)\n  );\n  --tweet-border: 1px solid rgb(66, 83, 100);\n  --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n    Helvetica, Arial, sans-serif;\n  --tweet-font-color: rgb(247, 249, 249);\n  --tweet-font-color-secondary: rgb(139, 152, 165);\n  --tweet-bg-color: rgb(21, 32, 43);\n  --tweet-bg-color-hover: rgb(30, 39, 50);\n  --tweet-quoted-bg-color-hover: rgba(255, 255, 255, 0.03);\n  --tweet-color-blue-primary: rgb(29, 155, 240);\n  --tweet-color-blue-primary-hover: rgb(26, 140, 216);\n  --tweet-color-blue-secondary: rgb(107, 201, 251);\n  --tweet-color-blue-secondary-hover: rgba(107, 201, 251, 0.1);\n  --tweet-color-red-primary: rgb(249, 24, 128);\n  --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);\n  --tweet-color-green-primary: rgb(0, 186, 124);\n  --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);\n  --tweet-twitter-icon-color: var(--tweet-font-color);\n  --tweet-verified-old-color: rgb(130, 154, 171);\n  --tweet-verified-blue-color: #fff;\n}\n\n@media (prefers-color-scheme: dark) {\n  :where(.react-tweet-theme) {\n    --tweet-skeleton-gradient: linear-gradient(\n      270deg,\n      #15202b,\n      rgb(30, 39, 50),\n      rgb(30, 39, 50),\n      rgb(21, 32, 43)\n    );\n    --tweet-border: 1px solid rgb(66, 83, 100);\n    --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n      Helvetica, Arial, sans-serif;\n    --tweet-font-color: rgb(247, 249, 249);\n    --tweet-font-color-secondary: rgb(139, 152, 165);\n    --tweet-bg-color: rgb(21, 32, 43);\n    --tweet-bg-color-hover: rgb(30, 39, 50);\n    --tweet-color-blue-primary: rgb(29, 155, 240);\n    --tweet-color-blue-primary-hover: rgb(26, 140, 216);\n    --tweet-color-blue-secondary: rgb(107, 201, 251);\n    --tweet-color-blue-secondary-hover: rgba(107, 201, 251, 0.1);\n    --tweet-color-red-primary: rgb(249, 24, 128);\n    --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);\n    --tweet-color-green-primary: rgb(0, 186, 124);\n    --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);\n    --tweet-twitter-icon-color: var(--tweet-font-color);\n    --tweet-verified-old-color: rgb(130, 154, 171);\n    --tweet-verified-blue-color: #fff;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;;;AA8BA;;;;;;;;;;;;;;;;;;;;;;AA6BA;EACE", "ignoreList": [0]}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}