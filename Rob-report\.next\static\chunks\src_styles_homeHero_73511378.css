/* [project]/src/styles/homeHero.css [client] (css) */
.specials-section {
  z-index: 3;
  background-color: var(--black);
  padding-top: .625rem;
  padding-bottom: 6.5625rem;
  position: relative;
  overflow-x: hidden;
}

.specials-slider-wrap {
  position: relative;
}

.swiper-horizontal {
  touch-action: pan-y;
}

.swiper-wrapper.specials_swiper_wrapper {
  z-index: -1;
  margin-left: 2.25rem;
  display: flex;
}

.swiper-slide.specials_swiper_slide {
  z-index: -1;
  cursor: grabbing;
  flex: none;
  width: 83rem;
  aspect-ratio: 4 / 2;
}

.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.special-stories-slider-block {
  z-index: 11;
  color: var(--black);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: relative;
  margin: 0 auto;
}

.special-stories-slider-block img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.film-overlay-wrap {
  background-image: linear-gradient(221.52deg, #0e101033 33.53%, #0e1010cc 63.89%, #0e1010fa 85.9%);
  justify-content: flex-start;
  align-items: flex-end;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 99;
  top: 0;
  padding-left: 2.5rem;
  padding: 0 5rem;
  padding-bottom: 4.375rem;
  padding-right: 2.5rem;
  display: flex;
}

.heroSliderContentWrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

.video-stories-slider-inner-block {
  z-index: 1;
  grid-column-gap: 1.375rem;
  grid-row-gap: 1.375rem;
  border-radius: .3125rem;
  flex-flow: column;
  justify-content: flex-end;
  align-items: flex-start;
  height: 100%;
  max-width: 35.5rem;
  display: flex;
  position: relative;
  font-family: sweet-sans-pro, sans-serif;
}

.heading-h2 {
  font-size: var(--desktop-font-heading--d-h2);
  letter-spacing: -.16px;
  margin-top: 0;
  margin-bottom: 0;
  font-weight: 400;
  line-height: 120%;
  font-family: rocky, sans-serif;
}

.special-stories-summary {
  color: var(--dark-gray);
  font-size: var(--mobile-font-size--subtitle-15);
  letter-spacing: .32px;
  width: 100%;
  max-width: 31rem;
  line-height: 1.40625rem;
  font-family: sweet-sans-pro, sans-serif;
}

.text-color-white {
  color: var(--white);
}

.special-stories-date-block {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  color: var(--red);
  font-size: var(--desktop-paragraph-font--small-p);
  letter-spacing: .16px;
  text-transform: uppercase;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.special-stories-date-block.color-white, .special-stories-title.letter-animation.color-white {
  color: var(--white);
}

.swiper-arrow-wrap {
  z-index: 5;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 95%;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  position: absolute;
  inset: 45% 0% auto;
  transform: translateY(-50%);
}

.play-films-html {
  color: var(--white);
  justify-content: center;
  align-items: center;
  display: flex;
}

.w-embed:before, .w-embed:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after, .swiper-button-next:after, .swiper-rtl .swiper-button-prev:after {
  content: "" !important;
}

.swiper-button-prev, .swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}

.swiper-button-prev, .swiper-button-next {
  position: static !important;
}

.swiper-button-prev, .swiper-button-next {
  color: #fff;
}

.swiper-button-prev, .swiper-button-next {
  border: 1px solid var(--white);
  color: var(--white);
  cursor: pointer;
  border-radius: 2.5rem;
  justify-content: center;
  align-items: center;
  width: 3.125rem;
  min-width: 3.125rem;
  height: 3.125rem;
  min-height: 3.125rem;
  transition: all .3s ease-in-out;
  display: flex;
}

.swiper-button-prev:hover {
  background-color: #fff;
}

.swiper-button-next:hover {
  background-color: #fff;
}

.swiper-button-next:hover svg:not(.swiper- button-prev) {
  fill: var(--black);
}

.swiper-button-prev:hover svg:not(.swiper- button-prev) {
  fill: var(--black);
}

.controls-wrapper {
  justify-content: space-between;
  align-items: flex-start;
  max-width: 80%;
  margin-top: 2.25rem;
  padding-left: 2.125rem;
  display: flex;
  font-family: sweet-sans-pro, sans-serif;
}

.next-up-text {
  color: var(--white);
  font-size: var(--desktop-font-size-normal--tag-14);
  letter-spacing: .16px;
  text-transform: uppercase;
  flex: none;
  position: relative;
}

.controls-row {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  flex: none;
  width: 100%;
  max-width: 15rem;
  display: flex;
  position: absolute;
  inset: auto 10.0625rem 0% auto;
}

.bullet-pagination {
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bullet-pagination .bullet {
  font-size: 15px;
  width: 100%;
  display: flex;
}

.swiper-paginationn {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative !important;
}

.customscrollbar {
  background-color: #fff3;
  width: 100%;
  height: 3px;
  position: relative;
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background-color: #fff !important;
}

@media screen and (width >= 1920px) {
  .swiper-slide.specials_swiper_slide {
    width: 96%;
  }

  .specials-section {
  }
}

@media screen and (width <= 991px) {
  .specials-section {
    padding-top: .3125rem;
    padding-bottom: 7rem;
  }

  .swiper-slide.specials_swiper_slide {
    width: 100%;
  }

  .swiper-wrapper.specials_swiper_wrapper {
    margin-left: auto;
  }

  .special-stories-summary {
    max-width: 33rem;
  }

  .swiper-arrow-wrap {
    grid-column-gap: .8125rem;
    grid-row-gap: .8125rem;
    justify-content: flex-end;
    align-items: center;
    display: flex;
    inset: auto 2% -3% auto;
  }

  .controls-wrapper {
    margin-top: 2.875rem;
  }

  .controls-row {
    max-width: 13.25rem;
    position: absolute;
    inset: auto 17% 1% auto;
  }
}

@media screen and (width <= 767px) {
  .specials-section {
    padding-bottom: 6.25rem;
  }

  .swiper-slide.specials_swiper_slide {
    width: 100%;
  }

  .special-stories-slider-block {
    justify-content: flex-start;
    align-items: flex-end;
  }

  .special-stories-slider-block img {
  }

  .film-overlay-wrap {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-bottom: 0;
  }

  .remove-margin-bottom {
    width: 100%;
  }

  .controls-wrapper {
    margin-top: 6.25rem;
    padding-left: 1.25rem;
  }

  .controls-row {
    inset: auto auto 10% 3%;
  }

  .swiper-arrow-wrap {
    max-width: 100%;
    bottom: 3%;
    right: 5%;
  }

  .swiper-button-prev, .swiper-button-next {
    width: 2.8rem !important;
    min-width: 2.8rem !important;
    height: 2.8rem !important;
    min-height: 2.8rem !important;
  }
}

@media screen and (width <= 600px) {
  .specials-section {
    background-color: var(--black-dark);
  }

  .swiper-slide.specials_swiper_slide {
    height: auto;
  }

  .special-stories-slider-block img {
  }

  .film-overlay-wrap {
    padding-left: 1rem;
    padding-right: 1rem;
    position: relative;
  }

  .video-stories-slider-inner-block {
    max-width: 100%;
    grid-column-gap: .7rem;
    grid-row-gap: .7rem;
  }

  .special-stories-date-block {
    grid-column-gap: .7rem;
    grid-row-gap: .7rem;
    font-size: var(--desktop-font-size-normal--tag-14);
  }

  .heading-h2 {
    font-size: var(--mobile-font-heading--m-h2);
  }

  .controls-wrapper {
    max-width: 100%;
  }

  .controls-row {
    max-width: 11rem;
    left: 5%;
  }
}

/*# sourceMappingURL=src_styles_homeHero_73511378.css.map*/