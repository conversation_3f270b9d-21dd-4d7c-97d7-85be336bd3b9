{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/globals.css"], "sourcesContent": [":root {\r\n  --background: #fff;\r\n  --foreground: #171717;\r\n\r\n  --btn-top-bg: #fff;\r\n  --gray-bg: rgba(0, 0, 0, 0.85);\r\n  /*  */\r\n  --red: red;\r\n  --mobile-font-size--size-normal: 1rem;\r\n  --black-dark: #0e1010;\r\n  --white: white;\r\n  --desktop-font-heading--d-h1: 4.25rem;\r\n  --mobile-font-heading--m-h1: 2.75rem;\r\n  --mobile-font-size--size-large-30: 1.875rem;\r\n  --desktop-font-heading--d-h2: 2.5rem;\r\n  --mobile-font-heading--m-h2: 1.2rem;\r\n  --desktop-font-heading--d-h3: 1.5625rem;\r\n  --mobile-font-heading--m-h3: 1.4375rem;\r\n  --desktop-font-size-normal--subtitle-18: 1.125rem;\r\n  --mobile-font-size--subtitle-15: 0.9375rem;\r\n  --orange: #f68a33;\r\n  --desktop-font-size-normal--font-size-large-60: 3.75rem;\r\n  --desktop-font-size-normal--body-19: 1.1875rem;\r\n  --transparent: #0000;\r\n  --dark-text: #0f1628;\r\n  --black: black;\r\n  --r-page--cta: 0.812rem;\r\n  --desktop-paragraph-font--small-p: 0.9375rem;\r\n  --desktop-font-size-normal--tag-14: 0.875rem;\r\n  --dark-gray: #727272;\r\n  --gray: #8d8d8d;\r\n  --black-2-0: #0f1628;\r\n  --mobile-font-size--small-12: 0.75rem;\r\n  --desktop-font-size-normal--questions-20: 1.25rem;\r\n  --border-light: #0f16284d;\r\n  --white-2-0: #efefef;\r\n  --white-light: #fafaf1;\r\n  --desktop-font-size-normal--caption-10: 0.625rem;\r\n  --green: #396e8b;\r\n  --border-dark: white;\r\n  --navy-blue: #0b347c;\r\n  --desktop-paragraph-font--paragraph: 1.0625rem;\r\n  --desktop-font-size-normal--intro-35: 2.1875rem;\r\n  --mobile-font-size--size-medium-20: 1.25rem;\r\n  --Rockyfontfamily: rocky, serif;\r\n  --Sweetfontfamily: sweet, serif;\r\n\r\n  --gray-span: #fff;\r\n\r\n  --body-bg-color: #000;\r\n  --body-bg-colorblackwhite: #fff;\r\n  --text-color: #fff;\r\n  --text-colorblack: #000;\r\n  --filterblack: invert(1);\r\n  --drawer-btn-bg: #000;\r\n  --drawer-btn-bg-hover: #fff;\r\n  --about-side-border: rgba(128, 128, 128, 0.6);\r\n  --chip-color: #fff;\r\n\r\n  --line-color: rgba(128, 128, 128, 0.6);\r\n}\r\n/* Light Mode Theme */\r\n[data-theme=\"light\"] {\r\n  --body-bg-color: #fff;\r\n  --chip-color: #000;\r\n  --body-bg-colorblackwhite: #000;\r\n  --text-color: #000;\r\n  --text-colorwhite: #fff;\r\n  --filterwhite: invert(100%);\r\n  --filterblack: invert(0);\r\n  --btn-top-bg: #000;\r\n  --gray-span: #575757;\r\n  --drawer-btn-bg: #fff;\r\n  --drawer-btn-bg-hover: #000;\r\n  --about-side-border: #00000026;\r\n  --line-color: #00000026;\r\n  --related-post-bg: #f7f7f7;\r\n}\r\n[data-theme=\"dark\"] {\r\n  --related-post-bg: rgba(225, 225, 225, 0.05);\r\n}\r\n/* .light {\r\n  background-color: #fff !important;\r\n  color: black;\r\n}\r\n.dark {\r\n  color: white;\r\n  background-color: black !important;\r\n} */\r\n.dark .nav-content {\r\n  color: #000;\r\n}\r\n.dark .nav-content .brand img {\r\n  filter: invert(0);\r\n}\r\n.dark .nav-content .menu-item {\r\n  color: #000;\r\n}\r\n.HalfWidthBtn {\r\n  align-items: center;\r\n  justify-content: center;\r\n  display: flex;\r\n  width: 100%;\r\n}\r\nhtml,\r\nbody {\r\n  max-width: 100vw;\r\n  /* overflow-x: hidden; */\r\n}\r\n\r\nbody {\r\n  /* color: var(--foreground); */\r\n  background: var(--background);\r\n  /* font-family: Arial, sans-serif; */\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  font-size: var(--mobile-font-size--size-normal);\r\n  /* background-color: #fafafa; */\r\n  font-weight: 400;\r\n  line-height: 120%;\r\n  /* font-size: 1vw; */\r\n  /* color: #333; */\r\n  background-color: var(--background);\r\n  min-height: 100%;\r\n  font-display: swap;\r\n}\r\n\r\n* {\r\n  box-sizing: border-box;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\na {\r\n  color: inherit;\r\n  text-decoration: none;\r\n}\r\n/* .d-flex {\r\n  display: flex !important;\r\n} */\r\n.w-100,\r\n.w-full {\r\n  width: 100% !important;\r\n}\r\n.w-50 {\r\n  width: 50% !important;\r\n}\r\n.p-0 {\r\n  padding: 0 !important;\r\n}\r\n.py-0 {\r\n  padding-block: 0 !important;\r\n}\r\n.pt-0 {\r\n  padding-top: 0 !important;\r\n}\r\n.pb-0 {\r\n  padding-bottom: 0 !important;\r\n}\r\n.pl-0 {\r\n  padding-left: 0 !important;\r\n}\r\n.pr-0 {\r\n  padding-right: 0 !important;\r\n}\r\n.mb-0 {\r\n  margin-bottom: 0 !important;\r\n}\r\n.mb-2 {\r\n  margin-bottom: 0.5rem !important;\r\n}\r\n.text-underline {\r\n  text-decoration: underline !important;\r\n}\r\n.text-bold {\r\n  font-weight: bold !important;\r\n}\r\n.overflow-hidden {\r\n  overflow: hidden !important;\r\n}\r\n.flex-space-between {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n.ShareBtn {\r\n  display: flex;\r\n  gap: 5px;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  /* opacity: 0.8; */\r\n  transition: all 0.5s ease;\r\n  line-height: 1;\r\n}\r\n.ShareBtn svg {\r\n  font-size: 1rem;\r\n  /* font-weight: 400; */\r\n}\r\n.ShareBtn span {\r\n  font-size: 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 400;\r\n}\r\n.ShareBtn:hover {\r\n  /* fill: rgb(163, 163, 163); */\r\n  opacity: 0.8;\r\n}\r\n\r\n#sideBtn_container {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  right: 10px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: end;\r\n  gap: 20px;\r\n  z-index: 998;\r\n}\r\n\r\n.back-to-top {\r\n  opacity: 0;\r\n  pointer-events: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.back-to-top.show {\r\n  opacity: 1;\r\n  pointer-events: auto;\r\n}\r\n.btn-top {\r\n  position: relative;\r\n  gap: 8px;\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  padding: 5px;\r\n  font-size: 1.35rem;\r\n  cursor: pointer;\r\n  border: 1px solid currentColor;\r\n  background-color: transparent;\r\n  color: var(--btn-top-color, #fff);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.btn-top svg {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n.btn-top {\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n.btn-top:hover {\r\n  scale: 0.93;\r\n}\r\n\r\n/* Dark background -> white button */\r\n.back-to-top.dark .btn-top {\r\n  color: #fff;\r\n  background-color: transparent;\r\n}\r\n\r\n/* Light background -> black button with white bg */\r\n.back-to-top.light .btn-top {\r\n  color: #000;\r\n  background-color: #fff;\r\n}\r\n\r\n.search_btn {\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n.search_btn:hover {\r\n  scale: 0.93;\r\n}\r\n.search-btn {\r\n  position: relative;\r\n  gap: 8px;\r\n  width: 40px;\r\n  height: 40px;\r\n  padding: 5px;\r\n  font-size: 1.8rem;\r\n  cursor: pointer;\r\n  border: 1px solid currentColor;\r\n  background-color: transparent;\r\n  color: var(--text-color) !important;\r\n}\r\n.search-btn svg {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Dark background -> white button */\r\n.search_btn.dark .search-btn {\r\n  color: #fff;\r\n  background-color: transparent;\r\n}\r\n\r\n/* Light background -> black button with white bg */\r\n.search_btn.light .search-btn {\r\n  color: white;\r\n  border: none;\r\n}\r\n\r\n/* Ads Css */\r\n.ad-flex-all {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.ad-text::before {\r\n  color: var(--gray-span);\r\n  content: \"ADVERTISEMENT\";\r\n  display: block;\r\n  font-family: Arial, sans-serif;\r\n  font-size: 9px;\r\n  font-weight: 400;\r\n  letter-spacing: 1px;\r\n  line-height: 1.2;\r\n  margin: 5px auto;\r\n  text-align: center;\r\n  text-transform: uppercase;\r\n  -webkit-font-smoothing: antialiased;\r\n}\r\n\r\n.chipContainer {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin: 0 42px 40px;\r\n}\r\n\r\n@media only screen and (min-width: 768px) {\r\n  #sideBtn_container {\r\n    bottom: 50px;\r\n    right: 25px;\r\n  }\r\n\r\n  .ShareBtn svg {\r\n    font-size: 1.25rem;\r\n  }\r\n  .ShareBtn span {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n.headingTitle,\r\n.headingTitle h1 {\r\n  display: inline;\r\n  font-size: 3vw;\r\n  line-height: 10vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 600;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.sponsoredTitle,\r\n.sponsoredTitle h1 {\r\n  display: inline;\r\n  font-size: 2.5vw;\r\n  line-height: 10vw;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 600;\r\n  letter-spacing: 1px;\r\n  color: #e02020;\r\n  text-transform: uppercase;\r\n}\r\n\r\nhtml.w-mod-touch * {\r\n  background-attachment: scroll !important;\r\n}\r\n.containerWrapper {\r\n  width: 100%;\r\n  /* background-color: var(--body-bg-color); */\r\n  max-width: 70rem;\r\n  margin: 0 auto;\r\n  padding: 2rem 0;\r\n  padding-right: 0.625rem !important;\r\n  padding-left: 0.625rem !important;\r\n}\r\n\r\n@media screen and (max-width: 1200px) {\r\n  .headingTitle,\r\n  .headingTitle h1 {\r\n    font-size: 4vw;\r\n  }\r\n  .sponsoredTitle,\r\n  .sponsoredTitle h1 {\r\n    font-size: 4vw;\r\n  }\r\n}\r\n@media screen and (max-width: 768px) {\r\n  .headingTitle,\r\n  .headingTitle h1 {\r\n    font-size: 5vw;\r\n  }\r\n  .sponsoredTitle,\r\n  .sponsoredTitle h1 {\r\n    font-size: 5vw;\r\n  }\r\n  .chipContainer {\r\n    margin: 10px 10px 25px 10px;\r\n  }\r\n}\r\n@media screen and (max-width: 479px) {\r\n  .headingTitle,\r\n  .headingTitle h1 {\r\n    font-size: 6.5vw;\r\n  }\r\n  .sponsoredTitle,\r\n  .sponsoredTitle h1 {\r\n    font-size: 6vw;\r\n  }\r\n}\r\n@media only screen and (min-width: 92.5rem) {\r\n  .containerWrapper {\r\n    max-width: 100rem;\r\n    width: calc(100% - 21.875rem);\r\n    padding-right: 0.625rem !important;\r\n    padding-left: 0.625rem !important;\r\n  }\r\n}\r\n/* button css */\r\n.w-inline-block {\r\n  max-width: 100%;\r\n  display: inline-block;\r\n}\r\n.button_base {\r\n  font-weight: 500;\r\n  font-size: 17px;\r\n  line-height: 20px;\r\n  letter-spacing: 1px;\r\n  display: block;\r\n  /* padding: 1.125rem 0 0.875rem; */\r\n  text-align: center;\r\n  text-transform: uppercase;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  /* width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center; */\r\n}\r\n.view-more {\r\n  padding: 1.125rem 0 0.875rem;\r\n}\r\n.button_base.black {\r\n  background-color: #323440;\r\n  letter-spacing: 1.4px;\r\n  color: var(--white);\r\n  font-weight: 700;\r\n  text-transform: uppercase;\r\n  padding: 1.25rem 1.875rem;\r\n  /* font-size: 1.2vw; */\r\n  transition: all 0.5s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.button_base.black:hover {\r\n  background-color: #323440e6;\r\n}\r\n/* drawer save btn */\r\n.drawer_footer .button_base.black {\r\n  background-color: var(--drawer-btn-bg);\r\n  color: var(--drawer-btn-bg-hover);\r\n}\r\n.drawer_footer .button_base.black:hover {\r\n  background-color: var(--drawer-btn-bg-hover);\r\n  color: var(--drawer-btn-bg);\r\n}\r\n.hasMore_btn_wrap {\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n.hasMore_btn_wrap .button_base {\r\n  min-width: calc(50% - 1.625rem * 2);\r\n  border: none;\r\n}\r\n\r\n.sectioner--latest-stories .view-more-stories {\r\n  width: 100%;\r\n  margin-top: 2.5rem;\r\n}\r\n#home_wrappper {\r\n  background-color: var(--body-bg-color);\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .sectioner--latest-stories .view-more-stories {\r\n    margin-top: 0;\r\n    margin-bottom: 0;\r\n  }\r\n  .button_base.black {\r\n    width: 100%;\r\n    display: inline-block;\r\n    font-size: 17px;\r\n    line-height: 10px;\r\n    letter-spacing: 1px;\r\n  }\r\n}\r\n@media only screen and (max-width: 479px) {\r\n  .button_base.black {\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 100%;\r\n    font-size: 4vw;\r\n    display: flex;\r\n    padding: 1.25rem 0.875rem;\r\n  }\r\n}\r\n/* button css */\r\n/* ::-webkit-scrollbar-button {\r\n  transition: 0ms !important;\r\n  width: 0px;\r\n  height: 0px;\r\n} */\r\n/* ::-webkit-scrollbar-track {\r\n  background: transparent;\r\n}\r\n::-webkit-scrollbar-thumb {\r\n  -webkit-border-radius: 0px;\r\n  border-radius: 0px;\r\n  background: transparent;\r\n  opacity: 0;\r\n} */\r\n::-webkit-resizer {\r\n  width: 0px;\r\n  height: 0px;\r\n}\r\n/* ::-webkit-scrollbar {\r\n  width: 0px;\r\n} */\r\n.cont-link .ex-text {\r\n  pointer-events: none;\r\n}\r\n\r\n/* transitionloader */\r\n.modes_helpers {\r\n  height: calc(var(--vh, 1vh) * 100);\r\n  position: fixed;\r\n  pointer-events: none;\r\n  z-index: 9999;\r\n  visibility: hidden;\r\n  bottom: auto;\r\n  width: 100%;\r\n}\r\n\r\n.modes_helpers i {\r\n  transform: scaleY(0);\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  visibility: visible;\r\n}\r\n\r\n.modes_helpers .a {\r\n  background-color: #000000;\r\n}\r\n\r\n.modes_helpers .b {\r\n  background-color: #ffffff;\r\n}\r\n\r\n.modes_helpers.reverse .a {\r\n  background-color: #000;\r\n}\r\n\r\n.modes_helpers.reverse .b {\r\n  background: #fff;\r\n}\r\n/* transitionloader */\r\n/* tagwrapper */\r\n.TagWrapper {\r\n  color: var(--text-color);\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n}\r\n.TagWrapper .featured-category__story .entry__heading {\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n}\r\n\r\n/* tagwrapper */\r\n\r\n/* Page Loader */\r\n.result-loader-div {\r\n  /* position: absolute; */\r\n  /* z-index: 999; */\r\n  /* top: 0; */\r\n  width: 100vw;\r\n  /* height: 100vh; */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  /* margin-block: 50%; */\r\n}\r\n.loader-cont {\r\n  position: fixed;\r\n  z-index: 999;\r\n  top: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: var(--body-bg-color);\r\n}\r\n.MuiLinearProgress-root {\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: block;\r\n  z-index: 0;\r\n  background-color: rgb(220, 220, 220) !important;\r\n  height: 2px !important;\r\n}\r\n\r\n.MuiBox-root {\r\n  width: 15% !important;\r\n}\r\n\r\n.MuiLinearProgress-bar {\r\n  background-color: var(--text-colorblack) !important;\r\n}\r\n\r\n.MuiLinearProgress-bar1 {\r\n  width: 100%;\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  top: 0;\r\n  -webkit-transition: -webkit-transform 0.2s linear;\r\n  transition: transform 0.2s linear;\r\n  transform-origin: left;\r\n  background-color: var(--text-colorblack);\r\n  width: auto;\r\n  -webkit-animation: animation-1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395)\r\n    infinite;\r\n  animation: animation-1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;\r\n}\r\n\r\n.MuiLinearProgress-bar2 {\r\n  width: 100%;\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n  top: 0;\r\n  -webkit-transition: -webkit-transform 0.2s linear;\r\n  transition: transform 0.2s linear;\r\n  transform-origin: left;\r\n  --LinearProgressBar2-barColor: var(--text-colorblack);\r\n  background-color: var(--LinearProgressBar2-barColor, currentColor);\r\n  width: auto;\r\n  -webkit-animation: animation-2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s\r\n    infinite;\r\n  animation: animation-2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;\r\n}\r\n\r\n@keyframes animation-1 {\r\n  0% {\r\n    left: -35%;\r\n    right: 100%;\r\n  }\r\n\r\n  60% {\r\n    left: 100%;\r\n    right: -90%;\r\n  }\r\n\r\n  100% {\r\n    left: 100%;\r\n    right: -90%;\r\n  }\r\n}\r\n\r\n@keyframes animation-2 {\r\n  0% {\r\n    left: -200%;\r\n    right: 100%;\r\n  }\r\n\r\n  60% {\r\n    left: 107%;\r\n    right: -8%;\r\n  }\r\n\r\n  100% {\r\n    left: 107%;\r\n    right: -8%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;;;;;;;;;;;;;;;;;AAgBA;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAMA;;;;;;;;;;;;AAiBA;;;;;;AAMA;;;;;AAOA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;;;AASA;;;;AAIA;;;;;;AAKA;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;AAIA;;;;;;;;;;;;;;;AAeA;;;;;;;AAMA;;;;;AAIA;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;;AAYA;;;;;;;AAQA;;;;;AAMA;;;;;AAMA;;;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;;;;AAQA;EACE;;;;;EAKA;;;;EAGA;;;;;AAIF;;;;;;;;;AAUA;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;AAUA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;AAIF;EACE;;;;EAIA;;;;;AAKF;EACE;;;;;;;;AAQF;;;;;AAIA;;;;;;;;;;;AAeA;;;;AAGA;;;;;;;;;;;AAYA;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAKA;;;;;AAIA;;;;AAGA;EACE;;;;;EAIA;;;;;;;;;AAQF;EACE;;;;;;;;;;AAwBF;;;;;AAOA;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;AAKA;;;;;AAQA;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;;;;;;;;AAiBA"}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}