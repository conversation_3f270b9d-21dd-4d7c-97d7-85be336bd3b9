module.exports = {

"[project]/src/components/common/BacktoTop.jsx [ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_react-icons_lia_index_mjs_2c85c98e._.js",
  "server/chunks/ssr/[root of the server]__c4d1065b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/common/BacktoTop.jsx [ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),

};