{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/footer.css"], "sourcesContent": [".footer {\r\n  background-color: var(--black);\r\n}\r\n.wrapper_footer {\r\n  padding-top: 0;\r\n  padding-bottom: 2.5rem;\r\n}\r\n.dest_news {\r\n  border-bottom: 1px solid #f1f1f126;\r\n  width: 100%;\r\n  /* height: 80vh; */\r\n  display: flex;\r\n}\r\n.dest_ft {\r\n  border-right: 1px solid #f1f1f126;\r\n  width: 50%;\r\n  height: 100%;\r\n}\r\n\r\n.btn_wrapper {\r\n  border-top: 1px solid #f1f1f126;\r\n  padding: 16px 32px;\r\n  border-bottom: 1px solid #f1f1f126;\r\n}\r\n.btn_wrapper.expobtn {\r\n  padding: 16px 32px 16px 0;\r\n}\r\n.btn_wrapper.subbtn {\r\n  padding: 16px 0 16px 16;\r\n}\r\n\r\n.news_ft {\r\n  width: 50%;\r\n  height: 100%;\r\n}\r\n.dt_nw_wrapper {\r\n  flex-flow: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  /* padding: 52px 0 0; */\r\n  display: flex;\r\n}\r\n\r\n.footer_button {\r\n  background-color: var(--white);\r\n  color: var(--black);\r\n  letter-spacing: 1px;\r\n  border-radius: 2px;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: auto;\r\n  height: 52px;\r\n  margin-left: auto;\r\n  padding: 8px 62px 8px;\r\n  transition: all 0.4s;\r\n  display: flex;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n}\r\n.w-form {\r\n  margin: 0 0 15px;\r\n}\r\n.form-block {\r\n  margin-bottom: 0;\r\n}\r\n.subscribe_flex {\r\n  justify-content: space-around;\r\n  display: flex;\r\n}\r\n.w-form-done {\r\n  text-align: center;\r\n  background-color: #ddd;\r\n  padding: 20px;\r\n  display: none;\r\n}\r\n.subscribe_thanks {\r\n  background-color: var(--white);\r\n  border-radius: 2px;\r\n}\r\n.w-form-fail {\r\n  background-color: #ffdede;\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  display: none;\r\n}\r\n.error_state {\r\n  letter-spacing: 1px;\r\n\r\n  background-color: #fc5656;\r\n  border-radius: 3px;\r\n  width: 80%;\r\n  margin-top: 12px;\r\n  margin-left: 0;\r\n  margin-right: 0;\r\n  padding: 7px 12px 6px;\r\n  font-size: 0.9em;\r\n  line-height: 1.2;\r\n  position: static;\r\n}\r\n.w-input,\r\n.w-select {\r\n  color: #333;\r\n  vertical-align: middle;\r\n  background-color: #fff;\r\n  border: 1px solid #ccc;\r\n  width: 100%;\r\n  height: 38px;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  font-size: 14px;\r\n  line-height: 1.42857;\r\n  display: block;\r\n}\r\n.field_prefooter {\r\n  color: var(--white);\r\n  letter-spacing: 0.03em;\r\n  background-color: #fff0;\r\n  border: 1px solid #f1f1f140;\r\n  border-radius: 3px;\r\n  width: 100%;\r\n  height: 52px;\r\n  margin-bottom: 0;\r\n  margin-right: 8px;\r\n  padding: 8px 16px 4px;\r\n  transition: all 0.4s;\r\n}\r\ninput.w-button {\r\n  -webkit-appearance: button;\r\n  cursor: pointer;\r\n}\r\n.footer_links {\r\n  padding-top: 0;\r\n}\r\n.wrapper_footer_links {\r\n  padding-top: 32px;\r\n  /* padding-left: 32px; */\r\n  /* padding-right: 32px; */\r\n}\r\n.flexbox_footer {\r\n  display: flex;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.footer-left-block {\r\n  width: 50%;\r\n}\r\n.footer-right-block {\r\n  width: 50%;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  /* gap: 10%; */\r\n}\r\n.div-block-2 {\r\n  width: 100%;\r\n}\r\n.title_footer {\r\n  opacity: 0.65;\r\n  color: var(--white);\r\n  text-transform: uppercase;\r\n  margin-bottom: 16px;\r\n  font-size: 0.9em;\r\n  letter-spacing: 1px;\r\n}\r\n.links_flex {\r\n  grid-column-gap: 4px;\r\n  grid-row-gap: 4px;\r\n  flex-flow: column;\r\n  justify-content: flex-start;\r\n  align-items: flex-start;\r\n  display: flex;\r\n}\r\n.footer_link {\r\n  color: var(--white);\r\n  letter-spacing: 1px;\r\n  font-size: 0.9em;\r\n  transition: all 0.4s;\r\n}\r\n.last_line {\r\n  margin-top: 8em;\r\n}\r\n.flexbox_line {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n.left_rights {\r\n  width: 30%;\r\n  margin-right: 0;\r\n}\r\n.footer_link:hover,\r\n.footer_link.just_rights {\r\n  opacity: 0.65;\r\n}\r\n.privacy_box {\r\n  width: 25%;\r\n  margin-right: 0;\r\n  display: flex;\r\n  grid-column-gap: 15px;\r\n  grid-row-gap: 15px;\r\n  justify-content: flex-end;\r\n}\r\n.website_by {\r\n  justify-content: flex-end;\r\n  align-items: flex-start;\r\n  width: 25%;\r\n  margin-left: auto;\r\n  display: flex;\r\n}\r\n.footer_link.web_by {\r\n  opacity: 0.65;\r\n}\r\n.footer-brand {\r\n  position: relative;\r\n  width: 280px;\r\n  height: 55px;\r\n  overflow: hidden;\r\n}\r\n.footer-brand img {\r\n  /* aspect-ratio: 1; */\r\n  /* width: 200px;\r\n  height: auto; */\r\n  object-fit: cover;\r\n  filter: invert();\r\n  transition: filter 0.45s ease-in 0.5s;\r\n\r\n  /* fill: var(--text-color); */\r\n}\r\n/* Sub Footer */\r\n.sub-footer {\r\n  background-color: var(--gray-bg);\r\n}\r\n.sub-footer > .containerWrapper {\r\n  padding: 10px 0;\r\n}\r\n.flexbox_sub-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  /* height: 60px; */\r\n}\r\n.footer-tag-line {\r\n  color: var(--white);\r\n  letter-spacing: 1px;\r\n  font-size: 0.8em;\r\n  transition: all 0.4s;\r\n}\r\n.footer-vr-line {\r\n  display: inline-block;\r\n  align-self: stretch;\r\n  background: #fff;\r\n  width: 1px;\r\n  height: 100%;\r\n  opacity: 0.7;\r\n  margin: auto 15px;\r\n  min-height: 2.75em;\r\n}\r\n.sub-footer-img-block {\r\n  position: relative;\r\n  width: 180px;\r\n  min-width: 180px;\r\n  height: 5vw;\r\n  overflow: hidden;\r\n}\r\n.sub-footer-img-block-2 {\r\n  position: relative;\r\n  width: 80px;\r\n  min-width: 80px;\r\n  height: 5vw;\r\n  overflow: hidden;\r\n}\r\n.sub-footer-img-block img,\r\n.sub-footer-img-block-2 img {\r\n  object-fit: contain;\r\n}\r\n@media screen and (max-width: 991px) {\r\n  /* .dest_news {\r\n    height: 65vh;\r\n  } */\r\n  .btn_wrapper {\r\n    padding-left: 24px;\r\n    padding-right: 24px;\r\n  }\r\n  .footer_button {\r\n    padding-left: 32px;\r\n    padding-right: 32px;\r\n    font-size: 1em;\r\n  }\r\n  .field_prefooter {\r\n    font-size: 1em;\r\n  }\r\n  .subscribe_thanks {\r\n    border-radius: 2px;\r\n  }\r\n  .error_state {\r\n    padding-top: 10px;\r\n    font-size: 1.5em;\r\n  }\r\n  .thanks_txt {\r\n    font-size: 1.5em;\r\n    line-height: 1.1;\r\n  }\r\n  .wrapper_footer_links {\r\n    padding-top: 32px;\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n  }\r\n  .title_footer {\r\n    letter-spacing: 1px;\r\n\r\n    font-size: 0.9em;\r\n  }\r\n  .footer_link {\r\n    font-size: 1em;\r\n  }\r\n  .last_line {\r\n    margin-top: 25em;\r\n  }\r\n  .privacy_box {\r\n    grid-column-gap: 15px;\r\n    grid-row-gap: 15px;\r\n    width: 50%;\r\n    display: flex;\r\n  }\r\n  .website_by {\r\n    width: 20%;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .wrapper_footer {\r\n    padding-bottom: 20px;\r\n  }\r\n  .dest_news {\r\n    flex-flow: column;\r\n    height: auto;\r\n  }\r\n  .dest_ft,\r\n  .news_ft {\r\n    width: 100%;\r\n  }\r\n  .btn_wrapper {\r\n    /* margin-top: 6em; */\r\n    padding-left: 20px;\r\n    padding-right: 20px;\r\n  }\r\n  .footer_button {\r\n    font-size: 0.9em;\r\n  }\r\n  .field_prefooter {\r\n    font-size: 0.8em;\r\n  }\r\n  .subscribe_thanks {\r\n    border-radius: 2px;\r\n  }\r\n  .error_state {\r\n    font-size: 2em;\r\n  }\r\n  .thanks_txt {\r\n    font-size: 1em;\r\n  }\r\n  .wrapper_footer_links {\r\n    padding-top: 32px;\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n  }\r\n  .flexbox_footer {\r\n    flex-direction: column;\r\n  }\r\n  .footer-left-block {\r\n    width: 100%;\r\n    margin-bottom: 30px;\r\n  }\r\n  .footer-right-block {\r\n    width: 100%;\r\n    display: grid;\r\n    justify-content: flex-start;\r\n    grid-column-gap: 0px;\r\n    grid-row-gap: 52px;\r\n    grid-template-rows: auto auto;\r\n    grid-template-columns: 1fr 1fr;\r\n    grid-auto-columns: 1fr;\r\n  }\r\n  .sub-footer-img-block,\r\n  .sub-footer-img-block-2 {\r\n    width: 100%;\r\n  }\r\n  .footer-hr-line {\r\n    display: inline-block;\r\n    align-self: stretch;\r\n    background: #fff;\r\n    width: 100%;\r\n    height: 1px;\r\n    opacity: 0.7;\r\n    margin: 15px auto;\r\n  }\r\n\r\n  .flexbox_sub-footer {\r\n    flex-wrap: wrap;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .sub-footer-img-block {\r\n    order: 1;\r\n    width: calc(50% - (0.5px + 5px));\r\n    min-width: calc(50% - (0.5px + 5px));\r\n    height: 50px;\r\n  }\r\n\r\n  .footer-vr-line:first-of-type {\r\n    order: 2;\r\n    height: 100%;\r\n    width: 1px;\r\n    margin: 0;\r\n  }\r\n\r\n  .sub-footer-img-block-2 {\r\n    order: 3;\r\n    width: calc(50% - (0.5px + 5px));\r\n    min-width: calc(50% - (0.5px + 5px));\r\n    height: 50px;\r\n  }\r\n\r\n  .footer-vr-line:last-of-type {\r\n    order: 4;\r\n    width: 100%;\r\n    height: 1px;\r\n    min-height: 1px;\r\n    margin: 15px auto;\r\n  }\r\n\r\n  .footer-tag-line {\r\n    order: 5;\r\n    width: 100%;\r\n  }\r\n  /* .div-block-2 {\r\n    width: 50%;\r\n  } */\r\n  /* .div-block-3 {\r\n    width: 50%;\r\n  } */\r\n  .title_footer {\r\n    letter-spacing: 1px;\r\n\r\n    font-size: 1em;\r\n  }\r\n  .links_flex {\r\n    grid-column-gap: 4px;\r\n    grid-row-gap: 4px;\r\n  }\r\n  .footer_link {\r\n    letter-spacing: 1px;\r\n\r\n    padding-top: 0.2em;\r\n    font-size: 1em;\r\n  }\r\n  .last_line {\r\n    margin-top: 20em;\r\n  }\r\n  .flexbox_line {\r\n    flex-flow: column;\r\n  }\r\n  .privacy_box {\r\n    grid-column-gap: 16px;\r\n    grid-row-gap: 16px;\r\n    width: 100%;\r\n    margin-top: 8px;\r\n    display: flex;\r\n    justify-content: flex-start;\r\n  }\r\n  .website_by {\r\n    justify-content: flex-start;\r\n    align-items: flex-start;\r\n    width: 50%;\r\n    margin-top: 32px;\r\n    margin-left: 0;\r\n  }\r\n\r\n  .footer_link.web_by {\r\n    padding-top: 0.2em;\r\n  }\r\n}\r\n@media screen and (max-width: 479px) {\r\n  .footer-brand {\r\n    position: relative;\r\n    width: 180px;\r\n    height: 40px;\r\n    overflow: hidden;\r\n  }\r\n  .div-block-3 {\r\n    width: 100%;\r\n  }\r\n  .left_rights {\r\n    width: 100%;\r\n  }\r\n  .wrapper_footer_links {\r\n    padding-top: 32px;\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n  }\r\n  .flexbox_footer {\r\n    flex-direction: column;\r\n  }\r\n  .footer-left-block {\r\n    width: 100%;\r\n    margin-bottom: 30px;\r\n  }\r\n  .footer-right-block {\r\n    width: 100%;\r\n    display: grid;\r\n    justify-content: flex-start;\r\n    grid-column-gap: 20px;\r\n    grid-row-gap: 32px;\r\n    grid-template-rows: auto auto;\r\n    grid-template-columns: 1fr 1fr;\r\n    grid-auto-columns: 1fr;\r\n  }\r\n  .sub-footer-img-block,\r\n  .sub-footer-img-block-2 {\r\n    height: 40px;\r\n  }\r\n  .last_line {\r\n    margin-top: 1em;\r\n  }\r\n  .website_by {\r\n    justify-content: flex-start;\r\n    align-items: flex-start;\r\n    width: 100%;\r\n    margin-top: 52px;\r\n    margin-left: 0;\r\n  }\r\n  .footer_link.web_by {\r\n    text-decoration: underline;\r\n  }\r\n  .btn_wrapper.subbtn {\r\n    padding: 16px 0 16px 0px;\r\n  }\r\n  .btn_wrapper.expobtn {\r\n    padding: 16px 0px 16px 0;\r\n  }\r\n  .title_footer {\r\n    font-size: 0.9em;\r\n  }\r\n  .footer_link {\r\n    font-size: 0.9em;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAGA;;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;AAIA;;;;;AAIA;;;;;;;AAQA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;AAaA;;;;;AAIA;;;;AAGA;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;AAMA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;AAIA;;;;;;;;;AAQA;;;;;;;;AAOA;;;;AAGA;;;;;;;AAMA;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;;AAUA;;;;;;;;AAOA;;;;;;;;AAOA;;;;AAIA;EAIE;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;AAIF;EACE;;;;EAGA;;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAIA;;;;;;;;;;EAUA;;;;;;EAMA;;;;;;;EAOA;;;;;;;EAOA;;;;;;;EAOA;;;;;;;;EAQA;;;;;EAUA;;;;;EAKA;;;;;EAIA;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;;;;;EAQA;;;;;AAIF;EACE;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAIA;;;;EAGA;;;;;;;;EAOA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA"}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}