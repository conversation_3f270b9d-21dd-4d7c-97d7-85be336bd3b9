{"name": "style-to-object", "version": "1.0.8", "description": "Parse CSS inline style to JavaScript object.", "author": "<PERSON> <<EMAIL>>", "main": "./cjs/index.js", "module": "./esm/index.mjs", "exports": {"import": "./esm/index.mjs", "require": "./cjs/index.js"}, "scripts": {"build": "run-s build:*", "build:cjs": "tsc", "build:esm": "awk '!/sourceMappingURL/' cjs/index.d.ts > esm/index.d.mts", "build:umd": "rollup --config --failAfterWarnings", "clean": "rm -rf cjs coverage dist", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "lint:tsc": "tsc --noEmit", "prepare": "husky", "prepublishOnly": "run-s lint lint:tsc test clean build", "test": "jest", "test:ci": "CI=true jest --ci --colors --coverage", "test:esm": "npm run build:cjs && node --test __tests__", "test:watch": "npm run test -- --watch"}, "repository": {"type": "git", "url": "https://github.com/remarkablemark/style-to-object"}, "bugs": {"url": "https://github.com/remarkablemark/style-to-object/issues"}, "keywords": ["style-to-object", "inline", "style", "parser", "css", "object", "pojo"], "dependencies": {"inline-style-parser": "0.2.4"}, "devDependencies": {"@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@eslint/compat": "1.1.1", "@eslint/eslintrc": "3.1.0", "@eslint/js": "9.10.0", "@rollup/plugin-commonjs": "26.0.1", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-terser": "0.4.4", "@rollup/plugin-typescript": "11.1.6", "@types/jest": "29.5.12", "@types/node": "22.5.4", "@typescript-eslint/eslint-plugin": "8.5.0", "@typescript-eslint/parser": "8.5.0", "eslint": "9.10.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-simple-import-sort": "12.1.1", "globals": "15.9.0", "husky": "9.1.5", "jest": "29.7.0", "lint-staged": "15.2.10", "npm-run-all": "4.1.5", "prettier": "3.3.3", "rollup": "4.21.2", "ts-jest": "29.2.5", "typescript": "5.6.2"}, "files": ["/cjs", "/dist", "/esm", "/src"], "license": "MIT"}