{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/herosectionbanner.css"], "sourcesContent": ["/* ._bgCntr {\r\n  background-color: var(--body-bg-color);\r\n} */\r\n.fullWidthCntr {\r\n  position: relative;\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n  /* padding-top: 1rem; */\r\n}\r\n.editor-picks__primary-pick {\r\n  width: 100%;\r\n  padding: 1.25rem;\r\n  padding-bottom: 0;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n.editor-picks__primary-pick .post-meta {\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  align-items: flex-start;\r\n  justify-content: center;\r\n  padding-bottom: 35px;\r\n}\r\n.editor-picks__primary-pick .featured-image {\r\n  position: relative;\r\n  margin-bottom: 1.25rem;\r\n  width: 100%;\r\n  aspect-ratio: 16/9;\r\n  overflow: hidden;\r\n  /* transform: translateX(-25px); */\r\n}\r\n.featured-image a {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n.editor-picks__primary-pick .featured-image img {\r\n  object-fit: cover;\r\n}\r\n.editor-picks__primary-pick .entry {\r\n  padding: 0.9375rem 2.8125rem;\r\n  padding-bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n  margin-top: -50px;\r\n  position: relative;\r\n  z-index: 99;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n.editor-picks__primary-pick .entry__category {\r\n  font-size: 11px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 15px;\r\n  /* letter-spacing: 0.35px; */\r\n}\r\n.editor-picks__primary-pick .entry__heading {\r\n  font-size: 23px;\r\n  line-height: 28px;\r\n  text-transform: none;\r\n  margin-bottom: 0.625rem;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n  font-family: rocky, sans-serif;\r\n}\r\n.editor-picks__primary-pick .entry__excerpt {\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  margin-bottom: 1.25rem;\r\n  width: 100%;\r\n  letter-spacing: 0.37px;\r\n}\r\n.editor-picks__primary-pick .entry__excerpt {\r\n  font-size: 19px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  margin-bottom: 0.625rem;\r\n  font-weight: 300;\r\n  line-height: 1.1;\r\n}\r\n@media only screen and (min-width: 61.25rem) {\r\n  .editor-picks__primary-pick {\r\n    padding: 0;\r\n    text-align: left;\r\n  }\r\n  .editor-picks__primary-pick .featured-image {\r\n    position: relative;\r\n    width: 100%;\r\n    aspect-ratio: 16/9;\r\n    overflow: hidden;\r\n    margin-bottom: 0;\r\n    transform: none;\r\n  }\r\n  .editor-picks__primary-pick .entry__category {\r\n    font-size: 15px;\r\n    line-height: 22px;\r\n  }\r\n  .editor-picks__primary-pick .entry__heading {\r\n    font-size: 34px;\r\n    line-height: 41px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;;;AAMA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAMA;;;;AAGA;;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;AAOA;EACE;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}