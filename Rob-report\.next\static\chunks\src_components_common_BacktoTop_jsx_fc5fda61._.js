(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_components_common_BacktoTop_jsx_fc5fda61._.js", {

"[project]/src/components/common/BacktoTop.jsx [client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-icons_lia_index_mjs_55ed59cf._.js",
  "static/chunks/node_modules_gsap_ScrollToPlugin_7fa90d3d.js",
  "static/chunks/src_components_common_BacktoTop_jsx_f4f28639._.js",
  "static/chunks/src_components_common_BacktoTop_jsx_0fb79dc2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/common/BacktoTop.jsx [client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);