/* [project]/src/styles/globals.css [client] (css) */
:root {
  --background: #fff;
  --foreground: #171717;
  --btn-top-bg: #fff;
  --gray-bg: #000000d9;
  --red: red;
  --mobile-font-size--size-normal: 1rem;
  --black-dark: #0e1010;
  --white: white;
  --desktop-font-heading--d-h1: 4.25rem;
  --mobile-font-heading--m-h1: 2.75rem;
  --mobile-font-size--size-large-30: 1.875rem;
  --desktop-font-heading--d-h2: 2.5rem;
  --mobile-font-heading--m-h2: 1.2rem;
  --desktop-font-heading--d-h3: 1.5625rem;
  --mobile-font-heading--m-h3: 1.4375rem;
  --desktop-font-size-normal--subtitle-18: 1.125rem;
  --mobile-font-size--subtitle-15: .9375rem;
  --orange: #f68a33;
  --desktop-font-size-normal--font-size-large-60: 3.75rem;
  --desktop-font-size-normal--body-19: 1.1875rem;
  --transparent: #0000;
  --dark-text: #0f1628;
  --black: black;
  --r-page--cta: .812rem;
  --desktop-paragraph-font--small-p: .9375rem;
  --desktop-font-size-normal--tag-14: .875rem;
  --dark-gray: #727272;
  --gray: #8d8d8d;
  --black-2-0: #0f1628;
  --mobile-font-size--small-12: .75rem;
  --desktop-font-size-normal--questions-20: 1.25rem;
  --border-light: #0f16284d;
  --white-2-0: #efefef;
  --white-light: #fafaf1;
  --desktop-font-size-normal--caption-10: .625rem;
  --green: #396e8b;
  --border-dark: white;
  --navy-blue: #0b347c;
  --desktop-paragraph-font--paragraph: 1.0625rem;
  --desktop-font-size-normal--intro-35: 2.1875rem;
  --mobile-font-size--size-medium-20: 1.25rem;
  --Rockyfontfamily: rocky, serif;
  --Sweetfontfamily: sweet, serif;
  --gray-span: #fff;
  --body-bg-color: #000;
  --body-bg-colorblackwhite: #fff;
  --text-color: #fff;
  --text-colorblack: #000;
  --filterblack: invert(1);
  --drawer-btn-bg: #000;
  --drawer-btn-bg-hover: #fff;
  --about-side-border: #80808099;
  --chip-color: #fff;
  --line-color: #80808099;
}

[data-theme="light"] {
  --body-bg-color: #fff;
  --chip-color: #000;
  --body-bg-colorblackwhite: #000;
  --text-color: #000;
  --text-colorwhite: #fff;
  --filterwhite: invert(100%);
  --filterblack: invert(0);
  --btn-top-bg: #000;
  --gray-span: #575757;
  --drawer-btn-bg: #fff;
  --drawer-btn-bg-hover: #000;
  --about-side-border: #00000026;
  --line-color: #00000026;
  --related-post-bg: #f7f7f7;
}

[data-theme="dark"] {
  --related-post-bg: #e1e1e10d;
}

.dark .nav-content {
  color: #000;
}

.dark .nav-content .brand img {
  filter: invert(0);
}

.dark .nav-content .menu-item {
  color: #000;
}

.HalfWidthBtn {
  align-items: center;
  justify-content: center;
  display: flex;
  width: 100%;
}

html, body {
  max-width: 100vw;
}

body {
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: var(--mobile-font-size--size-normal);
  font-weight: 400;
  line-height: 120%;
  background-color: var(--background);
  min-height: 100%;
  font-display: swap;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

.w-100, .w-full {
  width: 100% !important;
}

.w-50 {
  width: 50% !important;
}

.p-0 {
  padding: 0 !important;
}

.py-0 {
  padding-block: 0 !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pl-0 {
  padding-left: 0 !important;
}

.pr-0 {
  padding-right: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-2 {
  margin-bottom: .5rem !important;
}

.text-underline {
  text-decoration: underline !important;
}

.text-bold {
  font-weight: bold !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.flex-space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ShareBtn {
  display: flex;
  gap: 5px;
  align-items: center;
  cursor: pointer;
  transition: all .5s;
  line-height: 1;
}

.ShareBtn svg {
  font-size: 1rem;
}

.ShareBtn span {
  font-size: 1rem;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 400;
}

.ShareBtn:hover {
  opacity: .8;
}

#sideBtn_container {
  position: fixed;
  bottom: 20px;
  right: 10px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  gap: 20px;
  z-index: 998;
}

.back-to-top {
  opacity: 0;
  pointer-events: none;
  transition: all .3s;
}

.back-to-top.show {
  opacity: 1;
  pointer-events: auto;
}

.btn-top {
  position: relative;
  gap: 8px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 5px;
  font-size: 1.35rem;
  cursor: pointer;
  border: 1px solid;
  background-color: #0000;
  color: var(--btn-top-color, #fff);
  transition: all .3s;
}

.btn-top svg {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.btn-top {
  transition: all .3s;
  cursor: pointer;
}

.btn-top:hover {
  scale: .93;
}

.back-to-top.dark .btn-top {
  color: #fff;
  background-color: #0000;
}

.back-to-top.light .btn-top {
  color: #000;
  background-color: #fff;
}

.search_btn {
  transition: all .3s;
  cursor: pointer;
}

.search_btn:hover {
  scale: .93;
}

.search-btn {
  position: relative;
  gap: 8px;
  width: 40px;
  height: 40px;
  padding: 5px;
  font-size: 1.8rem;
  cursor: pointer;
  border: 1px solid;
  background-color: #0000;
  color: var(--text-color) !important;
}

.search-btn svg {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.search_btn.dark .search-btn {
  color: #fff;
  background-color: #0000;
}

.search_btn.light .search-btn {
  color: #fff;
  border: none;
}

.ad-flex-all {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ad-text:before {
  color: var(--gray-span);
  content: "ADVERTISEMENT";
  display: block;
  font-family: Arial, sans-serif;
  font-size: 9px;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: 1.2;
  margin: 5px auto;
  text-align: center;
  text-transform: uppercase;
  -webkit-font-smoothing: antialiased;
}

.chipContainer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin: 0 42px 40px;
}

@media only screen and (width >= 768px) {
  #sideBtn_container {
    bottom: 50px;
    right: 25px;
  }

  .ShareBtn svg {
    font-size: 1.25rem;
  }

  .ShareBtn span {
    font-size: 1.25rem;
  }
}

.headingTitle, .headingTitle h1 {
  display: inline;
  font-size: 3vw;
  line-height: 10vw;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
  letter-spacing: 1px;
}

.sponsoredTitle, .sponsoredTitle h1 {
  display: inline;
  font-size: 2.5vw;
  line-height: 10vw;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
  letter-spacing: 1px;
  color: #e02020;
  text-transform: uppercase;
}

html.w-mod-touch * {
  background-attachment: scroll !important;
}

.containerWrapper {
  width: 100%;
  max-width: 70rem;
  margin: 0 auto;
  padding: 2rem 0;
  padding-right: .625rem !important;
  padding-left: .625rem !important;
}

@media screen and (width <= 1200px) {
  .headingTitle, .headingTitle h1 {
    font-size: 4vw;
  }

  .sponsoredTitle, .sponsoredTitle h1 {
    font-size: 4vw;
  }
}

@media screen and (width <= 768px) {
  .headingTitle, .headingTitle h1 {
    font-size: 5vw;
  }

  .sponsoredTitle, .sponsoredTitle h1 {
    font-size: 5vw;
  }

  .chipContainer {
    margin: 10px 10px 25px;
  }
}

@media screen and (width <= 479px) {
  .headingTitle, .headingTitle h1 {
    font-size: 6.5vw;
  }

  .sponsoredTitle, .sponsoredTitle h1 {
    font-size: 6vw;
  }
}

@media only screen and (width >= 92.5rem) {
  .containerWrapper {
    max-width: 100rem;
    width: calc(100% - 21.875rem);
    padding-right: .625rem !important;
    padding-left: .625rem !important;
  }
}

.w-inline-block {
  max-width: 100%;
  display: inline-block;
}

.button_base {
  font-weight: 500;
  font-size: 17px;
  line-height: 20px;
  letter-spacing: 1px;
  display: block;
  text-align: center;
  text-transform: uppercase;
  font-family: sweet-sans-pro, sans-serif;
}

.view-more {
  padding: 1.125rem 0 .875rem;
}

.button_base.black {
  background-color: #323440;
  letter-spacing: 1.4px;
  color: var(--white);
  font-weight: 700;
  text-transform: uppercase;
  padding: 1.25rem 1.875rem;
  transition: all .5s;
  cursor: pointer;
}

.button_base.black:hover {
  background-color: #323440e6;
}

.drawer_footer .button_base.black {
  background-color: var(--drawer-btn-bg);
  color: var(--drawer-btn-bg-hover);
}

.drawer_footer .button_base.black:hover {
  background-color: var(--drawer-btn-bg-hover);
  color: var(--drawer-btn-bg);
}

.hasMore_btn_wrap {
  width: 100%;
  display: flex;
  justify-content: center;
}

.hasMore_btn_wrap .button_base {
  min-width: calc(50% - 3.25rem);
  border: none;
}

.sectioner--latest-stories .view-more-stories {
  width: 100%;
  margin-top: 2.5rem;
}

#home_wrappper {
  background-color: var(--body-bg-color);
}

@media only screen and (width <= 41.6875rem) {
  .sectioner--latest-stories .view-more-stories {
    margin-top: 0;
    margin-bottom: 0;
  }

  .button_base.black {
    width: 100%;
    display: inline-block;
    font-size: 17px;
    line-height: 10px;
    letter-spacing: 1px;
  }
}

@media only screen and (width <= 479px) {
  .button_base.black {
    justify-content: center;
    align-items: center;
    width: 100%;
    font-size: 4vw;
    display: flex;
    padding: 1.25rem .875rem;
  }
}

::-webkit-resizer {
  width: 0;
  height: 0;
}

.cont-link .ex-text {
  pointer-events: none;
}

.modes_helpers {
  height: calc(var(--vh, 1vh) * 100);
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  visibility: hidden;
  bottom: auto;
  width: 100%;
}

.modes_helpers i {
  transform: scaleY(0);
  position: fixed;
  width: 100%;
  height: 100%;
  visibility: visible;
}

.modes_helpers .a {
  background-color: #000;
}

.modes_helpers .b {
  background-color: #fff;
}

.modes_helpers.reverse .a {
  background-color: #000;
}

.modes_helpers.reverse .b {
  background: #fff;
}

.TagWrapper {
  color: var(--text-color);
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
}

.TagWrapper .featured-category__story .entry__heading {
  color: var(--text-color);
  transition: color .45s ease-in .5s;
}

.result-loader-div {
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-cont {
  position: fixed;
  z-index: 999;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--body-bg-color);
}

.MuiLinearProgress-root {
  position: relative;
  overflow: hidden;
  display: block;
  z-index: 0;
  background-color: #dcdcdc !important;
  height: 2px !important;
}

.MuiBox-root {
  width: 15% !important;
}

.MuiLinearProgress-bar {
  background-color: var(--text-colorblack) !important;
}

.MuiLinearProgress-bar1 {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  -webkit-transition: -webkit-transform .2s linear;
  transition: transform .2s linear;
  transform-origin: 0;
  background-color: var(--text-colorblack);
  width: auto;
  -webkit-animation: 2.1s cubic-bezier(.65, .815, .735, .395) infinite animation-1;
  animation: 2.1s cubic-bezier(.65, .815, .735, .395) infinite animation-1;
}

.MuiLinearProgress-bar2 {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  -webkit-transition: -webkit-transform .2s linear;
  transition: transform .2s linear;
  transform-origin: 0;
  --LinearProgressBar2-barColor: var(--text-colorblack);
  background-color: var(--LinearProgressBar2-barColor, currentColor);
  width: auto;
  -webkit-animation: 2.1s cubic-bezier(.165, .84, .44, 1) 1.15s infinite animation-2;
  animation: 2.1s cubic-bezier(.165, .84, .44, 1) 1.15s infinite animation-2;
}

@keyframes animation-1 {
  0% {
    left: -35%;
    right: 100%;
  }

  60% {
    left: 100%;
    right: -90%;
  }

  100% {
    left: 100%;
    right: -90%;
  }
}

@keyframes animation-2 {
  0% {
    left: -200%;
    right: 100%;
  }

  60% {
    left: 107%;
    right: -8%;
  }

  100% {
    left: 107%;
    right: -8%;
  }
}

/*# sourceMappingURL=src_styles_globals_73511378.css.map*/