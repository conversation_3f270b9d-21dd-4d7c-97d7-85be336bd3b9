{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/threecardsection.css"], "sourcesContent": [".editor-picks__secondary-wrapper {\r\n  padding: 4rem 0 0;\r\n}\r\n\r\n.editor-picks__secondary-pick {\r\n  text-align: center;\r\n  margin-bottom: 1.25rem;\r\n  margin-top: 1.5625rem;\r\n}\r\n.editor-picks__secondary-wrapper .entry__heading {\r\n  color: var(--text-color);\r\n  transition: color 0.1s ease-in 0.5s;\r\n}\r\n.editor-picks__secondary-wrapper .featured-image:before {\r\n  display: block;\r\n  content: \" \";\r\n  width: 100%;\r\n  padding-top: 56.25%;\r\n}\r\n.editor-picks__secondary-wrapper .featured-image .image-wrapper {\r\n  overflow: hidden;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n.editor-picks__secondary-wrapper .editor-picks__secondary-pick .featured-image {\r\n  max-height: 300px !important;\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  .editor-picks__secondary-wrapper {\r\n    padding: 0 0;\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick a {\r\n    display: flex;\r\n    border-top: 1px solid #e8e8e8;\r\n    padding-top: 25px;\r\n    box-sizing: border-box;\r\n    margin: 10px;\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {\r\n    text-align: start;\r\n    margin-left: 10px;\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .editor-picks__secondary-pick .entry .entry__category {\r\n    font-size: 11px;\r\n    order: 1;\r\n    margin-bottom: 5px;\r\n  }\r\n  .editor-picks__secondary-pick .entry .entry__heading {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n    order: 2;\r\n    margin-bottom: 5px;\r\n  }\r\n  .editor-picks__secondary-pick .entry .post-meta {\r\n    justify-content: flex-end;\r\n    order: 3;\r\n  }\r\n  .editor-picks__secondary-pick .entry .post-meta .post-meta__author {\r\n    font-size: 12px;\r\n  }\r\n  .editor-picks__secondary-pick .entry .post-meta .post-meta__timestamp {\r\n    display: none;\r\n  }\r\n  .editor-picks__primary-pick .entry {\r\n    padding: 0.8rem 0.9375rem 0;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .editor-picks__primary-pick .entry .entry__category {\r\n    order: 1;\r\n  }\r\n  .editor-picks__primary-pick .entry .entry__heading {\r\n    order: 2;\r\n  }\r\n  .editor-picks__primary-pick .entry__excerpt {\r\n    display: none;\r\n  }\r\n  .editor-picks__primary-pick .entry .entry__excerpt {\r\n    order: 3;\r\n  }\r\n  .editor-picks__primary-pick .entry .post-meta {\r\n    order: 4;\r\n    padding-bottom: 0px;\r\n  }\r\n  .editor-picks__primary-pick .entry .post-meta .post-meta__author {\r\n    font-size: 12px;\r\n  }\r\n  .editor-picks__primary-pick .entry .post-meta .post-meta__timestamp {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media only screen and (max-width: 61.1875rem) {\r\n  .editor-picks__secondary-wrapper\r\n    .editor-picks__secondary-pick\r\n    .featured-image {\r\n    max-width: 100%;\r\n    min-width: 35%;\r\n    overflow: hidden;\r\n    position: relative;\r\n  }\r\n  /* .editor-picks__secondary-pick .featured-image img {\r\n    display: block;\r\n    position: absolute;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    height: 100%;\r\n    width: auto;\r\n    max-width: 200%;\r\n  } */\r\n}\r\n@media only screen and (max-width: 450px) {\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {\r\n    margin-left: 15px;\r\n  }\r\n}\r\n@media only screen and (min-width: 41.75rem) {\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-inner-wrapper {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n  }\r\n  .editor-picks__secondary-pick {\r\n    width: calc(33.33333% - 0.83333rem);\r\n    margin-bottom: 0;\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick {\r\n    width: calc(33.33333% - 0.83333rem);\r\n  }\r\n  .editor-picks__secondary-wrapper .editor-picks__secondary-pick {\r\n    margin-bottom: 1.5625rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;AAQA;;;;AAGA;EACE;;;;EAGA;;;;;;;;EAOA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;;EAMA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;AAKF;EACE;;;;;;;;AAkBF;EACE;;;;;AAIF;EACE;;;;;;EAKA;;;;;EAIA;;;;EAGA"}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}