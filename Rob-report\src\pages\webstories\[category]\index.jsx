import React from "react";
import Link from "next/link";
import Se<PERSON><PERSON>eader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";

// DUMMY DATA - TODO: Remove when API is ready
const DUMMY_WEB_STORIES = [
  {
    slug: "luxury-cars-2024",
    title: "Top 5 Luxury Cars of 2024",
    coverImg: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=400&h=600&fit=crop",
    altName: "Luxury Cars 2024",
    timestamp: "2024-01-15T10:00:00Z",
    excerpt: "Discover the most extraordinary luxury automobiles that define excellence in 2024."
  },
  {
    slug: "luxury-watches-2024",
    title: "Exquisite Timepieces of 2024",
    coverImg: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=600&fit=crop",
    altName: "Luxury Watches 2024",
    timestamp: "2024-01-20T14:00:00Z",
    excerpt: "Explore the finest horological masterpieces from the world's most prestigious watchmakers."
  },
  {
    slug: "luxury-yachts-2024",
    title: "Magnificent Yachts of 2024",
    coverImg: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=600&fit=crop",
    altName: "Luxury Yachts 2024",
    timestamp: "2024-01-25T16:00:00Z",
    excerpt: "Journey through the world's most spectacular superyachts and sailing vessels."
  },
  {
    slug: "luxury-real-estate-2024",
    title: "Extraordinary Properties of 2024",
    coverImg: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=600&fit=crop",
    altName: "Luxury Real Estate 2024",
    timestamp: "2024-02-01T12:00:00Z",
    excerpt: "Step inside the most exclusive and breathtaking properties around the globe."
  },
  {
    slug: "luxury-jets-2024",
    title: "Private Jets Redefining Luxury",
    coverImg: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=400&h=600&fit=crop",
    altName: "Luxury Private Jets 2024",
    timestamp: "2024-02-05T18:00:00Z",
    excerpt: "Experience the pinnacle of private aviation with these extraordinary aircraft."
  }
];

const WebStoriesCategory = ({ stories, meta, breadcrumbs, category }) => {
  const categoryTitle = category === 'all' ? 'All Web Stories' : category.charAt(0).toUpperCase() + category.slice(1);

  return (
    <div className="min-h-screen bg-gray-50">
      <SeoHeader meta={meta} />
      <BreadcrumbSchema itemList={breadcrumbs} />
      
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">{categoryTitle}</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Immersive visual stories showcasing the world of luxury lifestyle, 
            from exotic cars to exclusive properties.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stories.map((story) => (
            <Link 
              key={story.slug} 
              href={`/webstories/${category}/${story.slug}`}
              className="group block bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <div className="aspect-[3/4] relative overflow-hidden">
                <img
                  src={story.coverImg}
                  alt={story.altName}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <h2 className="text-xl font-bold mb-2 line-clamp-2">
                    {story.title}
                  </h2>
                  <p className="text-sm opacity-90 line-clamp-2">
                    {story.excerpt}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-500">
            More web stories coming soon...
          </p>
        </div>
      </div>

      <style jsx>{`
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default WebStoriesCategory;

export async function getServerSideProps({ params }) {
  const { category } = params;

  // TODO: Replace with actual API call when ready
  // try {
  //   const storiesRes = await getWebStoriesCategory({
  //     slug: `/webstories/${category}`,
  //     limit: 20,
  //     offset: 0
  //   });
  //   return {
  //     props: {
  //       stories: storiesRes?.data || [],
  //       category: category,
  //       meta: {
  //         title: `${category.charAt(0).toUpperCase() + category.slice(1)} Web Stories | Robb Report India`,
  //         description: `Immersive visual stories in ${category} category showcasing luxury lifestyle.`,
  //         keywords: ["web stories", category, "luxury", "lifestyle", "robb report"],
  //         robots: "index,follow"
  //       },
  //       breadcrumbs: [
  //         { name: "Web Stories", slug: "/webstories" },
  //         { name: category.charAt(0).toUpperCase() + category.slice(1), slug: `/webstories/${category}` }
  //       ]
  //     }
  //   };
  // } catch (error) {
  //   console.error("Error fetching web stories:", error.message);
  //   return { notFound: true };
  // }

  // DUMMY DATA IMPLEMENTATION - Remove when API is ready
  const categoryTitle = category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1);
  
  return {
    props: {
      stories: DUMMY_WEB_STORIES,
      category: category,
      meta: {
        title: `${categoryTitle} Web Stories | Robb Report India`,
        description: `Immersive visual stories in ${category} category showcasing luxury lifestyle.`,
        keywords: ["web stories", category, "luxury", "lifestyle", "robb report"],
        robots: "index,follow"
      },
      breadcrumbs: [
        { name: "Web Stories", slug: "/webstories" },
        { name: categoryTitle, slug: `/webstories/${category}` }
      ]
    }
  };
}
