self.__BUILD_MANIFEST = {"__rewrites":{"afterFiles":[{"source":"/sitemap_index.xml","destination":"/api/sitemaps/sitemap_index"},{"source":"/post-sitemap.xml","destination":"/api/sitemaps/sitemap_post"},{"source":"/tag-sitemap.xml","destination":"/api/sitemaps/sitemap_tag"},{"source":"/category-sitemap.xml","destination":"/api/sitemaps/sitemap_category"},{"source":"/author-sitemap.xml","destination":"/api/sitemaps/sitemap_author"},{"source":"/page-sitemap.xml","destination":"/api/sitemaps/sitemap_page"},{"source":"/google-news-sitemap.xml","destination":"/api/sitemaps/sitemap_news"},{"source":"/gallery-sitemap.xml","destination":"/api/sitemaps/sitemap_gallery"}],"beforeFiles":[],"fallback":[]},"/":["static/chunks/pages/index.js"],"/404":["static/chunks/pages/404.js"],"/500":["static/chunks/pages/500.js"],"/_app":["static/chunks/pages/_app.js"],"/_error":["static/chunks/pages/_error.js"],"/about-us":["static/chunks/pages/about-us.js"],"/api/ArticleApi":["static/chunks/pages/api/ArticleApi.js"],"/api/AuthorApi":["static/chunks/pages/api/AuthorApi.js"],"/api/CategoryApi":["static/chunks/pages/api/CategoryApi.js"],"/api/Headers":["static/chunks/pages/api/Headers.js"],"/api/HomeApi":["static/chunks/pages/api/HomeApi.js"],"/api/ResultApi":["static/chunks/pages/api/ResultApi.js"],"/api/TagApi":["static/chunks/pages/api/TagApi.js"],"/api/VideoApi":["static/chunks/pages/api/VideoApi.js"],"/api/WebStoriesApi":["static/chunks/pages/api/WebStoriesApi.js"],"/api/sitemaps/sitemap_author":["static/chunks/pages/api/sitemaps/sitemap_author.js"],"/api/sitemaps/sitemap_category":["static/chunks/pages/api/sitemaps/sitemap_category.js"],"/api/sitemaps/sitemap_gallery":["static/chunks/pages/api/sitemaps/sitemap_gallery.js"],"/api/sitemaps/sitemap_index":["static/chunks/pages/api/sitemaps/sitemap_index.js"],"/api/sitemaps/sitemap_news":["static/chunks/pages/api/sitemaps/sitemap_news.js"],"/api/sitemaps/sitemap_page":["static/chunks/pages/api/sitemaps/sitemap_page.js"],"/api/sitemaps/sitemap_post":["static/chunks/pages/api/sitemaps/sitemap_post.js"],"/api/sitemaps/sitemap_tag":["static/chunks/pages/api/sitemaps/sitemap_tag.js"],"/api/sitemaps/sitemap_vcategory":["static/chunks/pages/api/sitemaps/sitemap_vcategory.js"],"/api/sitemaps/sitemap_vpost":["static/chunks/pages/api/sitemaps/sitemap_vpost.js"],"/author/[slug]":["static/chunks/pages/author/[slug].js"],"/contact-us":["static/chunks/pages/contact-us.js"],"/disclaimer":["static/chunks/pages/disclaimer.js"],"/privacy-policy":["static/chunks/pages/privacy-policy.js"],"/result":["static/chunks/pages/result.js"],"/result/[...slug]":["static/chunks/pages/result/[...slug].js"],"/stories/[slug]":["static/chunks/pages/stories/[slug].js"],"/tag/[slug]":["static/chunks/pages/tag/[slug].js"],"/terms-of-use":["static/chunks/pages/terms-of-use.js"],"/webstories":["static/chunks/pages/webstories.js"],"/webstories/[category]":["static/chunks/pages/webstories/[category].js"],"/webstories/[category]/[slug]":["static/chunks/pages/webstories/[category]/[slug].js"],"/[category]":["static/chunks/pages/[category].js"],"/[category]/[subcategory]":["static/chunks/pages/[category]/[subcategory].js"],"/[category]/[subcategory]/[stories]":["static/chunks/pages/[category]/[subcategory]/[stories].js"],"sortedPages":["/","/404","/500","/_app","/_error","/about-us","/api/ArticleApi","/api/AuthorApi","/api/CategoryApi","/api/Headers","/api/HomeApi","/api/ResultApi","/api/TagApi","/api/VideoApi","/api/WebStoriesApi","/api/sitemaps/sitemap_author","/api/sitemaps/sitemap_category","/api/sitemaps/sitemap_gallery","/api/sitemaps/sitemap_index","/api/sitemaps/sitemap_news","/api/sitemaps/sitemap_page","/api/sitemaps/sitemap_post","/api/sitemaps/sitemap_tag","/api/sitemaps/sitemap_vcategory","/api/sitemaps/sitemap_vpost","/author/[slug]","/contact-us","/disclaimer","/privacy-policy","/result","/result/[...slug]","/stories/[slug]","/tag/[slug]","/terms-of-use","/webstories","/webstories/[category]","/webstories/[category]/[slug]","/[category]","/[category]/[subcategory]","/[category]/[subcategory]/[stories]"]};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()