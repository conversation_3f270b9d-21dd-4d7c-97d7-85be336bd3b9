/* [project]/src/styles/footer.css [client] (css) */
.footer {
  background-color: var(--black);
}

.wrapper_footer {
  padding-top: 0;
  padding-bottom: 2.5rem;
}

.dest_news {
  border-bottom: 1px solid #f1f1f126;
  width: 100%;
  display: flex;
}

.dest_ft {
  border-right: 1px solid #f1f1f126;
  width: 50%;
  height: 100%;
}

.btn_wrapper {
  border-top: 1px solid #f1f1f126;
  padding: 16px 32px;
  border-bottom: 1px solid #f1f1f126;
}

.btn_wrapper.expobtn {
  padding: 16px 32px 16px 0;
}

.btn_wrapper.subbtn {
  padding: 16px 0 16px 16px;
}

.news_ft {
  width: 50%;
  height: 100%;
}

.dt_nw_wrapper {
  flex-flow: column;
  width: 100%;
  height: 100%;
  display: flex;
}

.footer_button {
  background-color: var(--white);
  color: var(--black);
  letter-spacing: 1px;
  border-radius: 2px;
  justify-content: center;
  align-items: center;
  width: auto;
  height: 52px;
  margin-left: auto;
  padding: 8px 62px;
  transition: all .4s;
  display: flex;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
  font-size: 1rem;
}

.w-form {
  margin: 0 0 15px;
}

.form-block {
  margin-bottom: 0;
}

.subscribe_flex {
  justify-content: space-around;
  display: flex;
}

.w-form-done {
  text-align: center;
  background-color: #ddd;
  padding: 20px;
  display: none;
}

.subscribe_thanks {
  background-color: var(--white);
  border-radius: 2px;
}

.w-form-fail {
  background-color: #ffdede;
  margin-top: 10px;
  padding: 10px;
  display: none;
}

.error_state {
  letter-spacing: 1px;
  background-color: #fc5656;
  border-radius: 3px;
  width: 80%;
  margin-top: 12px;
  margin-left: 0;
  margin-right: 0;
  padding: 7px 12px 6px;
  font-size: .9em;
  line-height: 1.2;
  position: static;
}

.w-input, .w-select {
  color: #333;
  vertical-align: middle;
  background-color: #fff;
  border: 1px solid #ccc;
  width: 100%;
  height: 38px;
  margin-bottom: 10px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.42857;
  display: block;
}

.field_prefooter {
  color: var(--white);
  letter-spacing: .03em;
  background-color: #fff0;
  border: 1px solid #f1f1f140;
  border-radius: 3px;
  width: 100%;
  height: 52px;
  margin-bottom: 0;
  margin-right: 8px;
  padding: 8px 16px 4px;
  transition: all .4s;
}

input.w-button {
  -webkit-appearance: button;
  cursor: pointer;
}

.footer_links {
  padding-top: 0;
}

.wrapper_footer_links {
  padding-top: 32px;
}

.flexbox_footer {
  display: flex;
  font-family: sweet-sans-pro, sans-serif;
}

.footer-left-block {
  width: 50%;
}

.footer-right-block {
  width: 50%;
  display: flex;
  justify-content: space-between;
}

.div-block-2 {
  width: 100%;
}

.title_footer {
  opacity: .65;
  color: var(--white);
  text-transform: uppercase;
  margin-bottom: 16px;
  font-size: .9em;
  letter-spacing: 1px;
}

.links_flex {
  grid-column-gap: 4px;
  grid-row-gap: 4px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.footer_link {
  color: var(--white);
  letter-spacing: 1px;
  font-size: .9em;
  transition: all .4s;
}

.last_line {
  margin-top: 8em;
}

.flexbox_line {
  display: flex;
  justify-content: space-between;
  font-family: sweet-sans-pro, sans-serif;
}

.left_rights {
  width: 30%;
  margin-right: 0;
}

.footer_link:hover, .footer_link.just_rights {
  opacity: .65;
}

.privacy_box {
  width: 25%;
  margin-right: 0;
  display: flex;
  grid-column-gap: 15px;
  grid-row-gap: 15px;
  justify-content: flex-end;
}

.website_by {
  justify-content: flex-end;
  align-items: flex-start;
  width: 25%;
  margin-left: auto;
  display: flex;
}

.footer_link.web_by {
  opacity: .65;
}

.footer-brand {
  position: relative;
  width: 280px;
  height: 55px;
  overflow: hidden;
}

.footer-brand img {
  object-fit: cover;
  filter: invert();
  transition: filter .45s ease-in .5s;
}

.sub-footer {
  background-color: var(--gray-bg);
}

.sub-footer > .containerWrapper {
  padding: 10px 0;
}

.flexbox_sub-footer {
  display: flex;
  align-items: center;
  font-family: sweet-sans-pro, sans-serif;
}

.footer-tag-line {
  color: var(--white);
  letter-spacing: 1px;
  font-size: .8em;
  transition: all .4s;
}

.footer-vr-line {
  display: inline-block;
  align-self: stretch;
  background: #fff;
  width: 1px;
  height: 100%;
  opacity: .7;
  margin: auto 15px;
  min-height: 2.75em;
}

.sub-footer-img-block {
  position: relative;
  width: 180px;
  min-width: 180px;
  height: 5vw;
  overflow: hidden;
}

.sub-footer-img-block-2 {
  position: relative;
  width: 80px;
  min-width: 80px;
  height: 5vw;
  overflow: hidden;
}

.sub-footer-img-block img, .sub-footer-img-block-2 img {
  object-fit: contain;
}

@media screen and (width <= 991px) {
  .btn_wrapper {
    padding-left: 24px;
    padding-right: 24px;
  }

  .footer_button {
    padding-left: 32px;
    padding-right: 32px;
    font-size: 1em;
  }

  .field_prefooter {
    font-size: 1em;
  }

  .subscribe_thanks {
    border-radius: 2px;
  }

  .error_state {
    padding-top: 10px;
    font-size: 1.5em;
  }

  .thanks_txt {
    font-size: 1.5em;
    line-height: 1.1;
  }

  .wrapper_footer_links {
    padding-top: 32px;
    padding-left: 0;
    padding-right: 0;
  }

  .title_footer {
    letter-spacing: 1px;
    font-size: .9em;
  }

  .footer_link {
    font-size: 1em;
  }

  .last_line {
    margin-top: 25em;
  }

  .privacy_box {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    width: 50%;
    display: flex;
  }

  .website_by {
    width: 20%;
  }
}

@media screen and (width <= 767px) {
  .wrapper_footer {
    padding-bottom: 20px;
  }

  .dest_news {
    flex-flow: column;
    height: auto;
  }

  .dest_ft, .news_ft {
    width: 100%;
  }

  .btn_wrapper {
    padding-left: 20px;
    padding-right: 20px;
  }

  .footer_button {
    font-size: .9em;
  }

  .field_prefooter {
    font-size: .8em;
  }

  .subscribe_thanks {
    border-radius: 2px;
  }

  .error_state {
    font-size: 2em;
  }

  .thanks_txt {
    font-size: 1em;
  }

  .wrapper_footer_links {
    padding-top: 32px;
    padding-left: 0;
    padding-right: 0;
  }

  .flexbox_footer {
    flex-direction: column;
  }

  .footer-left-block {
    width: 100%;
    margin-bottom: 30px;
  }

  .footer-right-block {
    width: 100%;
    display: grid;
    justify-content: flex-start;
    grid-column-gap: 0px;
    grid-row-gap: 52px;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
  }

  .sub-footer-img-block, .sub-footer-img-block-2 {
    width: 100%;
  }

  .footer-hr-line {
    display: inline-block;
    align-self: stretch;
    background: #fff;
    width: 100%;
    height: 1px;
    opacity: .7;
    margin: 15px auto;
  }

  .flexbox_sub-footer {
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
  }

  .sub-footer-img-block {
    order: 1;
    width: calc(50% - 5.5px);
    min-width: calc(50% - 5.5px);
    height: 50px;
  }

  .footer-vr-line:first-of-type {
    order: 2;
    height: 100%;
    width: 1px;
    margin: 0;
  }

  .sub-footer-img-block-2 {
    order: 3;
    width: calc(50% - 5.5px);
    min-width: calc(50% - 5.5px);
    height: 50px;
  }

  .footer-vr-line:last-of-type {
    order: 4;
    width: 100%;
    height: 1px;
    min-height: 1px;
    margin: 15px auto;
  }

  .footer-tag-line {
    order: 5;
    width: 100%;
  }

  .title_footer {
    letter-spacing: 1px;
    font-size: 1em;
  }

  .links_flex {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
  }

  .footer_link {
    letter-spacing: 1px;
    padding-top: .2em;
    font-size: 1em;
  }

  .last_line {
    margin-top: 20em;
  }

  .flexbox_line {
    flex-flow: column;
  }

  .privacy_box {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    width: 100%;
    margin-top: 8px;
    display: flex;
    justify-content: flex-start;
  }

  .website_by {
    justify-content: flex-start;
    align-items: flex-start;
    width: 50%;
    margin-top: 32px;
    margin-left: 0;
  }

  .footer_link.web_by {
    padding-top: .2em;
  }
}

@media screen and (width <= 479px) {
  .footer-brand {
    position: relative;
    width: 180px;
    height: 40px;
    overflow: hidden;
  }

  .div-block-3 {
    width: 100%;
  }

  .left_rights {
    width: 100%;
  }

  .wrapper_footer_links {
    padding-top: 32px;
    padding-left: 0;
    padding-right: 0;
  }

  .flexbox_footer {
    flex-direction: column;
  }

  .footer-left-block {
    width: 100%;
    margin-bottom: 30px;
  }

  .footer-right-block {
    width: 100%;
    display: grid;
    justify-content: flex-start;
    grid-column-gap: 20px;
    grid-row-gap: 32px;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
  }

  .sub-footer-img-block, .sub-footer-img-block-2 {
    height: 40px;
  }

  .last_line {
    margin-top: 1em;
  }

  .website_by {
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    margin-top: 52px;
    margin-left: 0;
  }

  .footer_link.web_by {
    text-decoration: underline;
  }

  .btn_wrapper.subbtn {
    padding: 16px 0;
  }

  .btn_wrapper.expobtn {
    padding: 16px 0;
  }

  .title_footer {
    font-size: .9em;
  }

  .footer_link {
    font-size: .9em;
  }
}

/*# sourceMappingURL=src_styles_footer_73511378.css.map*/