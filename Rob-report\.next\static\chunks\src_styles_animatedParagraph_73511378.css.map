{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/animatedParagraph.css"], "sourcesContent": [".wrapper_base {\r\n  padding: 152px 0;\r\n  font-size: 1vw;\r\n}\r\n.wrapper_base.specialalign {\r\n  display: flex;\r\n  /* align-items: center; */\r\n  justify-content: center;\r\n  flex-direction: column;\r\n}\r\n.split_box {\r\n  width: 60%;\r\n  display: block;\r\n  font-family: rocky, sans-serif;\r\n}\r\n.wrapper_base.specialalign .split_box {\r\n  width: 60%;\r\n}\r\n.m_txt {\r\n  /* font-size: 3.1em; */\r\n  font-size: 2.5em;\r\n  line-height: 1.05;\r\n}\r\n.line {\r\n  position: relative;\r\n}\r\n.line-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  background-color: #fff;\r\n  /* background-color: red; */\r\n  /* opacity: 0.5; */\r\n  height: 100%;\r\n  /* width: 100%; */\r\n  z-index: 1;\r\n}\r\n.parabtn {\r\n  position: relative;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  margin-top: 24px;\r\n}\r\n@media screen and (max-width: 991px) {\r\n  .wrapper_base {\r\n    padding: 112px 0;\r\n  }\r\n  .split_box,\r\n  .split_box:lang(de-de) {\r\n    width: 66%;\r\n  }\r\n  .wrapper_base.speciallign .split_box {\r\n    width: 80%;\r\n  }\r\n  .m_txt.split-lines {\r\n    font-size: 4em;\r\n  }\r\n}\r\n@media screen and (max-width: 767px) {\r\n  .wrapper_base {\r\n    padding: 72px 0;\r\n  }\r\n  .split_box {\r\n    width: 76%;\r\n  }\r\n  .wrapper_base.specialalign .split_box {\r\n    width: 76%;\r\n  }\r\n  .m_txt.split-lines {\r\n    font-size: 4.4em;\r\n  }\r\n}\r\n@media screen and (max-width: 479px) {\r\n  .wrapper_base {\r\n    padding: 100px 16px;\r\n  }\r\n  .split_box {\r\n    width: 100%;\r\n  }\r\n  .wrapper_base.specialalign .split_box {\r\n    width: 100%;\r\n  }\r\n  .m_txt.split-lines {\r\n    font-size: 5.9em;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;AAIA;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;;AAKA;;;;AAGA;;;;;;;;;AAWA;;;;;;;AAMA;EACE;;;;EAGA;;;;EAIA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;EAGA"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}