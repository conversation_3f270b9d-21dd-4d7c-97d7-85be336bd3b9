{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/author.css"], "sourcesContent": ["#author_header {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  /* background-color: #fff; */\r\n  padding: 30px 0 40px;\r\n}\r\n\r\n#profile_author {\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n}\r\n#profile_author img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  object-position: center;\r\n}\r\n\r\n#author_header h1 {\r\n  line-height: 6vw;\r\n  color: var(--text-color);\r\n}\r\n#author_header h4 {\r\n  font-weight: 500;\r\n  font-size: 1.5rem;\r\n  font-family: rocky, sans-serif;\r\n  margin-bottom: 20px;\r\n  margin-top: 10px;\r\n  color: var(--text-color);\r\n}\r\n\r\n#author-icons {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  font-size: 20px;\r\n}\r\n\r\n#author_header p {\r\n  font-size: 18px;\r\n  line-height: 28px;\r\n  letter-spacing: 0.37px;\r\n  font-family: Georgia, sans-serif;\r\n  color: var(--text-color);\r\n}\r\n\r\n\r\n@media (max-width:768px) {\r\n  #author_header h4{\r\n    margin-top: 18px;\r\n  }  \r\n  #author_header h1 {\r\n    margin-top: 8px;\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;;AAUA;;;;;;;AAMA;;;;;;;AAOA;;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;AASA;EACE;;;;EAGA"}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}