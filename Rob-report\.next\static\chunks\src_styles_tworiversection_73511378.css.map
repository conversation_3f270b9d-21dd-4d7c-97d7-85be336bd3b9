{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/tworiversection.css"], "sourcesContent": [".two-rivers-wrapper {\r\n  font-size: 1.15rem;\r\n}\r\n.two-rivers-wrapper .sectioner {\r\n  margin-bottom: 0;\r\n}\r\n.sectioner--latest-stories {\r\n  width: 100%;\r\n  padding: 75px 0;\r\nbackground-color: var(--body-bg-color);\r\n}\r\n.sectioner--latest-stories .entry {\r\n  margin-left: 10px;\r\n}\r\n.latest-story.latest-story--primary .entry {\r\n  margin-left: 0px;\r\n}\r\n.sectioner--latest-stories .entry__category {\r\n  font-size: 11px;\r\n  line-height: 15px;\r\n}\r\n.latest-story--primary {\r\n  flex-direction: column;\r\n}\r\n.sectioner--latest-stories .latest-story {\r\n  margin-bottom: 1.875rem;\r\n  display: flex;\r\n  /* justify-content: space-between; */\r\n  text-align: left;\r\n  border-bottom: 0.0625rem solid #dddee4;\r\n  padding-bottom: 1.25rem;\r\n}\r\n.sectioner--latest-stories .latest-story:last-of-type {\r\n  margin-bottom: 0;\r\n  border-bottom: none;\r\n  padding-bottom: 0;\r\n}\r\n.sectioner--latest-stories .latest-story--primary {\r\n  border-bottom: 0.0625rem solid #dddee4;\r\n  padding-bottom: 1.25rem;\r\n  text-align: center;\r\n}\r\n.sectioner--latest-stories .latest-story--primary .entry__excerpt {\r\n  font-size: 19px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 26px;\r\n  letter-spacing: normal;\r\n  font-weight: 400;\r\n  margin-bottom: 0.75rem;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n  color: var(--text-color);\r\n}\r\n\r\n.latest-story--primary .entry {\r\n  align-items: center;\r\n  text-align: center;\r\n  min-height: 160px;\r\n}\r\n.latest-story .entry__heading {\r\n  font-family: rocky, sans-serif;\r\n  font-size: 29px;\r\n  line-height: 29px;\r\n  letter-spacing: normal;\r\n  color: var(--text-color);\r\n}\r\n.sectioner--latest-stories .latest-story--primary .entry__heading {\r\n  font-size: 26px;\r\n  line-height: 32px;\r\n  letter-spacing: normal;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\ncolor: var(--text-color);\r\n}\r\n.sectioner--latest-stories .section-header {\r\n  display: block;\r\n  border-bottom: none;\r\n  border-left: none;\r\n  text-align: center;\r\n  padding: 0;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.sectioner--latest-stories .section-header .section-header__heading {\r\n  font-size: 1.875rem;\r\n  line-height: 24px;\r\n  letter-spacing: 1px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n    color: var(--text-color);\r\n\r\n}\r\n.latest-story:not(.latest-story--primary) .featured-image {\r\n  display: inline-block;\r\n  height: auto;\r\n  max-width: 350px;\r\n  overflow: hidden;\r\n}\r\n.latest-story:not(.latest-story--primary) .featured-image a {\r\n  height: auto;\r\n}\r\n.latest-story--primary .featured-image {\r\n  margin-bottom: 0.9375rem;\r\n}\r\n/* .sectioner--latest-stories .latest-story .entry__heading {\r\n  margin-left: 10px;\r\n} */\r\n@media only screen and (min-width: 41.75rem) {\r\n  /* .two-rivers-wrapper .sectioner {\r\n    margin-bottom: 5rem;\r\n  } */\r\n  .latest-story .featured-image a {\r\n    width: calc(50% - 0.625rem);\r\n  }\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    display: inline-table;\r\n    max-width: 350px;\r\n    min-width: 50%;\r\n    overflow: hidden;\r\n  }\r\n  .latest-story {\r\n    display: flex;\r\n    /* justify-content: space-between; */\r\n    text-align: left;\r\n  }\r\n  .latest-story .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n  }\r\n  .latest-story .entry__heading {\r\n    font-size: 23px;\r\n  }\r\n}\r\n.latest-story--primary .entry,\r\n.latest-story--primary .featured-image {\r\n  width: 100%;\r\n}\r\n\r\n@media only screen and (min-width: 61.25rem) {\r\n  .two-rivers-wrapper {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    border-top: 0.5px solid #d4d4d4;\r\n  }\r\n  /* .sectioner--latest-stories {\r\n    width: calc(50% - 1.25rem);\r\n  } */\r\n  .sectioner--latest-stories {\r\n    padding: 75px 1.625rem;\r\n  }\r\n  .sectioner--latest-stories .latest-story--primary {\r\n    font-size: 2.25rem;\r\n    justify-content: flex-start;\r\n  }\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    display: inline-table;\r\n    max-width: 200px;\r\n    /* min-width: 15.125rem; */\r\n    min-width: 35%;\r\n    overflow: hidden;\r\n  }\r\n  .latest-story .entry__heading {\r\n    font-size: 20px;\r\n  }\r\n  .latest-story .entry__heading {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n  }\r\n}\r\n@media only screen and (min-width: 70.625rem) {\r\n  /* .sectioner--latest-stories {\r\n    max-width: 33.125rem;\r\n  } */\r\n  /* .sectioner--latest-stories .featured-image {\r\n    margin-bottom: 0;\r\n  } */\r\n  .sectioner--latest-stories {\r\n    padding: 75px 1.625rem;\r\n  }\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    display: inline-table;\r\n    max-width: 250px;\r\n    min-width: 35%;\r\n    /* min-width: 18.125rem; */\r\n\r\n    overflow: hidden;\r\n  }\r\n  .latest-story .entry__heading {\r\n    font-size: 22px;\r\n    line-height: 28px;\r\n  }\r\n  .two-rivers-wrapper .sectioner--latest-stories .featured-image {\r\n    position: relative;\r\n    margin-bottom: 1.5625rem;\r\n  }\r\n  .two-rivers-wrapper .sectioner--latest-stories .featured-image::before {\r\n    display: block;\r\n    content: \" \";\r\n    width: 100%;\r\n    padding-top: 56.25%;\r\n  }\r\n  .two-rivers-wrapper .sectioner--latest-stories .featured-image .image-wrapper {\r\n    display: block;\r\n    position: absolute;\r\n    inset: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: hidden;\r\n  }\r\n  .two-rivers-wrapper .entry__category {\r\n    font-size: 15px;\r\n    line-height: 22px;\r\n  }\r\n}\r\n@media only screen and (max-width: 41.6875rem) {\r\n  /* .two-rivers-wrapper .sectioner {\r\n    margin-bottom: 5rem;\r\n  } */\r\n  .sectioner--latest-stories .entry {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .sectioner--latest-stories .entry .entry__category {\r\n    order: 1;\r\n    margin-bottom: 5px;\r\n  }\r\n  .sectioner--latest-stories .entry .entry__heading {\r\n    order: 2;\r\n    margin-bottom: 5px;\r\n  }\r\n  .sectioner--latest-stories .entry .post-meta {\r\n    order: 3;\r\n  }\r\n  .sectioner--latest-stories .entry .post-meta .post-meta__author {\r\n    font-size: 12px;\r\n  }\r\n  .sectioner--latest-stories .entry .post-meta .post-meta__timestamp {\r\n    display: none;\r\n  }\r\n  .sectioner--latest-stories .latest-story .entry__heading {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n    /* margin-left: 10px; */\r\n  }\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    max-width: 400px;\r\n    min-width: 40%;\r\n  }\r\n  .sectioner--latest-stories {\r\n    /* padding: 0; */\r\n    /* padding-bottom: 5rem;\r\n    padding-top: 0; */\r\n    padding: 25px 0;\r\n    /* padding-left: 0;\r\n    padding-right: 0; */\r\n  }\r\n  .latest-story.latest-story--primary .entry {\r\n    min-height: auto;\r\n  }\r\n  .sectioner--latest-stories .latest-story--primary .entry__excerpt {\r\n    display: none;\r\n  }\r\n  .sectioner--latest-stories .section-header .section-header__heading {\r\n    font-size: 1.375rem;\r\n    letter-spacing: 1px;\r\n  }\r\n}\r\n@media only screen and (width <= 375px) {\r\n  .latest-story:not(.latest-story--primary) .featured-image {\r\n    max-height: 90px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;AAQA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAMA;;;;AAGA;;;;AAMA;EAIE;;;;EAGA;;;;;;;EAMA;;;;;EAKA;;;;;;EAKA;;;;;AAIF;;;;AAKA;EACE;;;;;;EAQA;;;;EAGA;;;;;EAIA;;;;;;;EAOA;;;;EAGA;;;;;;AAKF;EAOE;;;;EAGA;;;;;;;EAQA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;;;EAQA;;;;;;AAKF;EAIE;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAKA;;;;;EAIA;;;;EAQA;;;;EAGA;;;;EAGA;;;;;;AAKF;EACE"}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}