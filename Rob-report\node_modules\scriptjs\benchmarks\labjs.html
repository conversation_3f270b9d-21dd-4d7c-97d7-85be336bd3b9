<!DOCTYPE HTML>
<html lang="en-us">
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title>LAB2.js - benchmarks</title>
    <style type="text/css">
      body {
        width: 650px;
        margin: 10px auto;
        font: 300 16px 'helvetica neue', helvetica, arial;
      }
      h1 {
        border-bottom: 3px solid #333;
      }

      strong {
        font-weight: bold;
      }
      #window-ms,
      #dom-ms {
        font-weight: bold;
        font-size: 20px;
      }
    </style>
    <script src="../vendor/lab2.min.js"></script>
    <script type="text/javascript">
      var start = (+new Date),
          onloadTime = 0,
          libsReady = 0;
      window.onload = function() {
        document.getElementById('window-ms').innerHTML = (+new Date) - start;
      };

      $LAB
      // load a bunch of libraries
      .script('../vendor/yui-utilities.js?' + start)
      .script('../vendor/jquery.js?' + start)
      .script('../vendor/mootools.js?' + start).wait(function () {
        YAHOO.util.Dom.get('yui').innerHTML = '✓';
        $('#jquery').html('✓');
        document.id('mootools').innerHTML = '✓';
        document.getElementById('total').innerHTML = (+new Date) - start;
      })
    </script>
  </head>
  <body>
    <h1>Benchmark Test</h1>
    <div id="ready">
      <img src="../vendor/bird.png">
      <p>window.onload time: <span id="window-ms">0</span>ms</p>
      <ul>
        <li>YUI: <span id="yui"></span></li>
        <li>jQuery: <span id="jquery"></span></li>
        <li>Mootools: <span id="mootools"></span></li>
        <li>Total: <strong id="total">0</strong>ms using $LAB2.js</li>
      </ul>
    </div>
  </body>
</html>
