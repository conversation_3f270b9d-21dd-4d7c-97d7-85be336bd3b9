/* [project]/src/styles/animatedParagraph.css [client] (css) */
.wrapper_base {
  padding: 152px 0;
  font-size: 1vw;
}

.wrapper_base.specialalign {
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.split_box {
  width: 60%;
  display: block;
  font-family: rocky, sans-serif;
}

.wrapper_base.specialalign .split_box {
  width: 60%;
}

.m_txt {
  font-size: 2.5em;
  line-height: 1.05;
}

.line {
  position: relative;
}

.line-mask {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #fff;
  height: 100%;
  z-index: 1;
}

.parabtn {
  position: relative;
  display: flex;
  justify-content: flex-start;
  margin-top: 24px;
}

@media screen and (width <= 991px) {
  .wrapper_base {
    padding: 112px 0;
  }

  .split_box, .split_box:lang(de-de) {
    width: 66%;
  }

  .wrapper_base.speciallign .split_box {
    width: 80%;
  }

  .m_txt.split-lines {
    font-size: 4em;
  }
}

@media screen and (width <= 767px) {
  .wrapper_base {
    padding: 72px 0;
  }

  .split_box {
    width: 76%;
  }

  .wrapper_base.specialalign .split_box {
    width: 76%;
  }

  .m_txt.split-lines {
    font-size: 4.4em;
  }
}

@media screen and (width <= 479px) {
  .wrapper_base {
    padding: 100px 16px;
  }

  .split_box {
    width: 100%;
  }

  .wrapper_base.specialalign .split_box {
    width: 100%;
  }

  .m_txt.split-lines {
    font-size: 5.9em;
  }
}

/*# sourceMappingURL=src_styles_animatedParagraph_73511378.css.map*/