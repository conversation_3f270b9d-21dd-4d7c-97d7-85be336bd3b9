const CHUNK_PUBLIC_PATH = "server/pages/webstories/[category]/[slug].js";
const runtime = require("../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/src_components_common_BacktoTop_jsx_77c8de36._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__96a1424e._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_0e3374d1._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_md_index_mjs_a52b9af3._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_gr_index_mjs_79793c0c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_rx_index_mjs_f6315842._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_ci_index_mjs_c424bbd0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_io_index_mjs_0ae45aa7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_io5_index_mjs_d99fb21f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_react-icons_lib_6322dabf._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@swc_helpers_cjs_ac5ba3e8._.js");
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/src/pages/webstories/[category]/[slug]/index.jsx [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/src/pages/_document.jsx [ssr] (ecmascript)\", INNER_APP => \"[project]/src/pages/_app.jsx [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/src/pages/webstories/[category]/[slug]/index.jsx [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/src/pages/_document.jsx [ssr] (ecmascript)\", INNER_APP => \"[project]/src/pages/_app.jsx [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
