{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/about.css"], "sourcesContent": ["#about_wrapper {\r\n  background-color: var(--body-bg-color);\r\n  padding: 60px 0;\r\n}\r\n\r\n#about_wrapper h1 {\r\n  font-size: 4rem;\r\n  line-height: 1.1;\r\n  font-family: rocky, sans-serif;\r\n  margin-bottom: 1.5rem;\r\n  padding: 0 1rem;\r\n}\r\n\r\n.container-wrap {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n}\r\n\r\n.text-container {\r\n  height: 100%;\r\n  padding: 2rem 1rem;\r\n  border-right: 1px solid var(--about-side-border);\r\n  padding-right: 2rem;\r\n}\r\n\r\n.text-container p {\r\n  font-size: 18px;\r\n  line-height: 28px;\r\n  letter-spacing: 0.37px;\r\n  font-weight: 300;\r\n  font-family: Georgia, sans-serif;\r\n  color: var(--text-color);\r\n  margin-bottom: 1.5rem;\r\n}\r\n.text-container p .bold{\r\n  font-weight: 600;\r\n}\r\n.text-container p.bold-p{\r\n  margin-bottom: .5rem;\r\n}\r\n.mt-top{\r\n  margin-top: 2.5rem;\r\n}\r\n.tel{\r\n  color: rgb(52, 117, 222);\r\n}\r\n.text-container ul li {\r\n  font-size: 18px;\r\n  line-height: 28px;\r\n  letter-spacing: 0.37px;\r\n  font-weight: 300;\r\n  font-family: Georgia, sans-serif;\r\n  color: var(--text-color);\r\n  margin-bottom: 0.6rem;\r\n  list-style-position: inside;\r\n}\r\n.text-container ul {\r\n  padding-left: 20px;\r\n}\r\n\r\n.text-container ul li {\r\n  list-style-position: outside;\r\n  padding-left: 10px;\r\n}\r\n\r\n.text-container h2 {\r\n  font-size: 26px;\r\n  line-height: 32px;\r\n  letter-spacing: normal;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n  color: var(--text-color);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@media screen and (max-width: 767px) {\r\n  #about_wrapper h1 {\r\n    font-size: 2rem;\r\n    color: var(--text-color);\r\n    width: 95%;\r\n    line-height: 1;\r\n    font-family: rocky, sans-serif;\r\n    letter-spacing: 0.37px;\r\n    padding: 0 0.625rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n  #about_wrapper .containerWrapper {\r\n    padding: 0.625rem !important;\r\n  }\r\n  .text-container p,\r\n  .text-container ul li {\r\n    font-weight: 300;\r\n    margin-bottom: 1.5rem;\r\n    line-height: 26px;\r\n    font-size: 16px;\r\n    font-family: Georgia, sans-serif;\r\n  }\r\n  .text-container h2 {\r\n    font-size: 17px;\r\n    line-height: 22px;\r\n  }\r\n  .text-container ul li {\r\n    margin-bottom: 0.6rem;\r\n  }\r\n  .text-container {\r\n    height: 100%;\r\n    padding: 1rem 0.625rem;\r\n    border-right: none;\r\n    border-bottom: 1px solid #00000026;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 960px) and (min-width: 767px) {\r\n}\r\n@media screen and (max-width: 1300px) and (min-width: 960px) {\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;;AAUA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;AAYA;EACE;;;;;;;;;;;EAUA;;;;EAGA;;;;;;;;EAQA;;;;;EAIA;;;;EAGA;;;;;;;;AAQF;;;;AAEA"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}