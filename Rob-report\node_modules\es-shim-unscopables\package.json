{"name": "es-shim-unscopables", "version": "1.1.0", "description": "Helper package to shim a method into `Array.prototype[Symbol.unscopables]`", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint-disabled": "evalmd README.md", "lint": "eslint --ext=.js,.mjs .", "postlint": "tsc && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@\">= 10.2\" audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-shim-unscopables.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-shim-unscopables/issues"}, "homepage": "https://github.com/ljharb/es-shim-unscopables#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "^5.8.0-dev.20250211"}, "dependencies": {"hasown": "^2.0.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}}