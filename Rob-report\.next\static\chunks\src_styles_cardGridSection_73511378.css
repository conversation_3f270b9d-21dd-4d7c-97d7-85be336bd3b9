/* [project]/src/styles/cardGridSection.css [client] (css) */
.category_separate .sectioner {
  padding-bottom: 0;
}

.containerWrapper.category_separate {
  padding-bottom: 0;
}

.grid__Section_pt {
  padding-top: 50px;
}

@media only screen and (width >= 41.75rem) {
  .sectioner--featured-category {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

.sectioner--the-latest-posts {
  background-color: var(--background-color);
  transition: background-color .45s ease-in .5s;
}

.module__heading, .section-header {
  border-bottom: 1px solid #979797;
  border-left: 1px solid #979797;
  color: #000;
  margin-bottom: .9375rem;
  padding: 0 0 .3125rem .625rem;
  text-transform: uppercase;
}

.section-header {
  width: 100%;
  display: flex;
  align-items: center;
}

.sectioner--featured-category .section-header {
  display: block;
  border-bottom: none;
  border-left: none;
  text-align: center;
  padding: 0;
  margin-bottom: 50px;
  font-family: sweet-sans-pro, sans-serif;
}

.section-header__heading {
  letter-spacing: 1px;
  font-weight: 600;
}

.sectioner--featured-category .section-header .section-header__heading {
  position: relative;
  z-index: 1;
  font-size: var(--desktop-font-heading--d-h2);
}

.sectioner--featured-category .section-header .section-header__heading:before {
  border-top: .1px solid var(--text-color);
  content: "";
  margin: 0 auto;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: -1;
}

.sectioner--featured-category .section-header .section-header__heading a, .sectioner--featured-category .section-header .section-header__heading span {
  background-color: var(--body-bg-color);
  padding: 0 10px;
  font-size: 2rem;
  letter-spacing: 1px;
  line-height: 24px;
  color: var(--text-color);
}

.sectioner--the-latest-posts .section-header__date {
  font-size: 14px;
  letter-spacing: .35px;
  color: var(--gray-span);
  margin: 14px;
}

.the-latest__secondary-wrapper .the-latest__story:last-of-type {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

@media only screen and (width <= 41.6875rem) {
  .sectioner--featured-category {
    padding-left: 0;
    padding-right: 0;
  }

  .sectioner--featured-category .section-header .section-header__heading a, .sectioner--featured-category .section-header .section-header__heading span {
    font-size: 1.375rem;
    letter-spacing: 1px;
  }

  .sectioner {
    margin-bottom: 0;
  }
}

@media screen and (width <= 479px) {
  .sectioner--featured-category .section-header .section-header__heading {
    font-size: var(--mobile-font-heading--m-h2);
  }
}

.featured-category__secondary-wrapper {
  width: 100%;
}

@media only screen and (width >= 41.75rem) {
  .featured-category__secondary-wrapper {
    display: flex;
    justify-content: space-between;
  }
}

@media only screen and (width >= 61.25rem) {
  .featured-category__secondary-wrapper {
    flex-direction: column;
    width: calc(35% - 1.875rem);
  }
}

@media only screen and (width >= 92.5rem) {
  .featured-category__secondary-wrapper {
    width: calc(30% - .125rem);
  }
}

.the-latest__secondary-wrapper {
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
}

.article {
  display: block;
}

.featured-category__story {
  width: 100%;
  margin-bottom: 1.25rem;
  text-align: center;
}

.featured-category__story .post-meta {
  display: flex;
  flex-direction: row-reverse;
  align-items: flex-start;
  justify-content: center;
  padding-bottom: 35px;
}

.post-meta__timestamp {
  margin-left: 25px;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: .35px;
  color: var(--gray-span);
}

@media only screen and (width >= 41.75rem) {
  .featured-category__story {
    width: calc(50% - 1.25rem);
  }
}

@media only screen and (width >= 61.25rem) {
  .featured-category__story {
    width: 100%;
    margin-bottom: 0;
  }
}

@media only screen and (width >= 41.75rem) {
  .the-latest__secondary-wrapper .the-latest__story {
    width: calc(50% - 1.25rem);
  }
}

.featured-image {
  margin-bottom: .625rem;
}

.featured-category__secondary-wrapper .featured-image {
  position: relative;
}

.featured-category__secondary-wrapper .featured-image:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 56.25%;
}

.featured-image .image-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: block;
}

.featured-category__secondary-wrapper .featured-image .image-wrapper {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.featured-image img {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
}

.featured-image .image-wrapper:after {
  content: "";
  display: block;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.entry__category {
  color: #e02020;
  display: inline-block;
  letter-spacing: 1px;
  margin-bottom: .625rem;
  text-transform: uppercase;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
}

.featured-category__secondary-wrapper .entry__category {
}

.featured-category__story .entry__category {
  font-size: 11px;
  line-height: 15px;
}

@media only screen and (width >= 70.625rem) {
  .featured-category__story .entry__category {
    font-size: 15px;
    line-height: 22px;
  }
}

@media only screen and (width <= 61.1875rem) {
  .featured-category__secondary-wrapper .featured-image {
    max-width: 100%;
    min-width: 35%;
    overflow: hidden;
  }
}

@media only screen and (width <= 375px) {
  .featured-category__secondary-wrapper .featured-image {
    max-height: 90px;
  }
}

@media only screen and (width <= 41.6875rem) {
  .the-latest__secondary-wrapper .featured-image {
    max-width: 100%;
    min-width: 35%;
    overflow: hidden;
  }

  .grid__Section_pt {
    padding-top: 25px;
    padding-bottom: 0;
  }
}

@media only screen and (width <= 375px) {
  .the-latest__secondary-wrapper .featured-image {
    max-height: 90px;
  }
}

.entry__category a {
  color: inherit;
  text-decoration: none;
}

html, html a {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

.entry__heading {
  color: inherit;
  margin-bottom: .75rem;
  max-width: 100%;
  font-family: rocky, sans-serif;
  letter-spacing: .37px;
}

.category_space .entry__heading {
  margin-top: 1rem;
}

.featured-category__secondary-wrapper .entry__heading {
}

.featured-category__story .entry__heading {
  font-family: rocky, sans-serif;
  color: var(--text-color);
  font-size: 23px;
  line-height: 29px;
  letter-spacing: normal;
  transition: opacity .3s ease-in .5s;
}

.featured-category__story .entry__heading:hover {
  opacity: .8;
}

.home .entry__heading {
  transition: opacity .3s ease-in .5s;
}

.entry__heading a {
  color: inherit;
  text-decoration: none;
  transition: opacity .3s ease-in .5s;
}

.entry__heading a:hover {
  opacity: .7;
}

.post-meta {
  align-items: center;
  display: flex;
  font-family: sweet-sans-pro, sans-serif;
}

.featured-category__story .post-meta {
  justify-content: center;
  padding-bottom: 15px;
}

.sectioner--the-latest-posts .post-meta {
  flex-direction: row-reverse;
  align-items: flex-start;
  padding-bottom: 35px;
}

.latest-story--primary .post-meta {
  flex-direction: row-reverse;
  justify-content: center;
  align-items: flex-start;
}

.latest-story .post-meta {
  flex-direction: row-reverse;
  justify-content: flex-end;
  align-items: flex-start;
}

.grid__Section_dark .post-meta__author, .grid__Section_dark .post-meta__timestamp, .two-rivers-wrapper .post-meta__author, .two-rivers-wrapper .post-meta__timestamp {
  color: var(--gray-span) !important;
}

.post-meta__author {
  font-size: 14px;
  font-style: italic;
  color: var(--gray-span);
  order: 2;
  letter-spacing: 1px;
}

.post-meta__author a {
  color: inherit;
}

.featured-category__story .post-meta a {
  font-style: normal;
  text-transform: uppercase;
}

.post-meta__author .by_author {
  font-family: Georgia, sans-serif;
  line-height: 14px;
}

.post-meta__author .author_name {
  font-family: sweet-sans-pro, sans-serif;
  text-transform: uppercase;
  font-style: normal;
  font-weight: 600;
  line-height: 14px;
}

.post-meta__author a span {
  letter-spacing: .35px;
  font-style: normal;
  text-transform: uppercase;
}

.post-meta__author a span, .post-meta__author time {
}

.sectioner--the-latest-posts .post-meta__timestamp {
  margin-left: 25px;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: .35px;
  color: var(--gray-span);
}

@media only screen and (width <= 41.6875rem) {
  .featured-category__secondary-wrapper .featured-category__story {
    display: flex;
  }

  .featured-category__secondary-wrapper .entry {
    text-align: start;
    margin-left: 10px;
  }

  .the-latest__secondary-wrapper .entry {
    display: flex;
    flex-direction: column;
  }

  .the-latest__secondary-wrapper .entry .entry__category {
    order: 1;
    margin-bottom: 5px;
  }

  .featured-category__story .entry__heading {
    font-size: 17px;
    line-height: 22px;
  }

  .the-latest__secondary-wrapper .entry .entry__heading {
    order: 2;
    margin-bottom: 5px;
  }

  .category_space .entry__heading {
    margin-top: 0;
  }

  .featured-category__story .post-meta {
    padding-bottom: 0;
  }

  .the-latest__secondary-wrapper .post-meta {
  }

  .featured-category__secondary-wrapper .entry .post-meta {
    justify-content: flex-end;
    order: 3;
  }

  .featured-category__secondary-wrapper .entry .post-meta .post-meta__author {
    font-size: 12px;
  }

  .featured-category__secondary-wrapper .entry .post-meta .post-meta__timestamp {
    display: none;
  }
}

@media only screen and (width <= 41.6875rem) and (width <= 41.6875rem) {
  .featured-category__secondary-wrapper .featured-category__story {
    border-bottom: .0625rem solid #dddee4;
    padding-bottom: 1.25rem;
  }
}

@media only screen and (width <= 41.6875rem) {
  .the-latest__secondary-wrapper .the-latest__story {
    display: flex;
    justify-content: flex-start;
    text-align: left;
  }
}

/*# sourceMappingURL=src_styles_cardGridSection_73511378.css.map*/