/* [project]/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css [client] (css) */
.skeleton-module__yM3A9W__skeleton {
  display: block;
  width: 100%;
  border-radius: 5px;
  background-image: var(--tweet-skeleton-gradient);
  background-size: 400% 100%;
  animation: 8s ease-in-out infinite skeleton-module__yM3A9W__loading;
}

@media (prefers-reduced-motion: reduce) {
  .skeleton-module__yM3A9W__skeleton {
    animation: none;
    background-position: 200% 0;
  }
}

@keyframes skeleton-module__yM3A9W__loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

/*# sourceMappingURL=node_modules_react-tweet_dist_twitter-theme_skeleton_module_73511378.css.map*/