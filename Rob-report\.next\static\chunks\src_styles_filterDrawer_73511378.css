/* [project]/src/styles/filterDrawer.css [client] (css) */
#drawer_container {
  width: 100%;
  height: 100dvh;
  background-color: #00000059;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 9999;
  pointer-events: none;
  backdrop-filter: blur(4px);
  opacity: 0;
}

#list_container {
  position: absolute;
  right: -100%;
  top: 0;
  width: 30%;
  height: 100%;
  background-color: var(--body-bg-color);
}

.Search_drawer #list_container {
  width: 35%;
}

.Search_drawer form svg {
  cursor: pointer;
}

.Search_drawer .chipContainer {
  margin: 0;
  margin-top: 3rem;
}

.filter_title {
  font-size: 1.2rem;
  color: var(--text-color);
  font-family: rocky, sans-serif;
  line-height: 1.1;
}

.accordion {
  margin-top: 10px;
  border-bottom: 1px solid #d2d2d2;
}

.accordion_header {
  cursor: pointer;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.accordion_header span {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 1.2rem;
  color: var(--text-color);
  font-family: sweet-sans-pro, sans-serif;
  line-height: 1.1;
}

.accordion_header span.clear_text {
  font-size: .8rem;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 23px;
  letter-spacing: .08rem;
  text-transform: uppercase;
}

.accordion_content {
  margin-top: 5px;
  padding-left: 10px;
  padding-bottom: 20px;
  max-height: 250px;
  overflow: auto;
}

.category_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 1rem;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 23px;
  letter-spacing: .08rem;
}

.category_item input {
  margin-right: 10px;
  cursor: pointer;
}

.item_count {
  color: #888;
}

.filter_title_row {
  position: sticky;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: .5rem;
  font-size: 1.2rem;
  font-weight: 600;
  padding: 2.5rem;
  padding-top: 1.8rem;
  padding-bottom: 3rem;
}

.search_drawer.filter_title_row {
  justify-content: end;
}

#search_drawer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: .5rem;
  border-bottom: 1px solid var(--chip-color);
}

#search_drawer input {
  width: 100%;
  padding: 10px;
  background-color: #0000;
  border: none;
  outline: none;
  font-size: 1rem;
}

#search_drawer input::placeholder {
  font-size: 1rem;
}

#search_drawer svg {
  font-size: 1.6rem;
}

.search_chipContainer .border_btn_button {
  border: 1px solid var(--chip-color);
  color: var(--chip-color);
}

.search_chipContainer .border_btn_button:hover {
  color: var(--body-bg-color);
}

.drawer_body {
  position: relative;
  width: 100%;
  height: calc(100% - 248px);
  overflow-y: scroll;
  padding: 0 2.5rem;
}

.drawer_body_search::-webkit-scrollbar {
  display: none;
}

.drawer_footer {
  width: 100%;
  position: relative;
  padding: 2.5rem;
}

.drawer_footer .button_base {
  width: 100%;
}

.close_icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.close_icon svg {
  font-size: 35px;
  color: var(--text-color);
  font-family: rocky, sans-serif;
  line-height: 1.1;
  cursor: pointer;
}

.accordion_label {
  width: 100%;
  display: flex;
  align-items: center;
  gap: .5rem;
}

.arrow_icon {
  transition: transform .3s;
}

.arrow_icon.rotated {
  transform: rotate(180deg);
}

@media screen and (width <= 767px) {
  #list_container {
    width: 100%;
    padding: 1rem;
  }

  .Search_drawer #list_container {
    width: 100%;
  }

  .drawer_body {
    padding: 0;
  }

  .filter_title_row {
    padding: 0 0 4rem;
  }

  .drawer_footer {
    padding: 2.5rem 0;
  }
}

@media screen and (width <= 960px) and (width >= 767px) {
  #list_container {
    width: 55%;
  }
}

@media screen and (width <= 1300px) and (width >= 960px) {
  #list_container {
    width: 42%;
  }
}

/*# sourceMappingURL=src_styles_filterDrawer_73511378.css.map*/