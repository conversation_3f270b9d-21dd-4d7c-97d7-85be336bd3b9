/* [project]/src/styles/categoryPage.css [client] (css) */
.categoryWrapper {
  color: var(--text-color);
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
  min-height: 60vh;
}

.categoryWrapper .featured-category__story .entry__heading {
  color: var(--text-color);
  transition: color .45s ease-in .5s;
}

.categoryWrapper .sectioner--featured-category .section-header .section-header__heading:before {
  border-top: .1px solid var(--body-bg-colorblackwhite);
  transition: border-top .45s ease-in .5s;
}

.categoryWrapper .sectioner--featured-category .section-header .section-header__heading a, .categoryWrapper .sectioner--featured-category .section-header .section-header__heading span {
  background-color: var(--body-bg-color);
  color: var(--text-color);
  transition: background-color .45s ease-in .5s, color .45s ease-in .5s;
}

section.tab_section {
  position: relative;
  padding: 40px 0;
  scroll-margin-top: 120px;
}

section.tab_section.noPadding {
  padding: 0;
}

.page_head_set {
  text-align: center;
  padding: 1vw 0 1.5vw;
  flex-direction: column;
  align-items: center;
  font-size: 1.25vw;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
}

.editionTitle {
  font-size: 3vw;
  line-height: 10vw;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.page_breadcrumb {
  padding-bottom: 2rem;
}

.page_breadcrumb ol li a {
  font-size: 16px;
}

.page_breadcrumb ol {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  list-style: none;
  font-size: 20px;
  padding: 0;
}

.page_breadcrumb ol li:after {
  content: ">";
  display: inline-block;
  position: relative;
  font-weight: 300;
  font-size: 14px;
  margin: 0 5px;
}

.page_breadcrumb ol li:last-child:after {
  content: "";
}

.search_bar {
  padding: 12px 30px;
  justify-content: space-between;
  background-color: var(--body-bg-colorblackwhite);
  color: var(--text-colorwhite);
  transition: background-color .45s ease-in .5s, color .45s ease-in .5s;
}

.search_bar, .search_bar_side, .filters_toggle {
  align-items: center;
}

.bar_input {
  width: 100%;
}

.flexCntr {
  display: flex;
}

.search_bar_side {
  mix-blend-mode: exclusion;
}

.bar_icon {
  position: relative;
  padding-right: 20px;
  border-right: 1px solid #ffffff4d;
}

.bar_icon, .col_image {
  margin-right: 20px;
}

.bar_input input {
  background: none;
  border: none;
  outline: none;
  appearance: none;
  color: #fff;
  width: 100%;
  min-width: 232px;
  font-size: 1rem;
}

input::selection {
  background: none;
}

.bar_input input:focus {
  outline: none;
  border: none;
  box-shadow: none;
  background-color: #0000 !important;
}

input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus {
  border: 1px solid #0000;
  -webkit-text-fill-color: #f1f1f1;
  -webkit-box-shadow: inset 0 0 0 1000px #0000;
  transition: background-color 5000s ease-in-out;
  background-color: #0000 !important;
}

.f_lable {
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 500;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
}

.filters_circle {
  width: 32px;
  height: 32px;
  background: #fff;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.filters_circle, .tab span a {
  margin-left: 20px;
}

.filters_circle i {
  width: 4px;
  height: 4px;
  background: #fff;
  mix-blend-mode: exclusion;
  margin: 1px;
  border-radius: 50%;
}

.search_bar, .search_bar_side, .filters_toggle {
  align-items: center;
}

.fixed-bar {
  position: sticky;
  top: 6.5rem;
  z-index: 99;
  width: 100%;
}

@media only screen and (width <= 61.1875rem) {
  .fixed-bar {
    top: 4.5rem;
  }
}

.fixed_item {
  z-index: 599;
  position: relative;
  transition: margin .2s cubic-bezier(.22, .61, .36, 1);
}

.page_bar {
  margin: 0 42px;
}

.tabs_bar {
  width: 100%;
  display: flex;
  align-items: center;
  color: #fff;
  background: #000000b3;
  background: #2a2a2a;
  height: 60px;
  overflow-x: hidden;
}

.tabs_bar::-webkit-scrollbar {
  display: none;
}

.tabs_bar .tab {
  width: 16.6667%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}

.tabs_bar .tab:before {
  position: absolute;
  content: "";
  width: 1px;
  top: 0;
  right: 0;
  bottom: 0;
  background: #ffffff68;
  z-index: 99;
}

.full_bg {
  height: 100%;
  width: 0%;
  background-color: #3f3f3f;
  transform-origin: 0;
  position: absolute;
  left: 0;
}

.tab_content {
  position: relative;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  padding: 20px 25px;
  font-family: sweet-sans-pro, sans-serif;
}

.tabs_bar .tab_content span {
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
  letter-spacing: 1px;
}

.tabs_bar .tab_content .f_16 {
  font-size: 16px;
  line-height: 20px;
}

.tabs_bar .tab_content .f_40 {
  font-weight: 500;
  text-transform: uppercase;
}

.tab span:not(.flex) {
  display: block;
}

.bar_icon svg {
  max-width: 100%;
  cursor: pointer;
}

@media screen and (width <= 1200px) {
  .tabs_bar {
    width: 100%;
  }

  .editionTitle {
    font-size: 4vw;
  }

  .tabs_bar .tab_content span {
    font-size: 13px;
  }
}

@media screen and (width <= 960px) {
  
}

@media screen and (width <= 800px) {
  .tab_content {
    padding: 10px 0;
  }

  .page_bar {
    margin: 0 10px;
  }

  .tabs_bar .tab_content span {
    font-size: 13px;
  }

  .page_head_set {
    margin: 0;
    font-size: 3vw;
  }
}

@media screen and (width <= 767px) {
  .tabs_bar .tab {
    width: 100%;
  }

  .editionTitle, .search_input input {
    font-size: 6vw;
    line-height: 19vw;
  }

  .line-height-10 {
    line-height: 10vw !important;
  }

  section.tab_section {
    padding: 2rem 0;
  }

  .page_head_set {
    margin: 0;
    font-size: 3vw;
  }

  .page_breadcrumb ol li a {
    font-size: 12px;
  }

  .categoryWrapper .featured-category__story .entry__heading {
    display: -webkit-box;
    max-width: 100%;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@media screen and (width <= 640px) {
  .search_bar {
    padding: 15px;
  }

  .filters_toggle .f_lable {
    display: none;
  }

  .bar_input input {
    min-width: 190px;
  }

  .tabs_bar {
    width: 100%;
  }

  .tabs_bar .tab {
    width: 100%;
  }

  .tabs_bar .tab_content span {
    font-size: 1rem;
  }
}

@media screen and (width <= 600px) {
  .page_head_set {
    margin: 0;
    font-size: 4vw;
  }

  .bar_input input {
    min-width: 100px;
    font-size: 16px;
  }

  .bar_icon, .col_image {
    margin-right: 5px;
    padding-right: 0;
  }

  .bar_icon {
    border: 0;
  }

  .bar_icon svg {
    max-width: 80%;
  }
}

/*# sourceMappingURL=src_styles_categoryPage_73511378.css.map*/