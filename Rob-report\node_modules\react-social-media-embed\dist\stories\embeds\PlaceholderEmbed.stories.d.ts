import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
declare const StoryComponent: ({ url, linkText, imageUrl, spinner, allowJavaScriptUrls, spinnerDisabled, ...divProps }: import("../../components/placeholder/PlaceholderEmbed").PlaceholderEmbedProps) => React.JSX.Element | null;
declare const meta: Meta<typeof StoryComponent>;
export default meta;
type Story = StoryObj<typeof meta>;
export declare const MainExample: Story;
export declare const TextWidthHeight100x100: Story;
export declare const TextWidthHeight200x200: Story;
export declare const TextWidthHeight400x200: Story;
export declare const TextWidthHeight200x400: Story;
export declare const TextWidthHeight600x400: Story;
export declare const TextWidthHeight400x600: Story;
export declare const TextWidth100: Story;
export declare const TextWidth200: Story;
export declare const TextWidth400: Story;
export declare const TextWidth800: Story;
export declare const TextHeight100: Story;
export declare const TextHeight200: Story;
export declare const TextHeight400: Story;
export declare const TextHeight800: Story;
export declare const TextNoSizing: Story;
export declare const LandscapeImageWidthHeight100x100: Story;
export declare const LandscapeImageWidthHeight200x200: Story;
export declare const LandscapeImageWidthHeight400x200: Story;
export declare const LandscapeImageWidthHeight200x400: Story;
export declare const LandscapeImageWidthHeight600x400: Story;
export declare const LandscapeImageWidthHeight400x600: Story;
export declare const LandscapeImageWidth100: Story;
export declare const LandscapeImageWidth200: Story;
export declare const LandscapeImageWidth400: Story;
export declare const LandscapeImageWidth800: Story;
export declare const LandscapeImageHeight100: Story;
export declare const LandscapeImageHeight200: Story;
export declare const LandscapeImageHeight400: Story;
export declare const LandscapeImageHeight800: Story;
export declare const LandscapeImageNoSizing: Story;
export declare const PortraitImageWidthHeight100x100: Story;
export declare const PortraitImageWidthHeight200x200: Story;
export declare const PortraitImageWidthHeight400x200: Story;
export declare const PortraitImageWidthHeight200x400: Story;
export declare const PortraitImageWidthHeight600x400: Story;
export declare const PortraitImageWidthHeight400x600: Story;
export declare const PortraitImageWidth100: Story;
export declare const PortraitImageWidth200: Story;
export declare const PortraitImageWidth400: Story;
export declare const PortraitImageWidth800: Story;
export declare const PortraitImageHeight100: Story;
export declare const PortraitImageHeight200: Story;
export declare const PortraitImageHeight400: Story;
export declare const PortraitImageHeight800: Story;
export declare const PortraitImageNoSizing: Story;
export declare const TextWithSpinnerDisabled: Story;
export declare const ImageWithSpinnerDisabled: Story;
export declare const TextWithCustomSpinner: Story;
export declare const ImageWithCustomSpinner: Story;
