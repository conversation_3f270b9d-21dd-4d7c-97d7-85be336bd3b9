{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/office-work/Rob-report/src/components/common/BacktoTop.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport gsap from \"gsap\";\r\nimport { LiaLongArrowAltUpSolid } from \"react-icons/lia\";\r\nimport { ScrollToPlugin } from \"gsap/ScrollToPlugin\";\r\n\r\ngsap.registerPlugin(ScrollToPlugin);\r\n\r\nconst BackToTop = () => {\r\n  const [showButton, setShowButton] = useState(false);\r\n  const [isDarkBg, setIsDarkBg] = useState(false);\r\n  const buttonRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const scrollY = window.scrollY;\r\n      setShowButton(scrollY > 300);\r\n\r\n      if (buttonRef.current) {\r\n        const { left, top, width, height } =\r\n          buttonRef.current.getBoundingClientRect();\r\n        const x = left + width / 2;\r\n        const y = top + height / 2;\r\n\r\n        const elementBehind = document.elementFromPoint(x, y);\r\n\r\n        if (elementBehind) {\r\n          const bgColor =\r\n            window.getComputedStyle(elementBehind).backgroundColor;\r\n          const rgb = bgColor.match(/\\d+/g)?.map(Number) || [255, 255, 255];\r\n\r\n          const brightness =\r\n            (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\r\n\r\n          setIsDarkBg(brightness < 128);\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", handleScroll);\r\n    handleScroll(); // run initially\r\n\r\n    return () => {\r\n      window.removeEventListener(\"scroll\", handleScroll);\r\n    };\r\n  }, []);\r\n\r\n  const scrollToTop = (e) => {\r\n    e.preventDefault();\r\n    gsap.to(window, { scrollTo: { y: 0 }, duration: 1, ease: \"power2.out\" });\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id=\"buttonTop\"\r\n      ref={buttonRef}\r\n      className={`back-to-top ${showButton ? \"show\" : \"\"} ${\r\n        isDarkBg ? \"light\" : \"dark\"\r\n      }`}\r\n    >\r\n      <button\r\n        className=\"btn-top\"\r\n        aria-label=\"Back to Top\"\r\n        onClick={scrollToTop}\r\n      >\r\n        <LiaLongArrowAltUpSolid />\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BackToTop;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,iGAAA,CAAA,UAAI,CAAC,cAAc,CAAC,iJAAA,CAAA,iBAAc;AAElC,MAAM,YAAY;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,YAAY,CAAA,GAAA,mGAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,UAAU,OAAO,OAAO;YAC9B,cAAc,UAAU;YAExB,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAChC,UAAU,OAAO,CAAC,qBAAqB;gBACzC,MAAM,IAAI,OAAO,QAAQ;gBACzB,MAAM,IAAI,MAAM,SAAS;gBAEzB,MAAM,gBAAgB,SAAS,gBAAgB,CAAC,GAAG;gBAEnD,IAAI,eAAe;oBACjB,MAAM,UACJ,OAAO,gBAAgB,CAAC,eAAe,eAAe;oBACxD,MAAM,MAAM,QAAQ,KAAK,CAAC,SAAS,IAAI,WAAW;wBAAC;wBAAK;wBAAK;qBAAI;oBAEjE,MAAM,aACJ,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI;oBAEjD,YAAY,aAAa;gBAC3B;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,gBAAgB,gBAAgB;QAEhC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,iGAAA,CAAA,UAAI,CAAC,EAAE,CAAC,QAAQ;YAAE,UAAU;gBAAE,GAAG;YAAE;YAAG,UAAU;YAAG,MAAM;QAAa;IACxE;IAEA,qBACE,qKAAC;QACC,IAAG;QACH,KAAK;QACL,WAAW,CAAC,YAAY,EAAE,aAAa,SAAS,GAAG,CAAC,EAClD,WAAW,UAAU,QACrB;kBAEF,cAAA,qKAAC;YACC,WAAU;YACV,cAAW;YACX,SAAS;sBAET,cAAA,qKAAC,wIAAA,CAAA,yBAAsB;;;;;;;;;;;;;;;AAI/B;uCAEe", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}