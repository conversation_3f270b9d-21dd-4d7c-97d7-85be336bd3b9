/* [project]/src/styles/globals.css [client] (css) */
:root {
  --background: #fff;
  --foreground: #171717;
  --btn-top-bg: #fff;
  --gray-bg: #000000d9;
  --red: red;
  --mobile-font-size--size-normal: 1rem;
  --black-dark: #0e1010;
  --white: white;
  --desktop-font-heading--d-h1: 4.25rem;
  --mobile-font-heading--m-h1: 2.75rem;
  --mobile-font-size--size-large-30: 1.875rem;
  --desktop-font-heading--d-h2: 2.5rem;
  --mobile-font-heading--m-h2: 1.2rem;
  --desktop-font-heading--d-h3: 1.5625rem;
  --mobile-font-heading--m-h3: 1.4375rem;
  --desktop-font-size-normal--subtitle-18: 1.125rem;
  --mobile-font-size--subtitle-15: .9375rem;
  --orange: #f68a33;
  --desktop-font-size-normal--font-size-large-60: 3.75rem;
  --desktop-font-size-normal--body-19: 1.1875rem;
  --transparent: #0000;
  --dark-text: #0f1628;
  --black: black;
  --r-page--cta: .812rem;
  --desktop-paragraph-font--small-p: .9375rem;
  --desktop-font-size-normal--tag-14: .875rem;
  --dark-gray: #727272;
  --gray: #8d8d8d;
  --black-2-0: #0f1628;
  --mobile-font-size--small-12: .75rem;
  --desktop-font-size-normal--questions-20: 1.25rem;
  --border-light: #0f16284d;
  --white-2-0: #efefef;
  --white-light: #fafaf1;
  --desktop-font-size-normal--caption-10: .625rem;
  --green: #396e8b;
  --border-dark: white;
  --navy-blue: #0b347c;
  --desktop-paragraph-font--paragraph: 1.0625rem;
  --desktop-font-size-normal--intro-35: 2.1875rem;
  --mobile-font-size--size-medium-20: 1.25rem;
  --Rockyfontfamily: rocky, serif;
  --Sweetfontfamily: sweet, serif;
  --gray-span: #fff;
  --body-bg-color: #000;
  --body-bg-colorblackwhite: #fff;
  --text-color: #fff;
  --text-colorblack: #000;
  --filterblack: invert(1);
  --drawer-btn-bg: #000;
  --drawer-btn-bg-hover: #fff;
  --about-side-border: #80808099;
  --chip-color: #fff;
  --line-color: #80808099;
}

[data-theme="light"] {
  --body-bg-color: #fff;
  --chip-color: #000;
  --body-bg-colorblackwhite: #000;
  --text-color: #000;
  --text-colorwhite: #fff;
  --filterwhite: invert(100%);
  --filterblack: invert(0);
  --btn-top-bg: #000;
  --gray-span: #575757;
  --drawer-btn-bg: #fff;
  --drawer-btn-bg-hover: #000;
  --about-side-border: #00000026;
  --line-color: #00000026;
  --related-post-bg: #f7f7f7;
}

[data-theme="dark"] {
  --related-post-bg: #e1e1e10d;
}

.dark .nav-content {
  color: #000;
}

.dark .nav-content .brand img {
  filter: invert(0);
}

.dark .nav-content .menu-item {
  color: #000;
}

.HalfWidthBtn {
  align-items: center;
  justify-content: center;
  display: flex;
  width: 100%;
}

html, body {
  max-width: 100vw;
}

body {
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: var(--mobile-font-size--size-normal);
  font-weight: 400;
  line-height: 120%;
  background-color: var(--background);
  min-height: 100%;
  font-display: swap;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

.w-100, .w-full {
  width: 100% !important;
}

.w-50 {
  width: 50% !important;
}

.p-0 {
  padding: 0 !important;
}

.py-0 {
  padding-block: 0 !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pl-0 {
  padding-left: 0 !important;
}

.pr-0 {
  padding-right: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-2 {
  margin-bottom: .5rem !important;
}

.text-underline {
  text-decoration: underline !important;
}

.text-bold {
  font-weight: bold !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.flex-space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ShareBtn {
  display: flex;
  gap: 5px;
  align-items: center;
  cursor: pointer;
  transition: all .5s;
  line-height: 1;
}

.ShareBtn svg {
  font-size: 1rem;
}

.ShareBtn span {
  font-size: 1rem;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 400;
}

.ShareBtn:hover {
  opacity: .8;
}

#sideBtn_container {
  position: fixed;
  bottom: 20px;
  right: 10px;
  display: flex;
  flex-direction: column;
  justify-content: end;
  gap: 20px;
  z-index: 998;
}

.back-to-top {
  opacity: 0;
  pointer-events: none;
  transition: all .3s;
}

.back-to-top.show {
  opacity: 1;
  pointer-events: auto;
}

.btn-top {
  position: relative;
  gap: 8px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 5px;
  font-size: 1.35rem;
  cursor: pointer;
  border: 1px solid;
  background-color: #0000;
  color: var(--btn-top-color, #fff);
  transition: all .3s;
}

.btn-top svg {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.btn-top {
  transition: all .3s;
  cursor: pointer;
}

.btn-top:hover {
  scale: .93;
}

.back-to-top.dark .btn-top {
  color: #fff;
  background-color: #0000;
}

.back-to-top.light .btn-top {
  color: #000;
  background-color: #fff;
}

.search_btn {
  transition: all .3s;
  cursor: pointer;
}

.search_btn:hover {
  scale: .93;
}

.search-btn {
  position: relative;
  gap: 8px;
  width: 40px;
  height: 40px;
  padding: 5px;
  font-size: 1.8rem;
  cursor: pointer;
  border: 1px solid;
  background-color: #0000;
  color: var(--text-color) !important;
}

.search-btn svg {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.search_btn.dark .search-btn {
  color: #fff;
  background-color: #0000;
}

.search_btn.light .search-btn {
  color: #fff;
  border: none;
}

.ad-flex-all {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ad-text:before {
  color: var(--gray-span);
  content: "ADVERTISEMENT";
  display: block;
  font-family: Arial, sans-serif;
  font-size: 9px;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: 1.2;
  margin: 5px auto;
  text-align: center;
  text-transform: uppercase;
  -webkit-font-smoothing: antialiased;
}

.chipContainer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin: 0 42px 40px;
}

@media only screen and (width >= 768px) {
  #sideBtn_container {
    bottom: 50px;
    right: 25px;
  }

  .ShareBtn svg {
    font-size: 1.25rem;
  }

  .ShareBtn span {
    font-size: 1.25rem;
  }
}

.headingTitle, .headingTitle h1 {
  display: inline;
  font-size: 3vw;
  line-height: 10vw;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
  letter-spacing: 1px;
}

.sponsoredTitle, .sponsoredTitle h1 {
  display: inline;
  font-size: 2.5vw;
  line-height: 10vw;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
  letter-spacing: 1px;
  color: #e02020;
  text-transform: uppercase;
}

html.w-mod-touch * {
  background-attachment: scroll !important;
}

.containerWrapper {
  width: 100%;
  max-width: 70rem;
  margin: 0 auto;
  padding: 2rem 0;
  padding-right: .625rem !important;
  padding-left: .625rem !important;
}

@media screen and (width <= 1200px) {
  .headingTitle, .headingTitle h1 {
    font-size: 4vw;
  }

  .sponsoredTitle, .sponsoredTitle h1 {
    font-size: 4vw;
  }
}

@media screen and (width <= 768px) {
  .headingTitle, .headingTitle h1 {
    font-size: 5vw;
  }

  .sponsoredTitle, .sponsoredTitle h1 {
    font-size: 5vw;
  }

  .chipContainer {
    margin: 10px 10px 25px;
  }
}

@media screen and (width <= 479px) {
  .headingTitle, .headingTitle h1 {
    font-size: 6.5vw;
  }

  .sponsoredTitle, .sponsoredTitle h1 {
    font-size: 6vw;
  }
}

@media only screen and (width >= 92.5rem) {
  .containerWrapper {
    max-width: 100rem;
    width: calc(100% - 21.875rem);
    padding-right: .625rem !important;
    padding-left: .625rem !important;
  }
}

.w-inline-block {
  max-width: 100%;
  display: inline-block;
}

.button_base {
  font-weight: 500;
  font-size: 17px;
  line-height: 20px;
  letter-spacing: 1px;
  display: block;
  text-align: center;
  text-transform: uppercase;
  font-family: sweet-sans-pro, sans-serif;
}

.view-more {
  padding: 1.125rem 0 .875rem;
}

.button_base.black {
  background-color: #323440;
  letter-spacing: 1.4px;
  color: var(--white);
  font-weight: 700;
  text-transform: uppercase;
  padding: 1.25rem 1.875rem;
  transition: all .5s;
  cursor: pointer;
}

.button_base.black:hover {
  background-color: #323440e6;
}

.drawer_footer .button_base.black {
  background-color: var(--drawer-btn-bg);
  color: var(--drawer-btn-bg-hover);
}

.drawer_footer .button_base.black:hover {
  background-color: var(--drawer-btn-bg-hover);
  color: var(--drawer-btn-bg);
}

.hasMore_btn_wrap {
  width: 100%;
  display: flex;
  justify-content: center;
}

.hasMore_btn_wrap .button_base {
  min-width: calc(50% - 3.25rem);
  border: none;
}

.sectioner--latest-stories .view-more-stories {
  width: 100%;
  margin-top: 2.5rem;
}

#home_wrappper {
  background-color: var(--body-bg-color);
}

@media only screen and (width <= 41.6875rem) {
  .sectioner--latest-stories .view-more-stories {
    margin-top: 0;
    margin-bottom: 0;
  }

  .button_base.black {
    width: 100%;
    display: inline-block;
    font-size: 17px;
    line-height: 10px;
    letter-spacing: 1px;
  }
}

@media only screen and (width <= 479px) {
  .button_base.black {
    justify-content: center;
    align-items: center;
    width: 100%;
    font-size: 4vw;
    display: flex;
    padding: 1.25rem .875rem;
  }
}

::-webkit-resizer {
  width: 0;
  height: 0;
}

.cont-link .ex-text {
  pointer-events: none;
}

.modes_helpers {
  height: calc(var(--vh, 1vh) * 100);
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  visibility: hidden;
  bottom: auto;
  width: 100%;
}

.modes_helpers i {
  transform: scaleY(0);
  position: fixed;
  width: 100%;
  height: 100%;
  visibility: visible;
}

.modes_helpers .a {
  background-color: #000;
}

.modes_helpers .b {
  background-color: #fff;
}

.modes_helpers.reverse .a {
  background-color: #000;
}

.modes_helpers.reverse .b {
  background: #fff;
}

.TagWrapper {
  color: var(--text-color);
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
}

.TagWrapper .featured-category__story .entry__heading {
  color: var(--text-color);
  transition: color .45s ease-in .5s;
}

.result-loader-div {
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-cont {
  position: fixed;
  z-index: 999;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--body-bg-color);
}

.MuiLinearProgress-root {
  position: relative;
  overflow: hidden;
  display: block;
  z-index: 0;
  background-color: #dcdcdc !important;
  height: 2px !important;
}

.MuiBox-root {
  width: 15% !important;
}

.MuiLinearProgress-bar {
  background-color: var(--text-colorblack) !important;
}

.MuiLinearProgress-bar1 {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  -webkit-transition: -webkit-transform .2s linear;
  transition: transform .2s linear;
  transform-origin: 0;
  background-color: var(--text-colorblack);
  width: auto;
  -webkit-animation: 2.1s cubic-bezier(.65, .815, .735, .395) infinite animation-1;
  animation: 2.1s cubic-bezier(.65, .815, .735, .395) infinite animation-1;
}

.MuiLinearProgress-bar2 {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  -webkit-transition: -webkit-transform .2s linear;
  transition: transform .2s linear;
  transform-origin: 0;
  --LinearProgressBar2-barColor: var(--text-colorblack);
  background-color: var(--LinearProgressBar2-barColor, currentColor);
  width: auto;
  -webkit-animation: 2.1s cubic-bezier(.165, .84, .44, 1) 1.15s infinite animation-2;
  animation: 2.1s cubic-bezier(.165, .84, .44, 1) 1.15s infinite animation-2;
}

@keyframes animation-1 {
  0% {
    left: -35%;
    right: 100%;
  }

  60% {
    left: 100%;
    right: -90%;
  }

  100% {
    left: 100%;
    right: -90%;
  }
}

@keyframes animation-2 {
  0% {
    left: -200%;
    right: 100%;
  }

  60% {
    left: 107%;
    right: -8%;
  }

  100% {
    left: 107%;
    right: -8%;
  }
}


/* [project]/src/styles/homeHero.css [client] (css) */
.specials-section {
  z-index: 3;
  background-color: var(--black);
  padding-top: .625rem;
  padding-bottom: 6.5625rem;
  position: relative;
  overflow-x: hidden;
}

.specials-slider-wrap {
  position: relative;
}

.swiper-horizontal {
  touch-action: pan-y;
}

.swiper-wrapper.specials_swiper_wrapper {
  z-index: -1;
  margin-left: 2.25rem;
  display: flex;
}

.swiper-slide.specials_swiper_slide {
  z-index: -1;
  cursor: grabbing;
  flex: none;
  width: 83rem;
  aspect-ratio: 4 / 2;
}

.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.special-stories-slider-block {
  z-index: 11;
  color: var(--black);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: relative;
  margin: 0 auto;
}

.special-stories-slider-block img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.film-overlay-wrap {
  background-image: linear-gradient(221.52deg, #0e101033 33.53%, #0e1010cc 63.89%, #0e1010fa 85.9%);
  justify-content: flex-start;
  align-items: flex-end;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 99;
  top: 0;
  padding-left: 2.5rem;
  padding: 0 5rem;
  padding-bottom: 4.375rem;
  padding-right: 2.5rem;
  display: flex;
}

.heroSliderContentWrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

.video-stories-slider-inner-block {
  z-index: 1;
  grid-column-gap: 1.375rem;
  grid-row-gap: 1.375rem;
  border-radius: .3125rem;
  flex-flow: column;
  justify-content: flex-end;
  align-items: flex-start;
  height: 100%;
  max-width: 35.5rem;
  display: flex;
  position: relative;
  font-family: sweet-sans-pro, sans-serif;
}

.heading-h2 {
  font-size: var(--desktop-font-heading--d-h2);
  letter-spacing: -.16px;
  margin-top: 0;
  margin-bottom: 0;
  font-weight: 400;
  line-height: 120%;
  font-family: rocky, sans-serif;
}

.special-stories-summary {
  color: var(--dark-gray);
  font-size: var(--mobile-font-size--subtitle-15);
  letter-spacing: .32px;
  width: 100%;
  max-width: 31rem;
  line-height: 1.40625rem;
  font-family: sweet-sans-pro, sans-serif;
}

.text-color-white {
  color: var(--white);
}

.special-stories-date-block {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  color: var(--red);
  font-size: var(--desktop-paragraph-font--small-p);
  letter-spacing: .16px;
  text-transform: uppercase;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.special-stories-date-block.color-white, .special-stories-title.letter-animation.color-white {
  color: var(--white);
}

.swiper-arrow-wrap {
  z-index: 5;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 95%;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  position: absolute;
  inset: 45% 0% auto;
  transform: translateY(-50%);
}

.play-films-html {
  color: var(--white);
  justify-content: center;
  align-items: center;
  display: flex;
}

.w-embed:before, .w-embed:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after, .swiper-button-next:after, .swiper-rtl .swiper-button-prev:after {
  content: "" !important;
}

.swiper-button-prev, .swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}

.swiper-button-prev, .swiper-button-next {
  position: static !important;
}

.swiper-button-prev, .swiper-button-next {
  color: #fff;
}

.swiper-button-prev, .swiper-button-next {
  border: 1px solid var(--white);
  color: var(--white);
  cursor: pointer;
  border-radius: 2.5rem;
  justify-content: center;
  align-items: center;
  width: 3.125rem;
  min-width: 3.125rem;
  height: 3.125rem;
  min-height: 3.125rem;
  transition: all .3s ease-in-out;
  display: flex;
}

.swiper-button-prev:hover {
  background-color: #fff;
}

.swiper-button-next:hover {
  background-color: #fff;
}

.swiper-button-next:hover svg:not(.swiper- button-prev) {
  fill: var(--black);
}

.swiper-button-prev:hover svg:not(.swiper- button-prev) {
  fill: var(--black);
}

.controls-wrapper {
  justify-content: space-between;
  align-items: flex-start;
  max-width: 80%;
  margin-top: 2.25rem;
  padding-left: 2.125rem;
  display: flex;
  font-family: sweet-sans-pro, sans-serif;
}

.next-up-text {
  color: var(--white);
  font-size: var(--desktop-font-size-normal--tag-14);
  letter-spacing: .16px;
  text-transform: uppercase;
  flex: none;
  position: relative;
}

.controls-row {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  flex: none;
  width: 100%;
  max-width: 15rem;
  display: flex;
  position: absolute;
  inset: auto 10.0625rem 0% auto;
}

.bullet-pagination {
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bullet-pagination .bullet {
  font-size: 15px;
  width: 100%;
  display: flex;
}

.swiper-paginationn {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative !important;
}

.customscrollbar {
  background-color: #fff3;
  width: 100%;
  height: 3px;
  position: relative;
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background-color: #fff !important;
}

@media screen and (width >= 1920px) {
  .swiper-slide.specials_swiper_slide {
    width: 96%;
  }

  .specials-section {
  }
}

@media screen and (width <= 991px) {
  .specials-section {
    padding-top: .3125rem;
    padding-bottom: 7rem;
  }

  .swiper-slide.specials_swiper_slide {
    width: 100%;
  }

  .swiper-wrapper.specials_swiper_wrapper {
    margin-left: auto;
  }

  .special-stories-summary {
    max-width: 33rem;
  }

  .swiper-arrow-wrap {
    grid-column-gap: .8125rem;
    grid-row-gap: .8125rem;
    justify-content: flex-end;
    align-items: center;
    display: flex;
    inset: auto 2% -3% auto;
  }

  .controls-wrapper {
    margin-top: 2.875rem;
  }

  .controls-row {
    max-width: 13.25rem;
    position: absolute;
    inset: auto 17% 1% auto;
  }
}

@media screen and (width <= 767px) {
  .specials-section {
    padding-bottom: 6.25rem;
  }

  .swiper-slide.specials_swiper_slide {
    width: 100%;
  }

  .special-stories-slider-block {
    justify-content: flex-start;
    align-items: flex-end;
  }

  .special-stories-slider-block img {
  }

  .film-overlay-wrap {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-bottom: 0;
  }

  .remove-margin-bottom {
    width: 100%;
  }

  .controls-wrapper {
    margin-top: 6.25rem;
    padding-left: 1.25rem;
  }

  .controls-row {
    inset: auto auto 10% 3%;
  }

  .swiper-arrow-wrap {
    max-width: 100%;
    bottom: 3%;
    right: 5%;
  }

  .swiper-button-prev, .swiper-button-next {
    width: 2.8rem !important;
    min-width: 2.8rem !important;
    height: 2.8rem !important;
    min-height: 2.8rem !important;
  }
}

@media screen and (width <= 600px) {
  .specials-section {
    background-color: var(--black-dark);
  }

  .swiper-slide.specials_swiper_slide {
    height: auto;
  }

  .special-stories-slider-block img {
  }

  .film-overlay-wrap {
    padding-left: 1rem;
    padding-right: 1rem;
    position: relative;
  }

  .video-stories-slider-inner-block {
    max-width: 100%;
    grid-column-gap: .7rem;
    grid-row-gap: .7rem;
  }

  .special-stories-date-block {
    grid-column-gap: .7rem;
    grid-row-gap: .7rem;
    font-size: var(--desktop-font-size-normal--tag-14);
  }

  .heading-h2 {
    font-size: var(--mobile-font-heading--m-h2);
  }

  .controls-wrapper {
    max-width: 100%;
  }

  .controls-row {
    max-width: 11rem;
    left: 5%;
  }
}


/* [project]/src/styles/animatedParagraph.css [client] (css) */
.wrapper_base {
  padding: 152px 0;
  font-size: 1vw;
}

.wrapper_base.specialalign {
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.split_box {
  width: 60%;
  display: block;
  font-family: rocky, sans-serif;
}

.wrapper_base.specialalign .split_box {
  width: 60%;
}

.m_txt {
  font-size: 2.5em;
  line-height: 1.05;
}

.line {
  position: relative;
}

.line-mask {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #fff;
  height: 100%;
  z-index: 1;
}

.parabtn {
  position: relative;
  display: flex;
  justify-content: flex-start;
  margin-top: 24px;
}

@media screen and (width <= 991px) {
  .wrapper_base {
    padding: 112px 0;
  }

  .split_box, .split_box:lang(de-de) {
    width: 66%;
  }

  .wrapper_base.speciallign .split_box {
    width: 80%;
  }

  .m_txt.split-lines {
    font-size: 4em;
  }
}

@media screen and (width <= 767px) {
  .wrapper_base {
    padding: 72px 0;
  }

  .split_box {
    width: 76%;
  }

  .wrapper_base.specialalign .split_box {
    width: 76%;
  }

  .m_txt.split-lines {
    font-size: 4.4em;
  }
}

@media screen and (width <= 479px) {
  .wrapper_base {
    padding: 100px 16px;
  }

  .split_box {
    width: 100%;
  }

  .wrapper_base.specialalign .split_box {
    width: 100%;
  }

  .m_txt.split-lines {
    font-size: 5.9em;
  }
}


/* [project]/src/styles/cardGridSection.css [client] (css) */
.category_separate .sectioner {
  padding-bottom: 0;
}

.containerWrapper.category_separate {
  padding-bottom: 0;
}

.grid__Section_pt {
  padding-top: 50px;
}

@media only screen and (width >= 41.75rem) {
  .sectioner--featured-category {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

.sectioner--the-latest-posts {
  background-color: var(--background-color);
  transition: background-color .45s ease-in .5s;
}

.module__heading, .section-header {
  border-bottom: 1px solid #979797;
  border-left: 1px solid #979797;
  color: #000;
  margin-bottom: .9375rem;
  padding: 0 0 .3125rem .625rem;
  text-transform: uppercase;
}

.section-header {
  width: 100%;
  display: flex;
  align-items: center;
}

.sectioner--featured-category .section-header {
  display: block;
  border-bottom: none;
  border-left: none;
  text-align: center;
  padding: 0;
  margin-bottom: 50px;
  font-family: sweet-sans-pro, sans-serif;
}

.section-header__heading {
  letter-spacing: 1px;
  font-weight: 600;
}

.sectioner--featured-category .section-header .section-header__heading {
  position: relative;
  z-index: 1;
  font-size: var(--desktop-font-heading--d-h2);
}

.sectioner--featured-category .section-header .section-header__heading:before {
  border-top: .1px solid var(--text-color);
  content: "";
  margin: 0 auto;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: -1;
}

.sectioner--featured-category .section-header .section-header__heading a, .sectioner--featured-category .section-header .section-header__heading span {
  background-color: var(--body-bg-color);
  padding: 0 10px;
  font-size: 2rem;
  letter-spacing: 1px;
  line-height: 24px;
  color: var(--text-color);
}

.sectioner--the-latest-posts .section-header__date {
  font-size: 14px;
  letter-spacing: .35px;
  color: var(--gray-span);
  margin: 14px;
}

.the-latest__secondary-wrapper .the-latest__story:last-of-type {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

@media only screen and (width <= 41.6875rem) {
  .sectioner--featured-category {
    padding-left: 0;
    padding-right: 0;
  }

  .sectioner--featured-category .section-header .section-header__heading a, .sectioner--featured-category .section-header .section-header__heading span {
    font-size: 1.375rem;
    letter-spacing: 1px;
  }

  .sectioner {
    margin-bottom: 0;
  }
}

@media screen and (width <= 479px) {
  .sectioner--featured-category .section-header .section-header__heading {
    font-size: var(--mobile-font-heading--m-h2);
  }
}

.featured-category__secondary-wrapper {
  width: 100%;
}

@media only screen and (width >= 41.75rem) {
  .featured-category__secondary-wrapper {
    display: flex;
    justify-content: space-between;
  }
}

@media only screen and (width >= 61.25rem) {
  .featured-category__secondary-wrapper {
    flex-direction: column;
    width: calc(35% - 1.875rem);
  }
}

@media only screen and (width >= 92.5rem) {
  .featured-category__secondary-wrapper {
    width: calc(30% - .125rem);
  }
}

.the-latest__secondary-wrapper {
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
}

.article {
  display: block;
}

.featured-category__story {
  width: 100%;
  margin-bottom: 1.25rem;
  text-align: center;
}

.featured-category__story .post-meta {
  display: flex;
  flex-direction: row-reverse;
  align-items: flex-start;
  justify-content: center;
  padding-bottom: 35px;
}

.post-meta__timestamp {
  margin-left: 25px;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: .35px;
  color: var(--gray-span);
}

@media only screen and (width >= 41.75rem) {
  .featured-category__story {
    width: calc(50% - 1.25rem);
  }
}

@media only screen and (width >= 61.25rem) {
  .featured-category__story {
    width: 100%;
    margin-bottom: 0;
  }
}

@media only screen and (width >= 41.75rem) {
  .the-latest__secondary-wrapper .the-latest__story {
    width: calc(50% - 1.25rem);
  }
}

.featured-image {
  margin-bottom: .625rem;
}

.featured-category__secondary-wrapper .featured-image {
  position: relative;
}

.featured-category__secondary-wrapper .featured-image:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 56.25%;
}

.featured-image .image-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: block;
}

.featured-category__secondary-wrapper .featured-image .image-wrapper {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.featured-image img {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
}

.featured-image .image-wrapper:after {
  content: "";
  display: block;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.entry__category {
  color: #e02020;
  display: inline-block;
  letter-spacing: 1px;
  margin-bottom: .625rem;
  text-transform: uppercase;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
}

.featured-category__secondary-wrapper .entry__category {
}

.featured-category__story .entry__category {
  font-size: 11px;
  line-height: 15px;
}

@media only screen and (width >= 70.625rem) {
  .featured-category__story .entry__category {
    font-size: 15px;
    line-height: 22px;
  }
}

@media only screen and (width <= 61.1875rem) {
  .featured-category__secondary-wrapper .featured-image {
    max-width: 100%;
    min-width: 35%;
    overflow: hidden;
  }
}

@media only screen and (width <= 375px) {
  .featured-category__secondary-wrapper .featured-image {
    max-height: 90px;
  }
}

@media only screen and (width <= 41.6875rem) {
  .the-latest__secondary-wrapper .featured-image {
    max-width: 100%;
    min-width: 35%;
    overflow: hidden;
  }

  .grid__Section_pt {
    padding-top: 25px;
    padding-bottom: 0;
  }
}

@media only screen and (width <= 375px) {
  .the-latest__secondary-wrapper .featured-image {
    max-height: 90px;
  }
}

.entry__category a {
  color: inherit;
  text-decoration: none;
}

html, html a {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

.entry__heading {
  color: inherit;
  margin-bottom: .75rem;
  max-width: 100%;
  font-family: rocky, sans-serif;
  letter-spacing: .37px;
}

.category_space .entry__heading {
  margin-top: 1rem;
}

.featured-category__secondary-wrapper .entry__heading {
}

.featured-category__story .entry__heading {
  font-family: rocky, sans-serif;
  color: var(--text-color);
  font-size: 23px;
  line-height: 29px;
  letter-spacing: normal;
  transition: opacity .3s ease-in .5s;
}

.featured-category__story .entry__heading:hover {
  opacity: .8;
}

.home .entry__heading {
  transition: opacity .3s ease-in .5s;
}

.entry__heading a {
  color: inherit;
  text-decoration: none;
  transition: opacity .3s ease-in .5s;
}

.entry__heading a:hover {
  opacity: .7;
}

.post-meta {
  align-items: center;
  display: flex;
  font-family: sweet-sans-pro, sans-serif;
}

.featured-category__story .post-meta {
  justify-content: center;
  padding-bottom: 15px;
}

.sectioner--the-latest-posts .post-meta {
  flex-direction: row-reverse;
  align-items: flex-start;
  padding-bottom: 35px;
}

.latest-story--primary .post-meta {
  flex-direction: row-reverse;
  justify-content: center;
  align-items: flex-start;
}

.latest-story .post-meta {
  flex-direction: row-reverse;
  justify-content: flex-end;
  align-items: flex-start;
}

.grid__Section_dark .post-meta__author, .grid__Section_dark .post-meta__timestamp, .two-rivers-wrapper .post-meta__author, .two-rivers-wrapper .post-meta__timestamp {
  color: var(--gray-span) !important;
}

.post-meta__author {
  font-size: 14px;
  font-style: italic;
  color: var(--gray-span);
  order: 2;
  letter-spacing: 1px;
}

.post-meta__author a {
  color: inherit;
}

.featured-category__story .post-meta a {
  font-style: normal;
  text-transform: uppercase;
}

.post-meta__author .by_author {
  font-family: Georgia, sans-serif;
  line-height: 14px;
}

.post-meta__author .author_name {
  font-family: sweet-sans-pro, sans-serif;
  text-transform: uppercase;
  font-style: normal;
  font-weight: 600;
  line-height: 14px;
}

.post-meta__author a span {
  letter-spacing: .35px;
  font-style: normal;
  text-transform: uppercase;
}

.post-meta__author a span, .post-meta__author time {
}

.sectioner--the-latest-posts .post-meta__timestamp {
  margin-left: 25px;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: .35px;
  color: var(--gray-span);
}

@media only screen and (width <= 41.6875rem) {
  .featured-category__secondary-wrapper .featured-category__story {
    display: flex;
  }

  .featured-category__secondary-wrapper .entry {
    text-align: start;
    margin-left: 10px;
  }

  .the-latest__secondary-wrapper .entry {
    display: flex;
    flex-direction: column;
  }

  .the-latest__secondary-wrapper .entry .entry__category {
    order: 1;
    margin-bottom: 5px;
  }

  .featured-category__story .entry__heading {
    font-size: 17px;
    line-height: 22px;
  }

  .the-latest__secondary-wrapper .entry .entry__heading {
    order: 2;
    margin-bottom: 5px;
  }

  .category_space .entry__heading {
    margin-top: 0;
  }

  .featured-category__story .post-meta {
    padding-bottom: 0;
  }

  .the-latest__secondary-wrapper .post-meta {
  }

  .featured-category__secondary-wrapper .entry .post-meta {
    justify-content: flex-end;
    order: 3;
  }

  .featured-category__secondary-wrapper .entry .post-meta .post-meta__author {
    font-size: 12px;
  }

  .featured-category__secondary-wrapper .entry .post-meta .post-meta__timestamp {
    display: none;
  }
}

@media only screen and (width <= 41.6875rem) and (width <= 41.6875rem) {
  .featured-category__secondary-wrapper .featured-category__story {
    border-bottom: .0625rem solid #dddee4;
    padding-bottom: 1.25rem;
  }
}

@media only screen and (width <= 41.6875rem) {
  .the-latest__secondary-wrapper .the-latest__story {
    display: flex;
    justify-content: flex-start;
    text-align: left;
  }
}


/* [project]/src/styles/highlightSection.css [client] (css) */
.wrapper_base2 {
  font-size: 1vw;
}

.flexbox_wrapper {
  grid-column-gap: 6em;
  grid-row-gap: 6em;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.flexbox_wrapper.special_layout {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
}

.l_side_tall {
  width: 44%;
  margin-right: auto;
  padding-right: 3.9em;
  position: relative;
}

.headline_blog_home {
  grid-column-gap: 6px;
  grid-row-gap: 6px;
  flex-flow: column;
  margin-bottom: 24px;
  display: flex;
}

.article_headline {
  width: auto;
  font-size: 2.5em;
  line-height: 1.1;
  font-family: rocky, sans-serif;
  font-weight: 600;
}

.b_txt {
  letter-spacing: .02em;
  font-size: 1.1rem;
  line-height: 1.2;
  font-family: sweet-sans-pro, sans-serif;
}

.img_blog.r_side {
  width: 55.8%;
  margin-left: auto;
  overflow: hidden;
}

.highlightimage {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.button_base.black {
  transition: all .5s;
  border: none;
}

.button_base.black:hover {
  background-color: #323440e6;
}

@media screen and (width <= 1200px) {
  .l_side_tall {
    width: 50%;
  }

  .b_txt {
    font-size: 1.2em;
  }

  .button_base.black {
    font-size: 2vw;
  }
}

@media screen and (width <= 991px) {
  .l_side_tall {
    width: 50%;
    padding-right: 4em;
  }

  .article_headline {
    font-size: 4em;
  }

  .b_txt {
    letter-spacing: .03em;
    font-size: 1.6em;
  }

  .button_base {
    padding-top: 20px;
    padding-bottom: 18px;
  }

  .button_base.black {
    font-size: 2vw;
  }

  .img_blog.r_side {
    width: 50%;
  }
}

@media screen and (width <= 767px) {
  .flexbox_wrapper.special_layout {
    flex-flow: column-reverse;
  }

  .l_side_tall {
    width: 100%;
    position: static;
  }

  .article_headline {
    font-size: 6em;
  }

  .b_txt, .b_txt.white {
    font-size: var(--desktop-paragraph-font--small-p);
  }

  .button_base.black {
    font-size: 3vw;
  }

  .img_blog.r_side {
    width: 100%;
  }
}

@media screen and (width <= 479px) {
  .flexbox_wrapper.special_layout, .flexbox_wrapper.partnerships, .flexbox_wrapper.spec_part {
    flex-flow: column-reverse;
  }

  .l_side_tall {
    width: 100%;
    padding-right: 0;
    position: static;
  }

  .headline_blog_home {
    margin-bottom: 16px;
    margin-top: 16px;
  }

  .article_headline {
    font-size: 7.1em;
  }

  .b_txt {
    font-size: 4.5em;
    font-size: var(--desktop-paragraph-font--small-p);
  }

  .button_base.black {
    justify-content: center;
    align-items: center;
    width: 100%;
    font-size: 3.6vw;
    line-height: 1.5;
    display: flex;
  }

  .img_blog.r_side {
    width: 100%;
    margin-left: 0;
  }
}


/* [project]/src/styles/swiperCardSection.css [client] (css) */
.swipperCardsectionlatest {
  position: relative;
  padding: 5rem 0 0;
}

@media screen and (width <= 767px) {
  .swipperCardsectionlatest {
    padding: 2rem 0 0;
  }
}

.latestCardPostHeading {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.editor-picks__secondary-inner-wrapper {
  position: relative;
  overflow: hidden;
}

.editor-picks__secondary-pick {
  text-align: center;
  margin-top: 1.5625rem;
  position: relative;
}

.editor-picks__secondary-pick .featured-image {
  overflow: hidden;
  max-height: 260px !important;
}

.editor-picks__secondary-pick .featured-image img {
  aspect-ratio: 16 / 9;
}

.editor-picks__secondary-pick .entry__category {
  font-size: 11px;
  line-height: 15px;
}

.sectioner--editor-picks .entry {
  color: #000;
}

.editor-picks__secondary-pick .entry__heading {
  font-size: 23px;
  line-height: 29px;
}

.entry__heading {
  transition: opacity .3s ease-in;
}

.arrows_arrow {
  position: relative;
  z-index: 0;
  display: inline-flex;
  width: 45px;
  height: 45px;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1px solid #737373;
  transition: all .3s;
  cursor: pointer;
  overflow: hidden;
  color: #737373;
  background-color: #0000;
}

.arrows_arrow svg {
  position: relative;
  z-index: 1;
  font-size: 16px;
  transition: color .3s;
  opacity: 1 !important;
}

.arrows_arrow:hover {
  border: 2px solid #000;
}

.arrows_arrow:hover .arrows_arrow svg {
  color: #000;
  font-weight: 900;
}

@media only screen and (width >= 70.625rem) {
  .editor-picks__secondary-pick .entry__category {
    font-size: 15px;
    line-height: 22px;
  }
}


/* [project]/src/styles/categoryPage.css [client] (css) */
.categoryWrapper {
  color: var(--text-color);
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
  min-height: 60vh;
}

.categoryWrapper .featured-category__story .entry__heading {
  color: var(--text-color);
  transition: color .45s ease-in .5s;
}

.categoryWrapper .sectioner--featured-category .section-header .section-header__heading:before {
  border-top: .1px solid var(--body-bg-colorblackwhite);
  transition: border-top .45s ease-in .5s;
}

.categoryWrapper .sectioner--featured-category .section-header .section-header__heading a, .categoryWrapper .sectioner--featured-category .section-header .section-header__heading span {
  background-color: var(--body-bg-color);
  color: var(--text-color);
  transition: background-color .45s ease-in .5s, color .45s ease-in .5s;
}

section.tab_section {
  position: relative;
  padding: 40px 0;
  scroll-margin-top: 120px;
}

section.tab_section.noPadding {
  padding: 0;
}

.page_head_set {
  text-align: center;
  padding: 1vw 0 1.5vw;
  flex-direction: column;
  align-items: center;
  font-size: 1.25vw;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
}

.editionTitle {
  font-size: 3vw;
  line-height: 10vw;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.page_breadcrumb {
  padding-bottom: 2rem;
}

.page_breadcrumb ol li a {
  font-size: 16px;
}

.page_breadcrumb ol {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  list-style: none;
  font-size: 20px;
  padding: 0;
}

.page_breadcrumb ol li:after {
  content: ">";
  display: inline-block;
  position: relative;
  font-weight: 300;
  font-size: 14px;
  margin: 0 5px;
}

.page_breadcrumb ol li:last-child:after {
  content: "";
}

.search_bar {
  padding: 12px 30px;
  justify-content: space-between;
  background-color: var(--body-bg-colorblackwhite);
  color: var(--text-colorwhite);
  transition: background-color .45s ease-in .5s, color .45s ease-in .5s;
}

.search_bar, .search_bar_side, .filters_toggle {
  align-items: center;
}

.bar_input {
  width: 100%;
}

.flexCntr {
  display: flex;
}

.search_bar_side {
  mix-blend-mode: exclusion;
}

.bar_icon {
  position: relative;
  padding-right: 20px;
  border-right: 1px solid #ffffff4d;
}

.bar_icon, .col_image {
  margin-right: 20px;
}

.bar_input input {
  background: none;
  border: none;
  outline: none;
  appearance: none;
  color: #fff;
  width: 100%;
  min-width: 232px;
  font-size: 1rem;
}

input::selection {
  background: none;
}

.bar_input input:focus {
  outline: none;
  border: none;
  box-shadow: none;
  background-color: #0000 !important;
}

input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus {
  border: 1px solid #0000;
  -webkit-text-fill-color: #f1f1f1;
  -webkit-box-shadow: inset 0 0 0 1000px #0000;
  transition: background-color 5000s ease-in-out;
  background-color: #0000 !important;
}

.f_lable {
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 500;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
}

.filters_circle {
  width: 32px;
  height: 32px;
  background: #fff;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.filters_circle, .tab span a {
  margin-left: 20px;
}

.filters_circle i {
  width: 4px;
  height: 4px;
  background: #fff;
  mix-blend-mode: exclusion;
  margin: 1px;
  border-radius: 50%;
}

.search_bar, .search_bar_side, .filters_toggle {
  align-items: center;
}

.fixed-bar {
  position: sticky;
  top: 6.5rem;
  z-index: 99;
  width: 100%;
}

@media only screen and (width <= 61.1875rem) {
  .fixed-bar {
    top: 4.5rem;
  }
}

.fixed_item {
  z-index: 599;
  position: relative;
  transition: margin .2s cubic-bezier(.22, .61, .36, 1);
}

.page_bar {
  margin: 0 42px;
}

.tabs_bar {
  width: 100%;
  display: flex;
  align-items: center;
  color: #fff;
  background: #000000b3;
  background: #2a2a2a;
  height: 60px;
  overflow-x: hidden;
}

.tabs_bar::-webkit-scrollbar {
  display: none;
}

.tabs_bar .tab {
  width: 16.6667%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}

.tabs_bar .tab:before {
  position: absolute;
  content: "";
  width: 1px;
  top: 0;
  right: 0;
  bottom: 0;
  background: #ffffff68;
  z-index: 99;
}

.full_bg {
  height: 100%;
  width: 0%;
  background-color: #3f3f3f;
  transform-origin: 0;
  position: absolute;
  left: 0;
}

.tab_content {
  position: relative;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  padding: 20px 25px;
  font-family: sweet-sans-pro, sans-serif;
}

.tabs_bar .tab_content span {
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
  letter-spacing: 1px;
}

.tabs_bar .tab_content .f_16 {
  font-size: 16px;
  line-height: 20px;
}

.tabs_bar .tab_content .f_40 {
  font-weight: 500;
  text-transform: uppercase;
}

.tab span:not(.flex) {
  display: block;
}

.bar_icon svg {
  max-width: 100%;
  cursor: pointer;
}

@media screen and (width <= 1200px) {
  .tabs_bar {
    width: 100%;
  }

  .editionTitle {
    font-size: 4vw;
  }

  .tabs_bar .tab_content span {
    font-size: 13px;
  }
}

@media screen and (width <= 960px) {
  
}

@media screen and (width <= 800px) {
  .tab_content {
    padding: 10px 0;
  }

  .page_bar {
    margin: 0 10px;
  }

  .tabs_bar .tab_content span {
    font-size: 13px;
  }

  .page_head_set {
    margin: 0;
    font-size: 3vw;
  }
}

@media screen and (width <= 767px) {
  .tabs_bar .tab {
    width: 100%;
  }

  .editionTitle, .search_input input {
    font-size: 6vw;
    line-height: 19vw;
  }

  .line-height-10 {
    line-height: 10vw !important;
  }

  section.tab_section {
    padding: 2rem 0;
  }

  .page_head_set {
    margin: 0;
    font-size: 3vw;
  }

  .page_breadcrumb ol li a {
    font-size: 12px;
  }

  .categoryWrapper .featured-category__story .entry__heading {
    display: -webkit-box;
    max-width: 100%;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@media screen and (width <= 640px) {
  .search_bar {
    padding: 15px;
  }

  .filters_toggle .f_lable {
    display: none;
  }

  .bar_input input {
    min-width: 190px;
  }

  .tabs_bar {
    width: 100%;
  }

  .tabs_bar .tab {
    width: 100%;
  }

  .tabs_bar .tab_content span {
    font-size: 1rem;
  }
}

@media screen and (width <= 600px) {
  .page_head_set {
    margin: 0;
    font-size: 4vw;
  }

  .bar_input input {
    min-width: 100px;
    font-size: 16px;
  }

  .bar_icon, .col_image {
    margin-right: 5px;
    padding-right: 0;
  }

  .bar_icon {
    border: 0;
  }

  .bar_icon svg {
    max-width: 80%;
  }
}


/* [project]/src/styles/stories.css [client] (css) */
#story_wrapper {
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
}

.story_hero_hero_container {
  width: 100%;
  height: 100%;
  padding: 1rem 0;
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.story-hero-section .story-photoBy {
  padding-inline: .625rem;
  margin: 5px 0 0;
}

.story_hero_text_container {
  width: 100%;
  height: 83%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.story_hero_category {
  font-size: 1rem;
  color: red;
  font-weight: 500;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 1rem;
}

.story_hero_title {
  font-size: 2rem;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  width: 95%;
  line-height: 1;
  font-family: rocky, sans-serif;
  letter-spacing: .37px;
  margin-bottom: 2rem;
}

.story_hero_description {
  font-size: 1rem;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  opacity: .7;
  width: 75%;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 1.1;
  letter-spacing: .37px;
  margin-bottom: 30px;
}

.story_hero_info_container {
  width: 100%;
  height: 17%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  padding: 0 1rem;
  font-family: sweet-sans-pro, sans-serif;
  flex-wrap: wrap;
  letter-spacing: .37px;
}

.story_hero_info {
  display: inherit;
  white-space: pre;
  font-size: 1rem;
}

@media only screen and (width <= 767px) {
  .mob-py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}

@media only screen and (width >= 768px) {
  .story_hero_hero_container {
    height: 70vh;
    padding: 0;
  }
}

@media only screen and (width >= 92.5rem) {
  .story-main-wrapper > * {
    max-width: 100rem;
  }
}

.story_hero_image_container {
  width: 100%;
  height: auto;
  aspect-ratio: 16 / 9;
  background-color: gray;
  position: relative;
}

.story-photoBy {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin: 1px 0 20px;
  line-height: 1.35;
  text-align: left;
}

.story-caption {
  font-family: sweet-sans-pro, sans-serif;
  font-size: .875rem;
  letter-spacing: .37px;
  font-weight: 500;
  line-height: 1.1;
  opacity: .8;
  text-align: left;
  color: var(--text-color) !important;
}

.story-courtesy {
  font-family: rocky, sans-serif;
  font-size: .875rem;
  letter-spacing: 1px;
  text-align: left;
  color: var(--text-color) !important;
}

.story-main-wrapper .story-bannerSec {
  display: block;
  position: relative;
  width: 100vw;
  height: auto;
  margin-bottom: 20px;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-inline: 0 !important;
}

.story-main-wrapper .story-bannerSec .story-photoBy {
  padding: 0 20px;
}

.react-tweet-theme {
  margin-bottom: 0 !important;
}

.embed-frame iframe {
  height: 100% !important;
}

.embed-frame.embed-youtube {
  aspect-ratio: 16 / 9;
}

.instaembed {
  width: 100% !important;
}

.embed-frame.embed-instagram {
  height: auto;
  aspect-ratio: 9 / 16;
  margin: 0 auto;
}

.story-listStyle {
  padding-inline-start: 25px !important;
}

.d-flex {
  display: grid;
  grid-template-columns: 1fr;
}

.d-flex > :nth-child(3) {
  grid-column: 1 / -1;
}

.story-main-container p span {
  color: var(--text-color) !important;
}

@media (width <= 500px) {
  .story_hero_description {
    width: 95%;
  }

  .story_hero_info_container {
    gap: 20px;
  }

  .story_info_tag:nth-child(2) {
    order: 3;
  }

  .story_info_tag:nth-child(3) {
    order: 2;
  }
}

@media (width >= 768px) {
  .instaembed {
  }

  .embed-frame.embed-instagram {
    height: 100vh;
    aspect-ratio: 9 / 16;
    margin: 0 auto;
  }

  .story_hero_title {
    font-size: 4rem;
    line-height: 1.1;
    font-family: rocky, sans-serif;
    width: 75%;
  }

  .story_hero_description {
    font-size: 1.5rem;
    font-family: sweet-sans-pro, sans-serif;
    margin-bottom: 0;
  }

  .story_hero_info {
    font-size: 1.25rem;
    font-family: sweet-sans-pro, sans-serif;
  }

  .story_hero_info_container {
    padding: 0 2.5rem;
  }

  .d-flex {
    grid-template-columns: 3fr 1fr;
  }
}

@media (width <= 768px) {
  .d-flex {
    display: flex;
    flex-direction: column;
  }

  .submenu-body.d-flex {
    flex-direction: column-reverse !important;
  }

  #about_wrapper .d-flex {
    flex-direction: column-reverse;
  }

  .d-flex > :last-child {
    order: 2;
  }

  .d-flex > :nth-last-child(2) {
    order: 3;
  }

  .story-main-wrapper {
    border: none;
  }
}

.story_main_classname_container {
}

.story-main-container {
}

.story-side-container {
}

.story-side-wrapper {
  padding: 0 0;
}

.story_main_classname_main {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}

.story-main-container h2 {
  font-size: 1.5rem;
  line-height: 1.1;
  font-family: rocky, sans-serif;
}

.story-main-container h2 span, .story-main-container h3 span, .story-main-container h4 span, .story-main-container h5 span, .story-main-container h6 span {
  color: var(--text-color) !important;
}

.story-main-container a, .story-main-container a > span {
  position: relative;
  color: red !important;
}

.story-main-container a:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 1px;
  transition: width .45s;
  background-color: red !important;
}

.story-main-container a:hover:after {
  width: 100%;
}

@media only screen and (width >= 768px) {
  .story_main_classname_main {
    flex-direction: row;
  }

  .story-side-wrapper {
    padding: 0 1rem;
    padding-left: 2rem;
  }

  .story-main-container h2 {
    font-size: 2rem;
    line-height: 1.1;
  }
}

.story_main_classname_left {
  width: 100%;
  padding: 20px 0;
}

@media (width >= 768px) {
  .story_main_classname_left {
    width: 72%;
    padding: 40px 40px 40px 0;
  }
}

.story-main-wrapper {
  width: 100%;
  height: 100%;
  font-size: 20px;
  text-align: justify;
  font-family: Georgia, sans-serif;
  line-height: 1.3;
  font-weight: 100;
  padding: 1rem 0;
}

.story-main-wrapper p {
  font-weight: 300;
  margin-bottom: 1.5rem;
  line-height: 26px;
  font-size: 16px;
  font-family: Georgia, sans-serif;
  color: var(--text-color);
}

@media (width >= 768px) {
  .story-main-wrapper {
    font-size: 27px;
    font-size: 1.4rem;
    line-height: 1.2;
    border-right: 1px solid var(--line-color);
    padding: 0 1rem;
    padding-right: 2rem;
  }

  .story-main-wrapper p {
    font-size: 18px;
    line-height: 28px;
    letter-spacing: .37px;
  }
}

.story_main_classname_imageWrapper {
  width: 100%;
  background: #ccc;
  position: relative;
  margin: 3rem 0;
  aspect-ratio: 16 / 9;
}

.story_main_classname_divider {
  display: none;
}

@media (width >= 768px) {
  .story_main_classname_divider {
    display: block;
    width: 1px;
    background: #a4a4a475;
  }
}

.story_main_classname_right {
  width: 100%;
  padding: 20px 0;
}

@media (width >= 768px) {
  .story_main_classname_right {
    width: 28%;
    padding: 40px 0 40px 10px;
  }
}

@media (width >= 1200px) {
  .story_main_classname_right {
    width: 28%;
    padding: 40px 0 40px 40px;
  }
}

.story_main_classname_card {
  height: 65vh;
  display: flex;
  flex-direction: column;
}

.story_main_classname_cardImage {
  width: 100%;
  object-fit: cover;
}

.story_main_classname_img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story_main_classname_cardText {
  height: 10%;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 500;
  padding: 10px;
  font-family: rocky, sans-serif;
  line-height: 1;
  color: var(--text-colorblack);
}

.story_main_classname_alsoInteresting {
  margin-bottom: 20px;
  padding: 10px;
  font-weight: 500;
  position: relative;
  text-align: center;
}

.story_main_classname_alsoInteresting span {
  background-color: var(--body-bg-color);
  padding: 0 10px;
  font-size: 1.4rem;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
  color: var(--text-color);
  position: relative;
  z-index: 2;
}

#about_wrapper .story_main_classname_alsoInteresting span {
  background-color: var(--body-bg-color);
  color: var(--text-color);
  position: relative;
  position: relative;
  z-index: 2;
}

.story_main_classname_alsoInteresting:before {
  border-top: .1px solid var(--line-color);
  content: "";
  margin: 0 auto;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: 1;
}

#about_wrapper .story_main_classname_alsoInteresting:before {
  border-top: .1px solid var(--line-color);
  z-index: 1;
}

.story_main_classname_advertisement {
  margin-top: 20px;
  height: 60vh;
  width: 100%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: sweet-sans-pro, sans-serif;
  color: gray;
}

.story_main_classname_slider {
  padding: 10px 0;
}

.relatedWrapper {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 0;
}

#related-stories-section {
  padding-top: 0;
}

@media (width >= 668px) {
  .relatedWrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .relatedWrapper > .featured-category__story {
    width: 100%;
  }
}

@media (width >= 768px) {
  #related-stories-section {
    padding-top: 40px;
  }

  .story_main_classname_slider {
    padding: 40px 0;
  }
}

@media (width >= 980px) {
  .relatedWrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  .relatedWrapper > .featured-category__story {
    width: 100%;
  }
}

@media (width >= 1200px) {
  .story_main_classname_slider {
    padding: 10px 0;
  }
}

.story_main_classname_social {
  padding: 10px 0;
  margin-top: 5px;
}

@media (width >= 768px) {
  .story_main_classname_social {
    margin-top: 5vh;
    margin-bottom: 5vh;
  }
}

@media (width >= 1200px) {
  .story_main_classname_social {
    padding: 10px 0;
  }
}

.story_main_classname_footer {
  width: 100%;
}

@media (width >= 768px) {
  .story_main_classname_footer {
    display: flex;
  }
}

.story_interesting_classname_container {
  width: 100%;
  margin-bottom: 10px;
}

.story_interesting_classname_card {
  width: 100%;
}

.story_interesting_classname_imageWrapper {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  overflow: hidden;
}

.story_interesting_classname_image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story_interesting_classname_text {
  width: 100%;
  min-height: 10vh;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  margin-top: .625rem;
}

.story_interesting_classname_heading {
  font-size: 1rem;
  color: red;
  font-weight: 500;
  font-family: sweet-sans-pro, sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.story_interesting_classname_description {
  font-size: 1.4rem;
  color: var(--text-color);
  font-family: rocky, sans-serif;
  line-height: 1.2;
}

#about_wrapper .story_interesting_classname_description {
  color: var(--text-color);
  line-height: 1.2;
}

.story_swiper_classname_container {
  position: relative;
  width: 100%;
  overflow: hidden;
  margin: 0 auto;
}

.story_swiper_classname_swiper {
  width: 100%;
  aspect-ratio: 1;
}

@media (width >= 768px) {
  .story_swiper_classname_swiper {
    height: 50vh;
  }
}

.story_swiper_classname_slide {
  position: relative;
  width: 100%;
  height: 100%;
}

.story_swiper_classname_image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.story_swiper_classname_controls {
  position: relative;
  width: 100%;
  height: 10vh;
  display: flex;
  align-items: center;
  padding-top: 1rem;
  font-family: sweet-sans-pro, sans-serif;
}

.story_swiper_classname_buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.story_swiper_classname_arrow {
  background: none;
  border: none;
  cursor: pointer;
}

.story_swiper_classname_icon {
  font-size: 24px;
  color: #000;
  background-color: #fff;
}

.story_swiper_classname_counter {
  font-size: 1.2rem;
  margin-left: 1rem;
  letter-spacing: 1px;
}

.story_social_container {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 0;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
}

.story_social_wrapper {
  width: 100%;
  font-family: sweet-sans-pro, sans-serif;
}

.story_social_section {
  width: 100%;
  min-height: 5vh;
  padding: 10px 0;
  border-top: 1px solid var(--line-color);
  display: flex;
  flex-direction: column;
}

@media (width >= 768px) {
  .story_social_section {
    min-height: 10vh;
    flex-direction: row;
    align-items: center;
  }
}

.story_social_label {
  width: 100%;
  min-height: 5vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-transform: uppercase;
  font-size: 12px;
}

@media (width >= 768px) {
  .story_social_label {
    width: 15%;
    font-size: 16px;
  }
}

.story_social_buttons {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

@media (width >= 768px) {
  .story_social_buttons {
    width: 85%;
  }
}

.story_social_author {
  width: 100%;
  text-transform: uppercase;
  font-size: 12px;
  line-height: 1.1;
}

@media (width >= 768px) {
  .story_social_author {
    width: 85%;
    font-size: 16px;
  }
}

.story_social_bio {
  display: flex;
  align-items: center;
  gap: 5px;
  text-transform: none;
  font-size: 1.2rem;
}

.border_btn_button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.border_btn_button svg {
  font-size: 1.2rem;
}

.border_btn {
  display: inline-block;
}

.border_btn_button {
  padding: 4px 8px;
  font-size: 12px;
  text-transform: uppercase;
  border: 1px solid var(--text-color);
  color: var(--text-color);
  background-color: #0000;
  transition: all .3s;
  cursor: pointer;
}

.border_btn_button:hover {
  background-color: var(--text-color);
  color: var(--body-bg-color);
  border-color: var(--body-bg-color);
}

@media (width >= 768px) {
  .border_btn_button {
    padding: 8px 16px;
    font-size: .875rem;
  }
}

.container_fullImage {
  max-width: 100vw;
  flex: 1;
  margin: 0 auto;
  position: relative;
}

.full-image-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.full-image-wrapper img {
  width: 100%;
  height: auto;
  object-fit: cover;
  object-position: center;
}

.share__modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #00000080;
  transform: scale(1.1);
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear .3s, opacity .3s, transform .3s;
  z-index: 9999;
}

.share-close-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  cursor: pointer;
  font-size: 23px;
  color: #000;
}

.share__modal.show {
  opacity: 1;
  pointer-events: all;
  visibility: visible;
  transform: scale(1);
  transition: visibility linear, opacity .25s, transform .25s;
}

.share__content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  width: 30rem;
  padding: 30px;
  border-radius: 10px;
  text-align: left;
  box-shadow: 0 4px 6px #0000001a;
}

.share_body {
  display: flex;
  flex-direction: column;
  gap: 30px;
  position: relative;
}

.share_body h2 {
  font-size: 2.1rem;
  color: #000;
  text-align: center;
  line-height: 1;
  font-family: rocky, sans-serif;
  font-weight: 400;
}

.share__icons {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

.share_body h3 {
  color: gray;
  font-weight: 300;
  font-size: 1rem;
  font-family: sweet-sans-pro, sans-serif;
  margin-bottom: .5rem;
}

#check-group {
  animation: .32s ease-in-out 1.03s check-group;
  transform-origin: center;
}

#check-group #check {
  animation: .34s cubic-bezier(.65, 0, 1, 1) .8s forwards check;
  stroke-dasharray: 0 75;
  stroke-linecap: round;
  stroke-linejoin: round;
}

#check-group #outline {
  animation: .38s ease-in outline;
  transform: rotate(0);
  transform-origin: center;
}

#check-group #white-circle {
  animation: .35s ease-in .35s forwards circle;
  transform: none;
  transform-origin: center;
}

.copy_text_anim {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-family: sweet-sans-pro, sans-serif;
  color: var(--text-colorblack);
}

.StoriesInfo_left_innercntr-full-width.embed-twitter {
  position: relative;
  aspect-ratio: 16 / 9;
}

.flex-all-embed {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes outline {
  from {
    stroke-dasharray: 0 345.576;
  }

  to {
    stroke-dasharray: 345.576 345.576;
  }
}

@keyframes circle {
  from {
    transform: scale(1);
  }

  to {
    transform: scale(0);
  }
}

@keyframes check {
  from {
    stroke-dasharray: 0 75;
  }

  to {
    stroke-dasharray: 75 75;
  }
}

@keyframes check-group {
  from {
    transform: scale(1);
  }

  50% {
    transform: scale(1.09);
  }

  to {
    transform: scale(1);
  }
}

.share__icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f3f3f3;
  border-radius: 50%;
  width: 58px;
  height: 58px;
  color: #333;
  transition: background .3s, color .3s;
}

.share__icon svg {
  font-size: 22px;
}

.share__icon:hover {
  background: #000;
  color: #fff;
}

.share__icon button {
  all: unset;
  cursor: pointer;
}

.link_copycntr {
  width: 100%;
  padding: 10px;
  background-color: #f3f3f3;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.link_copycntr input {
  width: 100%;
  background: none;
  outline: none;
  border: none;
  font-family: sweet-sans-pro, sans-serif;
  color: var(--text-colorblack);
}

.link_copycntr svg {
  font-size: 24px;
  cursor: pointer;
  color: var(--text-colorblack);
}

@media only screen and (width <= 992px) {
  .share-close-btn {
    top: -15px;
    right: -15px;
  }

  .card-meta_meta {
    row-gap: 16px;
  }

  .contr-fluid {
    gap: 1em;
  }

  .Stories_caption {
    font-size: .875rem;
  }

  .Stories_courtesy {
    font-size: .875rem;
  }
}

@media only screen and (width <= 767px) {
  .share__content {
    width: 85vw;
  }

  .share_body h2 {
    font-size: 2rem;
  }

  .share__icon {
    width: 40px;
    height: 40px;
  }

  .share__icon svg {
    font-size: 18px;
  }

  .share__icons {
    justify-content: space-around;
  }
}

@media only screen and (width <= 425px) {
  .share_body h2 {
    font-size: 1.5rem;
  }
}


/* [project]/src/styles/navbar.css [client] (css) */
.nav {
  position: relative;
  padding: 2rem 3rem;
  color: var(--text-color);
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s, color .45s ease-in .5s;
  position: sticky;
  top: 0;
  z-index: 999;
}

.brand {
  cursor: pointer;
  justify-self: left;
  font-size: 2vw;
  font-family: rocky, sans-serif;
  display: flex;
}

.brand img {
  width: 200px;
  height: auto;
  filter: var(--filterblack);
  transition: filter .45s ease-in .5s;
}

.nav-content {
  align-items: center;
  display: flex;
  justify-content: space-between;
  height: 2.5rem;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 1;
  overflow: hidden;
}

.links.nav-items {
  cursor: pointer;
  text-transform: uppercase;
  color: var(--text-color);
  transition: color .45s ease-in;
  display: none;
}

.links.nav-items ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.links.nav-items .menu-linksul {
  display: flex;
  text-transform: uppercase;
  width: 100%;
  flex-direction: column;
  padding: .625rem;
}

.links.nav-items li:not(:last-of-type) {
  margin-bottom: .625rem;
}

.links.nav-items .menu-linksul a {
  font-size: 1vw;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 23px;
  letter-spacing: .08rem;
}

.menu {
  display: none;
  font-family: sweet-sans-pro, sans-serif;
  transition: color .45s ease-in;
}

.toggle_cntr {
  position: relative;
  cursor: pointer;
  transition: all .3s;
  width: 40px;
  height: 40px;
}

.toggle_cntr:hover {
  scale: .92;
}

.toggle_cntr svg {
  transition: all .3s;
}

.icon {
  display: none;
  transition: transform .45s;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 8px;
  border: 1px solid #000;
  font-size: 1rem;
  cursor: pointer;
  background-color: #fff;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
}

.toggle_cntr .icon:hover svg {
  rotate: 25deg;
}

.icon.scale-0 {
  transform: scale(0);
}

.icon.scale-1 {
  transform: scale(1);
}

.menu-item {
  position: relative;
}

.nav_active {
  position: relative;
}

.menu-item a:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 1px;
  background-color: red;
  transition: width .45s;
}

.menu-item a.nav_active:after {
  width: 100% !important;
}

.menu-item a:hover:after {
  width: 100%;
}

@media only screen and (width >= 61.25rem) {
  .nav {
    padding: 2rem;
    border-bottom: none;
  }

  .links.nav-items {
    display: flex;
    flex-basis: 80%;
  }

  .links.nav-items .menu-linksul {
    align-items: center;
    justify-content: center;
    flex-direction: row;
    padding: 0;
    gap: 1.5rem;
  }

  .links.nav-items li:not(:last-of-type) {
    margin: 0;
  }

  .brand img {
    width: 200px;
    height: auto;
  }

  .toggle_cntr .icon svg {
    font-size: 25px;
  }

  .toggle_cntr .icon:first-child svg {
    font-size: 30px;
  }
}

@media only screen and (width <= 61.1875rem) {
  .nav {
    padding: 1rem;
  }

  .menu {
    display: flex;
  }

  .brand img {
    width: 160px;
    height: auto;
  }

  .brand {
    order: 2;
  }

  .material-icons.menu {
    font-size: 25px;
    cursor: pointer;
    line-height: 1;
    order: 3;
  }

  .toggle_cntr svg {
    font-size: 25px;
  }

  .toggle_cntr {
    order: 1;
    justify-content: flex-start;
  }
}

@media screen and (width <= 1200px) {
  .links.nav-items .menu-linksul a {
    font-size: 1.2vw;
  }
}

@media only screen and (width <= 768px) {
  
}


/* [project]/src/styles/footer.css [client] (css) */
.footer {
  background-color: var(--black);
}

.wrapper_footer {
  padding-top: 0;
  padding-bottom: 2.5rem;
}

.dest_news {
  border-bottom: 1px solid #f1f1f126;
  width: 100%;
  display: flex;
}

.dest_ft {
  border-right: 1px solid #f1f1f126;
  width: 50%;
  height: 100%;
}

.btn_wrapper {
  border-top: 1px solid #f1f1f126;
  padding: 16px 32px;
  border-bottom: 1px solid #f1f1f126;
}

.btn_wrapper.expobtn {
  padding: 16px 32px 16px 0;
}

.btn_wrapper.subbtn {
  padding: 16px 0 16px 16px;
}

.news_ft {
  width: 50%;
  height: 100%;
}

.dt_nw_wrapper {
  flex-flow: column;
  width: 100%;
  height: 100%;
  display: flex;
}

.footer_button {
  background-color: var(--white);
  color: var(--black);
  letter-spacing: 1px;
  border-radius: 2px;
  justify-content: center;
  align-items: center;
  width: auto;
  height: 52px;
  margin-left: auto;
  padding: 8px 62px;
  transition: all .4s;
  display: flex;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 600;
  font-size: 1rem;
}

.w-form {
  margin: 0 0 15px;
}

.form-block {
  margin-bottom: 0;
}

.subscribe_flex {
  justify-content: space-around;
  display: flex;
}

.w-form-done {
  text-align: center;
  background-color: #ddd;
  padding: 20px;
  display: none;
}

.subscribe_thanks {
  background-color: var(--white);
  border-radius: 2px;
}

.w-form-fail {
  background-color: #ffdede;
  margin-top: 10px;
  padding: 10px;
  display: none;
}

.error_state {
  letter-spacing: 1px;
  background-color: #fc5656;
  border-radius: 3px;
  width: 80%;
  margin-top: 12px;
  margin-left: 0;
  margin-right: 0;
  padding: 7px 12px 6px;
  font-size: .9em;
  line-height: 1.2;
  position: static;
}

.w-input, .w-select {
  color: #333;
  vertical-align: middle;
  background-color: #fff;
  border: 1px solid #ccc;
  width: 100%;
  height: 38px;
  margin-bottom: 10px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.42857;
  display: block;
}

.field_prefooter {
  color: var(--white);
  letter-spacing: .03em;
  background-color: #fff0;
  border: 1px solid #f1f1f140;
  border-radius: 3px;
  width: 100%;
  height: 52px;
  margin-bottom: 0;
  margin-right: 8px;
  padding: 8px 16px 4px;
  transition: all .4s;
}

input.w-button {
  -webkit-appearance: button;
  cursor: pointer;
}

.footer_links {
  padding-top: 0;
}

.wrapper_footer_links {
  padding-top: 32px;
}

.flexbox_footer {
  display: flex;
  font-family: sweet-sans-pro, sans-serif;
}

.footer-left-block {
  width: 50%;
}

.footer-right-block {
  width: 50%;
  display: flex;
  justify-content: space-between;
}

.div-block-2 {
  width: 100%;
}

.title_footer {
  opacity: .65;
  color: var(--white);
  text-transform: uppercase;
  margin-bottom: 16px;
  font-size: .9em;
  letter-spacing: 1px;
}

.links_flex {
  grid-column-gap: 4px;
  grid-row-gap: 4px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.footer_link {
  color: var(--white);
  letter-spacing: 1px;
  font-size: .9em;
  transition: all .4s;
}

.last_line {
  margin-top: 8em;
}

.flexbox_line {
  display: flex;
  justify-content: space-between;
  font-family: sweet-sans-pro, sans-serif;
}

.left_rights {
  width: 30%;
  margin-right: 0;
}

.footer_link:hover, .footer_link.just_rights {
  opacity: .65;
}

.privacy_box {
  width: 25%;
  margin-right: 0;
  display: flex;
  grid-column-gap: 15px;
  grid-row-gap: 15px;
  justify-content: flex-end;
}

.website_by {
  justify-content: flex-end;
  align-items: flex-start;
  width: 25%;
  margin-left: auto;
  display: flex;
}

.footer_link.web_by {
  opacity: .65;
}

.footer-brand {
  position: relative;
  width: 280px;
  height: 55px;
  overflow: hidden;
}

.footer-brand img {
  object-fit: cover;
  filter: invert();
  transition: filter .45s ease-in .5s;
}

.sub-footer {
  background-color: var(--gray-bg);
}

.sub-footer > .containerWrapper {
  padding: 10px 0;
}

.flexbox_sub-footer {
  display: flex;
  align-items: center;
  font-family: sweet-sans-pro, sans-serif;
}

.footer-tag-line {
  color: var(--white);
  letter-spacing: 1px;
  font-size: .8em;
  transition: all .4s;
}

.footer-vr-line {
  display: inline-block;
  align-self: stretch;
  background: #fff;
  width: 1px;
  height: 100%;
  opacity: .7;
  margin: auto 15px;
  min-height: 2.75em;
}

.sub-footer-img-block {
  position: relative;
  width: 180px;
  min-width: 180px;
  height: 5vw;
  overflow: hidden;
}

.sub-footer-img-block-2 {
  position: relative;
  width: 80px;
  min-width: 80px;
  height: 5vw;
  overflow: hidden;
}

.sub-footer-img-block img, .sub-footer-img-block-2 img {
  object-fit: contain;
}

@media screen and (width <= 991px) {
  .btn_wrapper {
    padding-left: 24px;
    padding-right: 24px;
  }

  .footer_button {
    padding-left: 32px;
    padding-right: 32px;
    font-size: 1em;
  }

  .field_prefooter {
    font-size: 1em;
  }

  .subscribe_thanks {
    border-radius: 2px;
  }

  .error_state {
    padding-top: 10px;
    font-size: 1.5em;
  }

  .thanks_txt {
    font-size: 1.5em;
    line-height: 1.1;
  }

  .wrapper_footer_links {
    padding-top: 32px;
    padding-left: 0;
    padding-right: 0;
  }

  .title_footer {
    letter-spacing: 1px;
    font-size: .9em;
  }

  .footer_link {
    font-size: 1em;
  }

  .last_line {
    margin-top: 25em;
  }

  .privacy_box {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    width: 50%;
    display: flex;
  }

  .website_by {
    width: 20%;
  }
}

@media screen and (width <= 767px) {
  .wrapper_footer {
    padding-bottom: 20px;
  }

  .dest_news {
    flex-flow: column;
    height: auto;
  }

  .dest_ft, .news_ft {
    width: 100%;
  }

  .btn_wrapper {
    padding-left: 20px;
    padding-right: 20px;
  }

  .footer_button {
    font-size: .9em;
  }

  .field_prefooter {
    font-size: .8em;
  }

  .subscribe_thanks {
    border-radius: 2px;
  }

  .error_state {
    font-size: 2em;
  }

  .thanks_txt {
    font-size: 1em;
  }

  .wrapper_footer_links {
    padding-top: 32px;
    padding-left: 0;
    padding-right: 0;
  }

  .flexbox_footer {
    flex-direction: column;
  }

  .footer-left-block {
    width: 100%;
    margin-bottom: 30px;
  }

  .footer-right-block {
    width: 100%;
    display: grid;
    justify-content: flex-start;
    grid-column-gap: 0px;
    grid-row-gap: 52px;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
  }

  .sub-footer-img-block, .sub-footer-img-block-2 {
    width: 100%;
  }

  .footer-hr-line {
    display: inline-block;
    align-self: stretch;
    background: #fff;
    width: 100%;
    height: 1px;
    opacity: .7;
    margin: 15px auto;
  }

  .flexbox_sub-footer {
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
  }

  .sub-footer-img-block {
    order: 1;
    width: calc(50% - 5.5px);
    min-width: calc(50% - 5.5px);
    height: 50px;
  }

  .footer-vr-line:first-of-type {
    order: 2;
    height: 100%;
    width: 1px;
    margin: 0;
  }

  .sub-footer-img-block-2 {
    order: 3;
    width: calc(50% - 5.5px);
    min-width: calc(50% - 5.5px);
    height: 50px;
  }

  .footer-vr-line:last-of-type {
    order: 4;
    width: 100%;
    height: 1px;
    min-height: 1px;
    margin: 15px auto;
  }

  .footer-tag-line {
    order: 5;
    width: 100%;
  }

  .title_footer {
    letter-spacing: 1px;
    font-size: 1em;
  }

  .links_flex {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
  }

  .footer_link {
    letter-spacing: 1px;
    padding-top: .2em;
    font-size: 1em;
  }

  .last_line {
    margin-top: 20em;
  }

  .flexbox_line {
    flex-flow: column;
  }

  .privacy_box {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    width: 100%;
    margin-top: 8px;
    display: flex;
    justify-content: flex-start;
  }

  .website_by {
    justify-content: flex-start;
    align-items: flex-start;
    width: 50%;
    margin-top: 32px;
    margin-left: 0;
  }

  .footer_link.web_by {
    padding-top: .2em;
  }
}

@media screen and (width <= 479px) {
  .footer-brand {
    position: relative;
    width: 180px;
    height: 40px;
    overflow: hidden;
  }

  .div-block-3 {
    width: 100%;
  }

  .left_rights {
    width: 100%;
  }

  .wrapper_footer_links {
    padding-top: 32px;
    padding-left: 0;
    padding-right: 0;
  }

  .flexbox_footer {
    flex-direction: column;
  }

  .footer-left-block {
    width: 100%;
    margin-bottom: 30px;
  }

  .footer-right-block {
    width: 100%;
    display: grid;
    justify-content: flex-start;
    grid-column-gap: 20px;
    grid-row-gap: 32px;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
  }

  .sub-footer-img-block, .sub-footer-img-block-2 {
    height: 40px;
  }

  .last_line {
    margin-top: 1em;
  }

  .website_by {
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    margin-top: 52px;
    margin-left: 0;
  }

  .footer_link.web_by {
    text-decoration: underline;
  }

  .btn_wrapper.subbtn {
    padding: 16px 0;
  }

  .btn_wrapper.expobtn {
    padding: 16px 0;
  }

  .title_footer {
    font-size: .9em;
  }

  .footer_link {
    font-size: .9em;
  }
}


/* [project]/src/styles/mobileMenu.css [client] (css) */
.menu-cont {
  position: fixed;
  top: 0;
  height: 100dvh;
  width: 100vw;
  z-index: 999;
  transition: all .4s;
  display: none;
}

.menu-inner {
  width: 100%;
  margin-left: auto;
  background-color: #000;
}

.manu-inner-block, .menu-inner {
  height: 100dvh;
  transition: all .3s ease-out;
}

.manu-inner-block {
  width: 100%;
}

.menu-top {
  display: flex;
  height: 80px;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  padding: 3vh 4vw;
}

.flex-all {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.menu-back {
  position: relative;
  width: 40px;
  height: 40px;
  font-size: 20px;
  background-color: #fff;
  transition: all .3s;
  border-radius: 50%;
}

.menu-close {
  width: 40px;
  height: 40px;
  font-size: 19px;
  background-color: #fff;
  transition: all .3s;
  border-radius: 50%;
}

.menu-close svg {
  color: #000 !important;
}

.menu-back svg {
  color: #000 !important;
}

.menu-main {
  position: relative;
  flex-direction: column;
  align-items: normal;
  height: calc(100dvh - 140px);
  width: 100%;
  overflow: auto;
  padding: 3vh 4vw;
}

.menu-extras, .menu-main {
  display: flex;
  justify-content: space-between;
}

.menu-items {
  height: 50px;
  color: #fff;
  width: 100%;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.menu-name {
  font-size: 1.25rem;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
  width: 100%;
  display: inline-block;
}

.menu-arrow {
  position: absolute;
  right: 0;
  font-size: 18px;
  transition: all .3s;
}

.menu-extras {
  align-items: flex-end;
}

.menu-ext {
  display: flex;
  align-items: center;
  color: #ffffffe6;
  font-size: 16px;
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 400;
  letter-spacing: 1px;
  margin: 8px 0;
  transition: all .3s;
}

.menu-btm {
  width: 100%;
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
  margin: auto;
  gap: 25px;
  padding: 0 4vw;
  border-top: 1px solid #fff;
}

.menu-follows {
  margin-left: 10px;
}

.menu-follows .menu-follows-items {
  display: flex;
  align-items: center;
  gap: 10px;
}

.menu-follows .menu-follows-items a {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
}

.menu-follows .menu-follows-items svg {
  font-size: 18px;
}

.menu-follows-text {
}

.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.submenu-head, .submenu-items {
  color: #fff;
  width: 100%;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.submenu-head {
}

.submenu-items {
  margin-bottom: 16px;
}

.submenu-name, .submenu-title {
  font-family: sweet-sans-pro, sans-serif;
  font-weight: 500;
  letter-spacing: 1px;
  line-height: 1.5;
  text-transform: uppercase;
}

.submenu-name {
  font-size: 20px;
  gap: 15px;
  width: 100%;
  display: inline-block;
  color: #e0e0e0bf !important;
}

.submenu-title {
  font-size: 26px;
  width: 100%;
  line-height: 1.2;
  margin-bottom: 20px;
  color: #fff !important;
}

@media (width <= 61.1875rem) {
  .menu-cont {
    display: flex;
  }
}


/* [project]/src/styles/herosectionbanner.css [client] (css) */
.fullWidthCntr {
  position: relative;
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
}

.editor-picks__primary-pick {
  width: 100%;
  padding: 1.25rem;
  padding-bottom: 0;
  text-align: center;
  position: relative;
}

.editor-picks__primary-pick .post-meta {
  display: flex;
  flex-direction: row-reverse;
  align-items: flex-start;
  justify-content: center;
  padding-bottom: 35px;
}

.editor-picks__primary-pick .featured-image {
  position: relative;
  margin-bottom: 1.25rem;
  width: 100%;
  aspect-ratio: 16 / 9;
  overflow: hidden;
}

.featured-image a {
  position: relative;
  height: 100%;
  width: 100%;
  display: block;
}

.editor-picks__primary-pick .featured-image img {
  object-fit: cover;
}

.editor-picks__primary-pick .entry {
  padding: .9375rem 2.8125rem;
  padding-bottom: 0;
  width: 100%;
  text-align: center;
  background-color: var(--body-bg-color);
  transition: background-color .45s ease-in .5s;
  margin-top: -50px;
  position: relative;
  z-index: 99;
  margin-left: auto;
  margin-right: auto;
}

.editor-picks__primary-pick .entry__category {
  font-size: 11px;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 15px;
}

.editor-picks__primary-pick .entry__heading {
  font-size: 23px;
  line-height: 28px;
  text-transform: none;
  margin-bottom: .625rem;
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  font-family: rocky, sans-serif;
}

.editor-picks__primary-pick .entry__excerpt {
  color: var(--text-color);
  transition: color .45s ease-in .5s;
  font-family: sweet-sans-pro, sans-serif;
  margin-bottom: 1.25rem;
  width: 100%;
  letter-spacing: .37px;
}

.editor-picks__primary-pick .entry__excerpt {
  font-size: 19px;
  font-family: sweet-sans-pro, sans-serif;
  margin-bottom: .625rem;
  font-weight: 300;
  line-height: 1.1;
}

@media only screen and (width >= 61.25rem) {
  .editor-picks__primary-pick {
    padding: 0;
    text-align: left;
  }

  .editor-picks__primary-pick .featured-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 9;
    overflow: hidden;
    margin-bottom: 0;
    transform: none;
  }

  .editor-picks__primary-pick .entry__category {
    font-size: 15px;
    line-height: 22px;
  }

  .editor-picks__primary-pick .entry__heading {
    font-size: 34px;
    line-height: 41px;
  }
}


/* [project]/src/styles/threecardsection.css [client] (css) */
.editor-picks__secondary-wrapper {
  padding: 4rem 0 0;
}

.editor-picks__secondary-pick {
  text-align: center;
  margin-bottom: 1.25rem;
  margin-top: 1.5625rem;
}

.editor-picks__secondary-wrapper .entry__heading {
  color: var(--text-color);
  transition: color .1s ease-in .5s;
}

.editor-picks__secondary-wrapper .featured-image:before {
  display: block;
  content: " ";
  width: 100%;
  padding-top: 56.25%;
}

.editor-picks__secondary-wrapper .featured-image .image-wrapper {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.editor-picks__secondary-wrapper .editor-picks__secondary-pick .featured-image {
  max-height: 300px !important;
}

@media only screen and (width <= 41.6875rem) {
  .editor-picks__secondary-wrapper {
    padding: 0;
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick a {
    display: flex;
    border-top: 1px solid #e8e8e8;
    padding-top: 25px;
    box-sizing: border-box;
    margin: 10px;
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {
    text-align: start;
    margin-left: 10px;
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {
    display: flex;
    flex-direction: column;
  }

  .editor-picks__secondary-pick .entry .entry__category {
    font-size: 11px;
    order: 1;
    margin-bottom: 5px;
  }

  .editor-picks__secondary-pick .entry .entry__heading {
    font-size: 17px;
    line-height: 22px;
    order: 2;
    margin-bottom: 5px;
  }

  .editor-picks__secondary-pick .entry .post-meta {
    justify-content: flex-end;
    order: 3;
  }

  .editor-picks__secondary-pick .entry .post-meta .post-meta__author {
    font-size: 12px;
  }

  .editor-picks__secondary-pick .entry .post-meta .post-meta__timestamp {
    display: none;
  }

  .editor-picks__primary-pick .entry {
    padding: .8rem .9375rem 0;
    display: flex;
    flex-direction: column;
  }

  .editor-picks__primary-pick .entry .entry__category {
    order: 1;
  }

  .editor-picks__primary-pick .entry .entry__heading {
    order: 2;
  }

  .editor-picks__primary-pick .entry__excerpt {
    display: none;
  }

  .editor-picks__primary-pick .entry .entry__excerpt {
    order: 3;
  }

  .editor-picks__primary-pick .entry .post-meta {
    order: 4;
    padding-bottom: 0;
  }

  .editor-picks__primary-pick .entry .post-meta .post-meta__author {
    font-size: 12px;
  }

  .editor-picks__primary-pick .entry .post-meta .post-meta__timestamp {
    display: none;
  }
}

@media only screen and (width <= 61.1875rem) {
  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .featured-image {
    max-width: 100%;
    min-width: 35%;
    overflow: hidden;
    position: relative;
  }
}

@media only screen and (width <= 450px) {
  .editor-picks__secondary-wrapper .editor-picks__secondary-pick .entry {
    margin-left: 15px;
  }
}

@media only screen and (width >= 41.75rem) {
  .editor-picks__secondary-wrapper .editor-picks__secondary-inner-wrapper {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .editor-picks__secondary-pick {
    width: calc(33.3333% - .83333rem);
    margin-bottom: 0;
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick {
    width: calc(33.3333% - .83333rem);
  }

  .editor-picks__secondary-wrapper .editor-picks__secondary-pick {
    margin-bottom: 1.5625rem;
  }
}


/* [project]/src/styles/tworiversection.css [client] (css) */
.two-rivers-wrapper {
  font-size: 1.15rem;
}

.two-rivers-wrapper .sectioner {
  margin-bottom: 0;
}

.sectioner--latest-stories {
  width: 100%;
  padding: 75px 0;
  background-color: var(--body-bg-color);
}

.sectioner--latest-stories .entry {
  margin-left: 10px;
}

.latest-story.latest-story--primary .entry {
  margin-left: 0;
}

.sectioner--latest-stories .entry__category {
  font-size: 11px;
  line-height: 15px;
}

.latest-story--primary {
  flex-direction: column;
}

.sectioner--latest-stories .latest-story {
  margin-bottom: 1.875rem;
  display: flex;
  text-align: left;
  border-bottom: .0625rem solid #dddee4;
  padding-bottom: 1.25rem;
}

.sectioner--latest-stories .latest-story:last-of-type {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.sectioner--latest-stories .latest-story--primary {
  border-bottom: .0625rem solid #dddee4;
  padding-bottom: 1.25rem;
  text-align: center;
}

.sectioner--latest-stories .latest-story--primary .entry__excerpt {
  font-size: 19px;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 26px;
  letter-spacing: normal;
  font-weight: 400;
  margin-bottom: .75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: var(--text-color);
}

.latest-story--primary .entry {
  align-items: center;
  text-align: center;
  min-height: 160px;
}

.latest-story .entry__heading {
  font-family: rocky, sans-serif;
  font-size: 29px;
  line-height: 29px;
  letter-spacing: normal;
  color: var(--text-color);
}

.sectioner--latest-stories .latest-story--primary .entry__heading {
  font-size: 26px;
  line-height: 32px;
  letter-spacing: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: var(--text-color);
}

.sectioner--latest-stories .section-header {
  display: block;
  border-bottom: none;
  border-left: none;
  text-align: center;
  padding: 0;
  margin-bottom: 2rem;
}

.sectioner--latest-stories .section-header .section-header__heading {
  font-size: 1.875rem;
  line-height: 24px;
  letter-spacing: 1px;
  font-family: sweet-sans-pro, sans-serif;
  color: var(--text-color);
}

.latest-story:not(.latest-story--primary) .featured-image {
  display: inline-block;
  height: auto;
  max-width: 350px;
  overflow: hidden;
}

.latest-story:not(.latest-story--primary) .featured-image a {
  height: auto;
}

.latest-story--primary .featured-image {
  margin-bottom: .9375rem;
}

@media only screen and (width >= 41.75rem) {
  .latest-story .featured-image a {
    width: calc(50% - .625rem);
  }

  .latest-story:not(.latest-story--primary) .featured-image {
    display: inline-table;
    max-width: 350px;
    min-width: 50%;
    overflow: hidden;
  }

  .latest-story {
    display: flex;
    text-align: left;
  }

  .latest-story .entry {
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .latest-story .entry__heading {
    font-size: 23px;
  }
}

.latest-story--primary .entry, .latest-story--primary .featured-image {
  width: 100%;
}

@media only screen and (width >= 61.25rem) {
  .two-rivers-wrapper {
    display: flex;
    justify-content: space-between;
    border-top: .5px solid #d4d4d4;
  }

  .sectioner--latest-stories {
    padding: 75px 1.625rem;
  }

  .sectioner--latest-stories .latest-story--primary {
    font-size: 2.25rem;
    justify-content: flex-start;
  }

  .latest-story:not(.latest-story--primary) .featured-image {
    display: inline-table;
    max-width: 200px;
    min-width: 35%;
    overflow: hidden;
  }

  .latest-story .entry__heading {
    font-size: 20px;
  }

  .latest-story .entry__heading {
    font-size: 17px;
    line-height: 22px;
  }
}

@media only screen and (width >= 70.625rem) {
  .sectioner--latest-stories {
    padding: 75px 1.625rem;
  }

  .latest-story:not(.latest-story--primary) .featured-image {
    display: inline-table;
    max-width: 250px;
    min-width: 35%;
    overflow: hidden;
  }

  .latest-story .entry__heading {
    font-size: 22px;
    line-height: 28px;
  }

  .two-rivers-wrapper .sectioner--latest-stories .featured-image {
    position: relative;
    margin-bottom: 1.5625rem;
  }

  .two-rivers-wrapper .sectioner--latest-stories .featured-image:before {
    display: block;
    content: " ";
    width: 100%;
    padding-top: 56.25%;
  }

  .two-rivers-wrapper .sectioner--latest-stories .featured-image .image-wrapper {
    display: block;
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .two-rivers-wrapper .entry__category {
    font-size: 15px;
    line-height: 22px;
  }
}

@media only screen and (width <= 41.6875rem) {
  .sectioner--latest-stories .entry {
    display: flex;
    flex-direction: column;
  }

  .sectioner--latest-stories .entry .entry__category {
    order: 1;
    margin-bottom: 5px;
  }

  .sectioner--latest-stories .entry .entry__heading {
    order: 2;
    margin-bottom: 5px;
  }

  .sectioner--latest-stories .entry .post-meta {
    order: 3;
  }

  .sectioner--latest-stories .entry .post-meta .post-meta__author {
    font-size: 12px;
  }

  .sectioner--latest-stories .entry .post-meta .post-meta__timestamp {
    display: none;
  }

  .sectioner--latest-stories .latest-story .entry__heading {
    font-size: 17px;
    line-height: 22px;
  }

  .latest-story:not(.latest-story--primary) .featured-image {
    max-width: 400px;
    min-width: 40%;
  }

  .sectioner--latest-stories {
    padding: 25px 0;
  }

  .latest-story.latest-story--primary .entry {
    min-height: auto;
  }

  .sectioner--latest-stories .latest-story--primary .entry__excerpt {
    display: none;
  }

  .sectioner--latest-stories .section-header .section-header__heading {
    font-size: 1.375rem;
    letter-spacing: 1px;
  }
}

@media only screen and (width <= 375px) {
  .latest-story:not(.latest-story--primary) .featured-image {
    max-height: 90px;
  }
}


/* [project]/src/styles/author.css [client] (css) */
#author_header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0 40px;
}

#profile_author {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
}

#profile_author img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

#author_header h1 {
  line-height: 6vw;
  color: var(--text-color);
}

#author_header h4 {
  font-weight: 500;
  font-size: 1.5rem;
  font-family: rocky, sans-serif;
  margin-bottom: 20px;
  margin-top: 10px;
  color: var(--text-color);
}

#author-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 20px;
}

#author_header p {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: .37px;
  font-family: Georgia, sans-serif;
  color: var(--text-color);
}

@media (width <= 768px) {
  #author_header h4 {
    margin-top: 18px;
  }

  #author_header h1 {
    margin-top: 8px;
  }
}


/* [project]/src/styles/error.css [client] (css) */
#error_page {
  width: 100%;
  height: 88vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--body-bg-color);
}

#error_page h2 {
  font-size: 6rem;
  line-height: 1.1;
  font-family: rocky, sans-serif;
  font-weight: 300;
}

#error_page h4 {
  font-size: 4rem;
  line-height: 1.1;
  font-family: rocky, sans-serif;
  font-weight: 300;
  text-transform: capitalize;
}

#error_page p {
  width: 60%;
  text-align: center;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: .37px;
  font-family: Georgia, sans-serif;
  margin-top: 30px;
}

@media screen and (width <= 767px) {
  #error_page {
    padding: 1rem;
  }

  #error_page p {
    width: 100%;
  }
}

@media screen and (width <= 960px) and (width >= 767px) {
  #error_page p {
    width: 85%;
  }
}

@media screen and (width <= 1300px) and (width >= 960px) {
  #error_page p {
    width: 75%;
  }
}


/* [project]/src/styles/about.css [client] (css) */
#about_wrapper {
  background-color: var(--body-bg-color);
  padding: 60px 0;
}

#about_wrapper h1 {
  font-size: 4rem;
  line-height: 1.1;
  font-family: rocky, sans-serif;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}

.container-wrap {
  width: 100%;
  height: 100%;
  display: flex;
}

.text-container {
  height: 100%;
  padding: 2rem 1rem;
  border-right: 1px solid var(--about-side-border);
  padding-right: 2rem;
}

.text-container p {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: .37px;
  font-weight: 300;
  font-family: Georgia, sans-serif;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.text-container p .bold {
  font-weight: 600;
}

.text-container p.bold-p {
  margin-bottom: .5rem;
}

.mt-top {
  margin-top: 2.5rem;
}

.tel {
  color: #3475de;
}

.text-container ul li {
  font-size: 18px;
  line-height: 28px;
  letter-spacing: .37px;
  font-weight: 300;
  font-family: Georgia, sans-serif;
  color: var(--text-color);
  margin-bottom: .6rem;
  list-style-position: inside;
}

.text-container ul {
  padding-left: 20px;
}

.text-container ul li {
  list-style-position: outside;
  padding-left: 10px;
}

.text-container h2 {
  font-size: 26px;
  line-height: 32px;
  letter-spacing: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: var(--text-color);
  margin-bottom: 1rem;
}

@media screen and (width <= 767px) {
  #about_wrapper h1 {
    font-size: 2rem;
    color: var(--text-color);
    width: 95%;
    line-height: 1;
    font-family: rocky, sans-serif;
    letter-spacing: .37px;
    padding: 0 .625rem;
    margin-bottom: 1rem;
  }

  #about_wrapper .containerWrapper {
    padding: .625rem !important;
  }

  .text-container p, .text-container ul li {
    font-weight: 300;
    margin-bottom: 1.5rem;
    line-height: 26px;
    font-size: 16px;
    font-family: Georgia, sans-serif;
  }

  .text-container h2 {
    font-size: 17px;
    line-height: 22px;
  }

  .text-container ul li {
    margin-bottom: .6rem;
  }

  .text-container {
    height: 100%;
    padding: 1rem .625rem;
    border-right: none;
    border-bottom: 1px solid #00000026;
  }
}

@media screen and (width <= 960px) and (width >= 767px) {
  
}

@media screen and (width <= 1300px) and (width >= 960px) {
  
}


/* [project]/src/styles/filterDrawer.css [client] (css) */
#drawer_container {
  width: 100%;
  height: 100dvh;
  background-color: #00000059;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 9999;
  pointer-events: none;
  backdrop-filter: blur(4px);
  opacity: 0;
}

#list_container {
  position: absolute;
  right: -100%;
  top: 0;
  width: 30%;
  height: 100%;
  background-color: var(--body-bg-color);
}

.Search_drawer #list_container {
  width: 35%;
}

.Search_drawer form svg {
  cursor: pointer;
}

.Search_drawer .chipContainer {
  margin: 0;
  margin-top: 3rem;
}

.filter_title {
  font-size: 1.2rem;
  color: var(--text-color);
  font-family: rocky, sans-serif;
  line-height: 1.1;
}

.accordion {
  margin-top: 10px;
  border-bottom: 1px solid #d2d2d2;
}

.accordion_header {
  cursor: pointer;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.accordion_header span {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 1.2rem;
  color: var(--text-color);
  font-family: sweet-sans-pro, sans-serif;
  line-height: 1.1;
}

.accordion_header span.clear_text {
  font-size: .8rem;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 23px;
  letter-spacing: .08rem;
  text-transform: uppercase;
}

.accordion_content {
  margin-top: 5px;
  padding-left: 10px;
  padding-bottom: 20px;
  max-height: 250px;
  overflow: auto;
}

.category_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 1rem;
  font-family: sweet-sans-pro, sans-serif;
  line-height: 23px;
  letter-spacing: .08rem;
}

.category_item input {
  margin-right: 10px;
  cursor: pointer;
}

.item_count {
  color: #888;
}

.filter_title_row {
  position: sticky;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: .5rem;
  font-size: 1.2rem;
  font-weight: 600;
  padding: 2.5rem;
  padding-top: 1.8rem;
  padding-bottom: 3rem;
}

.search_drawer.filter_title_row {
  justify-content: end;
}

#search_drawer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: .5rem;
  border-bottom: 1px solid var(--chip-color);
}

#search_drawer input {
  width: 100%;
  padding: 10px;
  background-color: #0000;
  border: none;
  outline: none;
  font-size: 1rem;
}

#search_drawer input::placeholder {
  font-size: 1rem;
}

#search_drawer svg {
  font-size: 1.6rem;
}

.search_chipContainer .border_btn_button {
  border: 1px solid var(--chip-color);
  color: var(--chip-color);
}

.search_chipContainer .border_btn_button:hover {
  color: var(--body-bg-color);
}

.drawer_body {
  position: relative;
  width: 100%;
  height: calc(100% - 248px);
  overflow-y: scroll;
  padding: 0 2.5rem;
}

.drawer_body_search::-webkit-scrollbar {
  display: none;
}

.drawer_footer {
  width: 100%;
  position: relative;
  padding: 2.5rem;
}

.drawer_footer .button_base {
  width: 100%;
}

.close_icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.close_icon svg {
  font-size: 35px;
  color: var(--text-color);
  font-family: rocky, sans-serif;
  line-height: 1.1;
  cursor: pointer;
}

.accordion_label {
  width: 100%;
  display: flex;
  align-items: center;
  gap: .5rem;
}

.arrow_icon {
  transition: transform .3s;
}

.arrow_icon.rotated {
  transform: rotate(180deg);
}

@media screen and (width <= 767px) {
  #list_container {
    width: 100%;
    padding: 1rem;
  }

  .Search_drawer #list_container {
    width: 100%;
  }

  .drawer_body {
    padding: 0;
  }

  .filter_title_row {
    padding: 0 0 4rem;
  }

  .drawer_footer {
    padding: 2.5rem 0;
  }
}

@media screen and (width <= 960px) and (width >= 767px) {
  #list_container {
    width: 55%;
  }
}

@media screen and (width <= 1300px) and (width >= 960px) {
  #list_container {
    width: 42%;
  }
}


/*# sourceMappingURL=src_styles_5758ad9e._.css.map*/
