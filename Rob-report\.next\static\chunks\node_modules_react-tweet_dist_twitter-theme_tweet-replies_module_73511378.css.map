{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css"], "sourcesContent": [".replies {\n  padding: 0.25rem 0;\n}\n.link {\n  text-decoration: none;\n  color: var(--tweet-color-blue-secondary);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 32px;\n  min-height: 32px;\n  user-select: none;\n  outline-style: none;\n  transition-property: background-color;\n  transition-duration: 0.2s;\n  padding: 0 1rem;\n  border: var(--tweet-border);\n  border-radius: 9999px;\n}\n.link:hover {\n  background-color: var(--tweet-color-blue-secondary-hover);\n}\n.text {\n  font-weight: var(--tweet-replies-font-weight);\n  font-size: var(--tweet-replies-font-size);\n  line-height: var(--tweet-replies-line-height);\n  overflow-wrap: break-word;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n"], "names": [], "mappings": "AAAA;;;;AAGA;;;;;;;;;;;;;;;;;AAgBA;;;;AAGA", "ignoreList": [0]}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}