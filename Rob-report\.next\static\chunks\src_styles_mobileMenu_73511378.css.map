{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/mobileMenu.css"], "sourcesContent": [".menu-cont {\r\n  position: fixed;\r\n  top: 0px;\r\n  height: 100dvh;\r\n  width: 100vw;\r\n  /* pointer-events: none; */\r\n  z-index: 999;\r\n  transition: 0.4s;\r\n  display: none;\r\n}\r\n.menu-inner {\r\n  width: 100%;\r\n  margin-left: auto;\r\n  background-color: rgb(0, 0, 0);\r\n}\r\n.manu-inner-block,\r\n.menu-inner {\r\n  height: 100dvh;\r\n  transition: 0.3s ease-out;\r\n}\r\n.manu-inner-block {\r\n  width: 100%;\r\n}\r\n.menu-top {\r\n  display: flex;\r\n  height: 80px;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 15px;\r\n  padding: 3vh 4vw;\r\n}\r\n.flex-all {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.menu-back {\r\n  position: relative;\r\n  width: 40px;\r\n  height: 40px;\r\n  font-size: 20px;\r\n  background-color: rgb(255, 255, 255);\r\n  transition: 0.3s;\r\n  border-radius: 50%;\r\n}\r\n.menu-close {\r\n  width: 40px;\r\n  height: 40px;\r\n  font-size: 19px;\r\n  background-color: rgb(255, 255, 255);\r\n  transition: 0.3s;\r\n  border-radius: 50%;\r\n}\r\n.menu-close svg {\r\n  color: #000 !important;\r\n}\r\n.menu-back svg {\r\n  color: #000 !important;\r\n}\r\n.menu-main {\r\n  position: relative;\r\n  flex-direction: column;\r\n  align-items: normal;\r\n  height: calc(100dvh - (80px + 60px));\r\n  width: 100%;\r\n  overflow: auto;\r\n  padding: 3vh 4vw;\r\n}\r\n.menu-extras,\r\n.menu-main {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n.menu-items {\r\n  height: 50px;\r\n  color: rgb(255, 255, 255);\r\n  width: 100%;\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.menu-name {\r\n  font-size: 1.25rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 500;\r\n  letter-spacing: 1px;\r\n  text-transform: uppercase;\r\n  width: 100%;\r\n  display: inline-block;\r\n}\r\n.menu-arrow {\r\n  position: absolute;\r\n  right: 0px;\r\n  font-size: 18px;\r\n  transition: 0.3s;\r\n}\r\n.menu-extras {\r\n  align-items: flex-end;\r\n}\r\n.menu-ext {\r\n  display: flex;\r\n  align-items: center;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 16px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 400;\r\n  letter-spacing: 1px;\r\n  margin: 8px 0px;\r\n  transition: 0.3s;\r\n}\r\n.menu-btm {\r\n  width: 100%;\r\n  position: relative;\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin: auto;\r\n  gap: 25px;\r\n  padding: 0vh 4vw;\r\n  border-top: 1px solid rgb(255, 255, 255);\r\n}\r\n.menu-follows {\r\n  margin-left: 10px;\r\n}\r\n.menu-follows .menu-follows-items {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n.menu-follows .menu-follows-items a {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: auto;\r\n}\r\n.menu-follows .menu-follows-items svg {\r\n  font-size: 18px;\r\n}\r\n.menu-follows-text {\r\n}\r\n.d-none {\r\n  display: none !important;\r\n}\r\n.d-block {\r\n  display: block !important;\r\n}\r\n.submenu-head,\r\n.submenu-items {\r\n  color: rgb(255, 255, 255);\r\n  width: 100%;\r\n  position: relative;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.submenu-head {\r\n  /* height: 50px; */\r\n}\r\n.submenu-items {\r\n  margin-bottom: 16px;\r\n}\r\n.submenu-name,\r\n.submenu-title {\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-weight: 500;\r\n  letter-spacing: 1px;\r\n  line-height: 1.5;\r\n  text-transform: uppercase;\r\n}\r\n.submenu-name {\r\n  font-size: 20px;\r\n  gap: 15px;\r\n  color: rgba(224, 224, 224, 0.75) !important;\r\n  width: 100%;\r\n  display: inline-block;\r\n}\r\n.submenu-title {\r\n  font-size: 26px;\r\n  color: #fff !important;\r\n  width: 100%;\r\n  line-height: 1.2;\r\n  margin-bottom: 20px;\r\n}\r\n@media (max-width: 61.1875rem) {\r\n  .menu-cont {\r\n    display: flex;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAUA;;;;;;AAKA;;;;;AAKA;;;;AAGA;;;;;;;;;AAQA;;;;;;;AAMA;;;;;;;;;;AASA;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;AAMA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;AAGA;;;;;;AAKA;;;;;;;AAMA;;;;AAGA;;;AAEA;;;;AAGA;;;;AAGA;;;;;;;;;AASA;;;AAGA;;;;AAGA;;;;;;;;AAQA;;;;;;;;AAOA;;;;;;;;AAOA;EACE"}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}