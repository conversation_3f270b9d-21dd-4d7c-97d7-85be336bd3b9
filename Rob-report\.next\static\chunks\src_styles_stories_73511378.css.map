{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/stories.css"], "sourcesContent": ["/* ///////////////////////////////hero section/////////////////////////// */\r\n#story_wrapper {\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n}\r\n.story_hero_hero_container {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 1rem 0;\r\n  background-color: var(--body-bg-color);\r\n  transition: background-color 0.45s ease-in 0.5s;\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n.story-hero-section .story-photoBy {\r\n  padding-inline: 0.625rem;\r\n  margin: 5px 0 0;\r\n}\r\n.story_hero_text_container {\r\n  width: 100%;\r\n  height: 83%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  /* gap: 2rem; */\r\n}\r\n\r\n.story_hero_category {\r\n  font-size: 1rem;\r\n  color: red;\r\n  font-weight: 500;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n  text-transform: uppercase;\r\n  margin-bottom: 1rem;\r\n}\r\n.story_hero_title {\r\n  font-size: 2rem;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n  width: 95%;\r\n  line-height: 1;\r\n  font-family: rocky, sans-serif;\r\n  letter-spacing: 0.37px;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.story_hero_description {\r\n  font-size: 1rem;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n  opacity: 0.7;\r\n  width: 75%;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  line-height: 1.1;\r\n  letter-spacing: 0.37px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.story_hero_info_container {\r\n  width: 100%;\r\n  height: 17%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n\r\n  /* opacity: 0.8; */\r\n  padding: 0 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  flex-wrap: wrap;\r\n  letter-spacing: 0.37px;\r\n}\r\n\r\n.story_hero_info {\r\n  display: inherit;\r\n  white-space: pre;\r\n  font-size: 1rem;\r\n}\r\n\r\n/* .story-main-wrapper:has(.story-bannerSec) {\r\n  border-right: none !important;\r\n} */\r\n@media only screen and (max-width: 767px) {\r\n  .mob-py-0 {\r\n    padding-top: 0 !important;\r\n    padding-bottom: 0 !important;\r\n  }\r\n}\r\n@media only screen and (min-width: 768px) {\r\n  .story_hero_hero_container {\r\n    height: 70vh;\r\n    padding: 0;\r\n  }\r\n}\r\n@media only screen and (min-width: 92.5rem) {\r\n  .story-main-wrapper > * {\r\n    max-width: 100rem;\r\n  }\r\n}\r\n\r\n.story_hero_image_container {\r\n  width: 100%;\r\n  height: auto;\r\n  aspect-ratio: 16/9;\r\n  background-color: gray;\r\n  position: relative;\r\n}\r\n.story-photoBy {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 5px;\r\n  margin: 1px 0 20px;\r\n  line-height: 1.35;\r\n  text-align: left;\r\n}\r\n.story-caption {\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  font-size: 0.875rem;\r\n  letter-spacing: 0.37px;\r\n  font-weight: 500;\r\n  line-height: 1.1;\r\n  color: var(--text-color) !important;\r\n  opacity: 0.8;\r\n  text-align: left;\r\n}\r\n.story-courtesy {\r\n  font-family: rocky, sans-serif;\r\n  font-size: 0.875rem;\r\n  letter-spacing: 1px;\r\n  color: var(--text-color) !important;\r\n  text-align: left;\r\n}\r\n.story-main-wrapper .story-bannerSec {\r\n  display: block;\r\n  position: relative;\r\n  width: 100vw;\r\n  height: auto;\r\n  margin-left: 0 !important;\r\n  margin-right: 0 !important;\r\n  margin-bottom: 20px;\r\n  padding-inline: 0 !important;\r\n}\r\n.story-main-wrapper .story-bannerSec .story-photoBy {\r\n  padding: 0px 20px;\r\n}\r\n.react-tweet-theme {\r\n  margin-bottom: 0px !important;\r\n}\r\n.embed-frame iframe {\r\n  height: 100% !important;\r\n}\r\n.embed-frame.embed-youtube {\r\n  aspect-ratio: 16/9;\r\n}\r\n.instaembed {\r\n  width: 100% !important;\r\n}\r\n.embed-frame.embed-instagram {\r\n  height: auto;\r\n  aspect-ratio: 9 / 16;\r\n  margin: 0 auto;\r\n}\r\n.story-listStyle {\r\n  padding-inline-start: 25px !important;\r\n}\r\n.d-flex {\r\n  display: grid;\r\n  grid-template-columns: 1fr;\r\n}\r\n.d-flex > :nth-child(3) {\r\n  grid-column: 1 / -1;\r\n}\r\n.story-main-container p span {\r\n  color: var(--text-color) !important;\r\n}\r\n@media (max-width: 500px) {\r\n  .story_hero_description {\r\n    width: 95%;\r\n  }\r\n  .story_hero_info_container {\r\n    gap: 20px;\r\n  }\r\n  .story_info_tag:nth-child(2) {\r\n    order: 3;\r\n  }\r\n\r\n  .story_info_tag:nth-child(3) {\r\n    order: 2;\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (min-width: 768px) {\r\n  .instaembed {\r\n    /* width: 75% !important; */\r\n  }\r\n  .embed-frame.embed-instagram {\r\n    height: 100vh;\r\n    aspect-ratio: 9 / 16;\r\n    margin: 0 auto;\r\n  }\r\n  .story_hero_title {\r\n    font-size: 4rem;\r\n    line-height: 1.1;\r\n    font-family: rocky, sans-serif;\r\n    width: 75%;\r\n  }\r\n\r\n  .story_hero_description {\r\n    font-size: 1.5rem;\r\n    font-family: sweet-sans-pro, sans-serif;\r\n    margin-bottom: 0px;\r\n  }\r\n\r\n  .story_hero_info {\r\n    font-size: 1.25rem;\r\n    font-family: sweet-sans-pro, sans-serif;\r\n  }\r\n\r\n  .story_hero_info_container {\r\n    padding: 0 2.5rem;\r\n  }\r\n  .d-flex {\r\n    grid-template-columns: 3fr 1fr;\r\n  }\r\n}\r\n@media (max-width: 768px) {\r\n  .d-flex {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n  .submenu-body.d-flex {\r\n    flex-direction: column-reverse !important;\r\n  }\r\n  #about_wrapper .d-flex {\r\n    flex-direction: column-reverse;\r\n  }\r\n\r\n  .d-flex > :nth-last-child(1) {\r\n    order: 2; /* last child becomes second */\r\n  }\r\n\r\n  .d-flex > :nth-last-child(2) {\r\n    order: 3; /* second-last becomes last */\r\n  }\r\n\r\n  .story-main-wrapper {\r\n    border: none;\r\n  }\r\n}\r\n\r\n/* /////////////////////////////main section/////////////////////////// */\r\n\r\n.story_main_classname_container {\r\n  /* width: 100%;\r\n  margin: 0 auto; */\r\n}\r\n.story-main-container {\r\n  /* width: 67vw; */\r\n}\r\n.story-side-container {\r\n  /* width: 33vw; */\r\n}\r\n.story-side-wrapper {\r\n  /* padding: 40px 0 40px 40px; */\r\n  /* margin-right: 10rem; */\r\n  padding: 0rem 0;\r\n}\r\n\r\n.story_main_classname_main {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-top: 10px;\r\n}\r\n.story-main-container h2 {\r\n  font-size: 1.5rem;\r\n  line-height: 1.1;\r\n  font-family: rocky, sans-serif;\r\n}\r\n.story-main-container h2 span,\r\n.story-main-container h3 span,\r\n.story-main-container h4 span,\r\n.story-main-container h5 span,\r\n.story-main-container h6 span {\r\n  color: var(--text-color) !important;\r\n}\r\n.story-main-container a,\r\n.story-main-container a > span {\r\n  position: relative;\r\n  color: red !important;\r\n}\r\n.story-main-container a::after {\r\n  content: \"\"; /* Required for the pseudo-element */\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 0%; /* Always 100% for active link */\r\n  height: 1px; /* Height of the border */\r\n  background-color: red !important; /* Border color */\r\n  transition: width 0.45s ease; /* Smooth transition of width */\r\n}\r\n\r\n.story-main-container a:hover::after {\r\n  width: 100%;\r\n}\r\n@media only screen and (min-width: 768px) {\r\n  .story_main_classname_main {\r\n    flex-direction: row;\r\n  }\r\n  .story-side-wrapper {\r\n    padding: 0rem 1rem;\r\n    padding-left: 2rem;\r\n  }\r\n  .story-main-container h2 {\r\n    font-size: 2rem;\r\n    line-height: 1.1;\r\n  }\r\n}\r\n\r\n.story_main_classname_left {\r\n  width: 100%;\r\n  padding: 20px 0;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_left {\r\n    width: 72%;\r\n    padding: 40px 40px 40px 0;\r\n  }\r\n}\r\n\r\n.story-main-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  /* padding: 20px; */\r\n  font-size: 20px;\r\n  text-align: justify;\r\n  font-family: Georgia, sans-serif;\r\n  line-height: 1.3;\r\n  font-weight: 100;\r\n  padding: 1rem 0;\r\n  /* padding: 40px 40px 40px 0px; */\r\n  /* border-bottom: 1px solid rgba(0, 0, 0, 0.15); */\r\n}\r\n\r\n.story-main-wrapper p {\r\n  font-weight: 300;\r\n  margin-bottom: 1.5rem;\r\n  line-height: 26px;\r\n  font-size: 16px;\r\n  /* letter-spacing: 0.37px; */\r\n  font-family: Georgia, sans-serif;\r\n  color: var(--text-color);\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story-main-wrapper {\r\n    font-size: 27px;\r\n    font-size: 1.4rem;\r\n    line-height: 1.2;\r\n    border-right: 1px solid var(--line-color);\r\n    padding: 0rem 1rem;\r\n    padding-right: 2rem;\r\n\r\n    /* padding-left: 40px; */\r\n  }\r\n  .story-main-wrapper p {\r\n    font-size: 18px;\r\n    line-height: 28px;\r\n    letter-spacing: 0.37px;\r\n  }\r\n}\r\n\r\n.story_main_classname_imageWrapper {\r\n  width: 100%;\r\n  /* height: 90vh; */\r\n  background: #ccc;\r\n  position: relative;\r\n  margin: 3rem 0;\r\n  aspect-ratio: 16/9;\r\n}\r\n\r\n.story_main_classname_divider {\r\n  display: none;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_divider {\r\n    display: block;\r\n    width: 1px;\r\n    background: #a4a4a475;\r\n  }\r\n}\r\n\r\n.story_main_classname_right {\r\n  width: 100%;\r\n  padding: 20px 0 20px 0px;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_right {\r\n    width: 28%;\r\n    padding: 40px 0 40px 10px;\r\n  }\r\n}\r\n@media (min-width: 1200px) {\r\n  .story_main_classname_right {\r\n    width: 28%;\r\n    padding: 40px 0 40px 40px;\r\n  }\r\n}\r\n.story_main_classname_card {\r\n  height: 65vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.story_main_classname_cardImage {\r\n  width: 100%;\r\n  object-fit: cover;\r\n}\r\n.story_main_classname_img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n.story_main_classname_cardText {\r\n  height: 10%;\r\n  text-align: center;\r\n  font-size: 1.5rem;\r\n  font-weight: 500;\r\n  padding: 10px;\r\n  font-family: rocky, sans-serif;\r\n  line-height: 1;\r\n  color: var(--text-colorblack);\r\n}\r\n\r\n.story_main_classname_alsoInteresting {\r\n  /* margin-top: 3rem; */\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  font-weight: 500;\r\n  position: relative;\r\n  /* border-bottom: 2px solid rgba(0, 0, 0, 0.12);\r\n  border-left: 2px solid rgba(0, 0, 0, 0.12); */\r\n  text-align: center;\r\n}\r\n.story_main_classname_alsoInteresting span {\r\n  background-color: var(--body-bg-color);\r\n  padding: 0 10px;\r\n  font-size: 1.4rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n  color: var(--text-color);\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n#about_wrapper .story_main_classname_alsoInteresting span {\r\n  background-color: var(--body-bg-color);\r\n  color: var(--text-color);\r\n  position: relative;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n.story_main_classname_alsoInteresting:before {\r\n  border-top: 0.1px solid var(--line-color);\r\n  content: \"\";\r\n  margin: 0 auto;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  z-index: 1;\r\n}\r\n#about_wrapper .story_main_classname_alsoInteresting:before {\r\n  border-top: 0.1px solid var(--line-color);\r\n  z-index: 1;\r\n}\r\n.story_main_classname_advertisement {\r\n  margin-top: 20px;\r\n  height: 60vh;\r\n  width: 100%;\r\n  background: #e0e0e0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  color: gray;\r\n}\r\n\r\n.story_main_classname_slider {\r\n  padding: 10px 0px 10px 0px;\r\n}\r\n.relatedWrapper {\r\n  display: grid;\r\n  grid-template-columns: repeat(1, 1fr);\r\n  gap: 0;\r\n}\r\n#related-stories-section {\r\n  padding-top: 0px;\r\n}\r\n\r\n@media (min-width: 668px) {\r\n  .relatedWrapper {\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 24px;\r\n  }\r\n  .relatedWrapper > .featured-category__story {\r\n    width: 100%;\r\n  }\r\n}\r\n@media (min-width: 768px) {\r\n  #related-stories-section {\r\n    padding-top: 40px;\r\n  }\r\n  .story_main_classname_slider {\r\n    padding: 40px 0 40px 0px;\r\n  }\r\n}\r\n@media (min-width: 980px) {\r\n  .relatedWrapper {\r\n    display: grid;\r\n    grid-template-columns: repeat(3, 1fr);\r\n    gap: 24px;\r\n  }\r\n  .relatedWrapper > .featured-category__story {\r\n    width: 100%;\r\n  }\r\n}\r\n@media (min-width: 1200px) {\r\n  .story_main_classname_slider {\r\n    padding: 10px 0 10px 0;\r\n  }\r\n}\r\n.story_main_classname_social {\r\n  padding: 10px 0px;\r\n  margin-top: 5px;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_social {\r\n    margin-top: 5vh;\r\n    margin-bottom: 5vh;\r\n  }\r\n}\r\n@media (min-width: 1200px) {\r\n  .story_main_classname_social {\r\n    padding: 10px 0 10px 0;\r\n  }\r\n}\r\n.story_main_classname_footer {\r\n  width: 100%;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_main_classname_footer {\r\n    display: flex;\r\n  }\r\n}\r\n\r\n/* ////////////////////////////////// interestin card /////////////////////////// */\r\n\r\n.story_interesting_classname_container {\r\n  width: 100%;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.story_interesting_classname_card {\r\n  width: 100%;\r\n}\r\n\r\n.story_interesting_classname_imageWrapper {\r\n  position: relative;\r\n  width: 100%;\r\n  aspect-ratio: 16/9;\r\n  overflow: hidden;\r\n  /* height: 22vh; */\r\n}\r\n\r\n.story_interesting_classname_image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.story_interesting_classname_text {\r\n  width: 100%;\r\n  min-height: 10vh;\r\n  text-align: center;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  margin-top: 0.625rem;\r\n}\r\n\r\n.story_interesting_classname_heading {\r\n  font-size: 1rem;\r\n  color: red;\r\n  font-weight: 500;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  letter-spacing: 1px;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.story_interesting_classname_description {\r\n  font-size: 1.4rem;\r\n  color: var(--text-color);\r\n  font-family: rocky, sans-serif;\r\n  line-height: 1.2;\r\n}\r\n#about_wrapper .story_interesting_classname_description {\r\n  color: var(--text-color);\r\n  line-height: 1.2;\r\n}\r\n/* //////////////////////////////////////////swiper slider/////////////////////////////////////////// */\r\n\r\n.story_swiper_classname_container {\r\n  position: relative;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  margin: 0 auto;\r\n}\r\n\r\n.story_swiper_classname_swiper {\r\n  width: 100%;\r\n  aspect-ratio: 1;\r\n  /* height: 50vh; */\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_swiper_classname_swiper {\r\n    height: 50vh;\r\n  }\r\n}\r\n\r\n.story_swiper_classname_slide {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.story_swiper_classname_image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.story_swiper_classname_controls {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 10vh;\r\n  display: flex;\r\n  align-items: center;\r\n  padding-top: 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n\r\n.story_swiper_classname_buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.story_swiper_classname_arrow {\r\n  background: transparent;\r\n  border: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.story_swiper_classname_icon {\r\n  font-size: 24px;\r\n  color: black;\r\n  background-color: white;\r\n}\r\n\r\n.story_swiper_classname_counter {\r\n  font-size: 1.2rem;\r\n  margin-left: 1rem;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n/* ///////////////////////////////////social card/////////////////////////////////////// */\r\n\r\n.story_social_container {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px 0;\r\n  color: var(--text-color);\r\n  transition: color 0.45s ease-in 0.5s;\r\n}\r\n\r\n.story_social_wrapper {\r\n  width: 100%;\r\n  /* padding: 0 5%; */\r\n  font-family: sweet-sans-pro, sans-serif;\r\n}\r\n\r\n.story_social_section {\r\n  width: 100%;\r\n  min-height: 5vh;\r\n  padding: 10px 0;\r\n  border-top: 1px solid var(--line-color);\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_social_section {\r\n    min-height: 10vh;\r\n    flex-direction: row;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.story_social_label {\r\n  width: 100%;\r\n  min-height: 5vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n  text-transform: uppercase;\r\n  font-size: 12px;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_social_label {\r\n    width: 15%;\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n.story_social_buttons {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_social_buttons {\r\n    width: 85%;\r\n  }\r\n}\r\n\r\n.story_social_author {\r\n  width: 100%;\r\n  text-transform: uppercase;\r\n  font-size: 12px;\r\n  line-height: 1.1;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .story_social_author {\r\n    width: 85%;\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n.story_social_bio {\r\n  /* margin-top: 10px; */\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  text-transform: none;\r\n  font-size: 1.2rem;\r\n}\r\n.border_btn_button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n.border_btn_button svg {\r\n  font-size: 1.2rem;\r\n}\r\n\r\n/* ////////////////////////////////////////border btn /////////////////////////////////////// */\r\n\r\n.border_btn {\r\n  display: inline-block;\r\n}\r\n\r\n.border_btn_button {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n  text-transform: uppercase;\r\n  border: 1px solid var(--text-color);\r\n  color: var(--text-color);\r\n  background-color: transparent;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.border_btn_button:hover {\r\n  background-color: var(--text-color);\r\n  color: var(--body-bg-color);\r\n  border-color: var(--body-bg-color);\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .border_btn_button {\r\n    padding: 8px 16px;\r\n    font-size: 0.875rem;\r\n  }\r\n}\r\n\r\n.container_fullImage {\r\n  /* padding: 20px; */\r\n  max-width: 100vw;\r\n  flex: 1;\r\n  margin: 0 auto;\r\n  position: relative;\r\n}\r\n.full-image-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  /* aspect-ratio: 1; */\r\n}\r\n\r\n.full-image-wrapper img {\r\n  width: 100%;\r\n  height: auto;\r\n  object-fit: cover;\r\n  object-position: center;\r\n}\r\n\r\n/* Share Modal Css */\r\n\r\n.share__modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  transform: scale(1.1);\r\n  opacity: 0;\r\n  pointer-events: none;\r\n  visibility: hidden;\r\n  transition: visibility 0s linear 0.3s, opacity 0.3s 0s, transform 0.3s;\r\n  z-index: 9999;\r\n}\r\n.share-close-btn {\r\n  position: absolute;\r\n  top: -10px;\r\n  right: -10px;\r\n  cursor: pointer;\r\n  font-size: 23px;\r\n  color: #000;\r\n}\r\n.share__modal.show {\r\n  opacity: 1;\r\n  pointer-events: all;\r\n  visibility: visible;\r\n  transform: scale(1);\r\n  transition: visibility 0s linear 0s, opacity 0.25s 0s, transform 0.25s;\r\n}\r\n.share__content {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background-color: white;\r\n  width: 30rem;\r\n  /* height: 20rem; */\r\n  /* overflow-y: scroll; */\r\n  padding: 30px;\r\n  border-radius: 10px;\r\n  text-align: left;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n.share_body {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30px;\r\n  position: relative;\r\n}\r\n.share_body h2 {\r\n  font-size: 2.1rem;\r\n  color: #000000;\r\n  text-align: center;\r\n  line-height: 1;\r\n  font-family: rocky, sans-serif;\r\n  font-weight: 400;\r\n}\r\n\r\n.share__icons {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  justify-content: center;\r\n}\r\n.share_body h3 {\r\n  color: gray;\r\n  font-weight: 300;\r\n  font-size: 1rem;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  margin-bottom: 0.5rem;\r\n}\r\n#check-group {\r\n  animation: 0.32s ease-in-out 1.03s check-group;\r\n  transform-origin: center;\r\n}\r\n\r\n#check-group #check {\r\n  animation: 0.34s cubic-bezier(0.65, 0, 1, 1) 0.8s forwards check;\r\n  stroke-dasharray: 0, 75px;\r\n  stroke-linecap: round;\r\n  stroke-linejoin: round;\r\n}\r\n\r\n#check-group #outline {\r\n  animation: 0.38s ease-in outline;\r\n  transform: rotate(0deg);\r\n  transform-origin: center;\r\n}\r\n\r\n#check-group #white-circle {\r\n  animation: 0.35s ease-in 0.35s forwards circle;\r\n  transform: none;\r\n  transform-origin: center;\r\n}\r\n.copy_text_anim {\r\n  text-align: center;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  color: var(--text-colorblack);\r\n}\r\n\r\n.StoriesInfo_left_innercntr-full-width.embed-twitter {\r\n  position: relative;\r\n  aspect-ratio: 16/9;\r\n}\r\n.flex-all-embed {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n@keyframes outline {\r\n  from {\r\n    stroke-dasharray: 0, 345.576px;\r\n  }\r\n  to {\r\n    stroke-dasharray: 345.576px, 345.576px;\r\n  }\r\n}\r\n@keyframes circle {\r\n  from {\r\n    transform: scale(1);\r\n  }\r\n  to {\r\n    transform: scale(0);\r\n  }\r\n}\r\n@keyframes check {\r\n  from {\r\n    stroke-dasharray: 0, 75px;\r\n  }\r\n  to {\r\n    stroke-dasharray: 75px, 75px;\r\n  }\r\n}\r\n@keyframes check-group {\r\n  from {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.09);\r\n  }\r\n  to {\r\n    transform: scale(1);\r\n  }\r\n}\r\n.share__icon {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background: #f3f3f3;\r\n  border-radius: 50%;\r\n  width: 58px;\r\n  height: 58px;\r\n  color: #333;\r\n\r\n  transition: background 0.3s, color 0.3s;\r\n}\r\n.share__icon svg {\r\n  font-size: 22px;\r\n}\r\n.share__icon:hover {\r\n  background: rgba(0, 0, 0, 1);\r\n  color: #fff;\r\n}\r\n\r\n.share__icon button {\r\n  all: unset;\r\n  cursor: pointer;\r\n}\r\n.link_copycntr {\r\n  width: 100%;\r\n  padding: 10px;\r\n  background-color: #f3f3f3;\r\n  border-radius: 5px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 10px;\r\n}\r\n.link_copycntr input {\r\n  width: 100%;\r\n  background: none;\r\n  outline: none;\r\n  border: none;\r\n  font-family: sweet-sans-pro, sans-serif;\r\n  color: var(--text-colorblack);\r\n}\r\n.link_copycntr svg {\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: var(--text-colorblack);\r\n}\r\n@media only screen and (max-width: 992px) {\r\n  .share-close-btn {\r\n    top: -15px;\r\n    right: -15px;\r\n  }\r\n  .card-meta_meta {\r\n    row-gap: 16px;\r\n  }\r\n  .contr-fluid {\r\n    gap: 1em;\r\n  }\r\n  .Stories_caption {\r\n    font-size: 0.875rem;\r\n  }\r\n  .Stories_courtesy {\r\n    font-size: 0.875rem;\r\n  }\r\n}\r\n@media only screen and (max-width: 767px) {\r\n  .share__content {\r\n    width: 85vw;\r\n  }\r\n  .share_body h2 {\r\n    font-size: 2rem;\r\n  }\r\n  .share__icon {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n  .share__icon svg {\r\n    font-size: 18px;\r\n  }\r\n  .share__icons {\r\n    justify-content: space-around;\r\n  }\r\n}\r\n@media only screen and (max-width: 425px) {\r\n  .share_body h2 {\r\n    font-size: 1.5rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAIA;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;AAgBA;;;;;;AASA;EACE;;;;;;AAKF;EACE;;;;;;AAKF;EACE;;;;;AAKF;;;;;;;;AAOA;;;;;;;;;AAQA;;;;;;;;;;;AAUA;;;;;;;;AAOA;;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;EACE;;;;EAGA;;;;EAGA;;;;EAIA;;;;;AAMF;EACE;;;EAGA;;;;;;EAKA;;;;;;;EAOA;;;;;;EAMA;;;;;EAKA;;;;EAGA;;;;;AAIF;EACE;;;;;EAIA;;;;EAGA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAOF;;;AAIA;;;AAGA;;;AAGA;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;AAOA;;;;;AAKA;;;;;;;;;;;AAWA;;;;AAGA;EACE;;;;EAGA;;;;;EAIA;;;;;;AAMF;;;;;AAKA;EACE;;;;;;AAMF;;;;;;;;;;;AAcA;;;;;;;;;AAUA;EACE;;;;;;;;;EAUA;;;;;;;AAOF;;;;;;;;AASA;;;;AAIA;EACE;;;;;;;AAOF;;;;;AAKA;EACE;;;;;;AAKF;EACE;;;;;;AAKF;;;;;;AAMA;;;;;AAIA;;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;AAUA;;;;;;;;;;;AAUA;;;;;;;;AAOA;;;;;;;;;;;;;AAYA;;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;AAKA;;;;AAIA;EACE;;;;;;EAKA;;;;;AAIF;EACE;;;;EAGA;;;;;AAIF;EACE;;;;;;EAKA;;;;;AAIF;EACE;;;;;AAIF;;;;;AAKA;EACE;;;;;;AAKF;EACE;;;;;AAIF;;;;AAIA;EACE;;;;;AAOF;;;;;AAKA;;;;AAIA;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;AAMA;;;;;AAMA;;;;;;;AAOA;;;;;AAMA;EACE;;;;;AAKF;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAQA;;;;;;;;;AASA;;;;;AAMA;;;;;;;;;AASA;EACE;;;;;;;AAOF;;;;;;;;;;AAUA;EACE;;;;;;AAMF;;;;;;;;AAQA;EACE;;;;;AAKF;;;;;;;AAOA;EACE;;;;;;AAMF;;;;;;;;AAQA;;;;;;AAKA;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;AAMA;EACE;;;;;;AAMF;;;;;;;AAOA;;;;;;AAOA;;;;;;;AASA;;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;AAQA;;;;;;;;AAOA;;;;;;;;;;;;;AAcA;;;;;;;AAMA;;;;;;;;;AASA;;;;;;;AAMA;;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAIA;;;;;;;AAMA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;AAKA;;;;;AAIA;;;;;;;;;;;AAUA;;;;;;;;;AAQA;;;;;;AAKA;EACE;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAIF;EACE;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;AAIF;EACE"}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}