module.exports = {

"[externals]/next/dist/compiled/next-server/pages.runtime.dev.js [external] (next/dist/compiled/next-server/pages.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/pages.runtime.dev.js", () => require("next/dist/compiled/next-server/pages.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("react/jsx-dev-runtime", () => require("react/jsx-dev-runtime"));

module.exports = mod;
}}),
"[externals]/react/jsx-runtime [external] (react/jsx-runtime, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("react/jsx-runtime", () => require("react/jsx-runtime"));

module.exports = mod;
}}),
"[externals]/react [external] (react, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("react", () => require("react"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/react-dom [external] (react-dom, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("react-dom", () => require("react-dom"));

module.exports = mod;
}}),
"[project]/src/pages/_document.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Document)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$document$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/document.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/script.js [ssr] (ecmascript)");
;
;
;
function Document() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$document$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["Html"], {
        lang: "en",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$document$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["Head"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                        rel: "stylesheet",
                        href: "https://use.typekit.net/smz6nuo.css"
                    }, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 8,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                        rel: "stylesheet",
                        href: "https://use.typekit.net/izt7oyh.css"
                    }, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 9,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                        rel: "stylesheet",
                        href: "https://use.typekit.net/gty1suo.css"
                    }, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 10,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                        rel: "preload",
                        href: "https://securepubads.g.doubleclick.net/tag/js/gpt.js",
                        as: "script"
                    }, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 11,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
                        async: true,
                        src: "https://www.googletagmanager.com/gtag/js?id=G-3G2PKDJZCD"
                    }, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 17,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
                        dangerouslySetInnerHTML: {
                            __html: `
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'G-3G2PKDJZCD');
          `
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 21,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/pages/_document.jsx",
                lineNumber: 7,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("body", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$document$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["Main"], {}, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 33,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                        async: true,
                        src: "https://securepubads.g.doubleclick.net/tag/js/gpt.js",
                        strategy: "beforeInteractive"
                    }, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$document$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["NextScript"], {}, void 0, false, {
                        fileName: "[project]/src/pages/_document.jsx",
                        lineNumber: 39,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/pages/_document.jsx",
                lineNumber: 32,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/pages/_document.jsx",
        lineNumber: 6,
        columnNumber: 5
    }, this);
}
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/next-themes [external] (next-themes, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("next-themes");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/helpers/MenuData.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "menus": (()=>menus)
});
const menus = [
    {
        name: "Motoring",
        link: "/motoring",
        submenus: [
            {
                name: "Cars",
                link: "/motoring/cars"
            },
            {
                name: "Bikes",
                link: "/motoring/bikes"
            },
            {
                name: "Vintage & Classics",
                link: "/motoring/vintage-and-classics"
            }
        ]
    },
    {
        name: "Yachting & Aviation",
        link: "/yachting-and-aviation",
        submenus: [
            {
                name: "Yachting",
                link: "/yachting-and-aviation/yachting"
            },
            {
                name: "Aviation",
                link: "/yachting-and-aviation/aviation"
            },
            {
                name: "Cruises & Expeditions",
                link: "/yachting-and-aviation/cruises-and-expeditions"
            }
        ]
    },
    {
        name: "Style",
        link: "/style",
        submenus: [
            {
                name: "Timepieces",
                link: "/style/timepieces"
            },
            {
                name: "Jewellery & Accessories",
                link: "/style/jewellery-and-accessories"
            },
            {
                name: "Fashion & Beauty",
                link: "/style/fashion-and-beauty"
            },
            {
                name: "Bespoke",
                link: "/style/bespoke"
            }
        ]
    },
    {
        name: "Home & Design",
        link: "/home-and-design",
        submenus: [
            {
                name: "Interiors & Architecture",
                link: "/home-and-design/interiors-and-architecture"
            },
            {
                name: "Real Estate",
                link: "/home-and-design/real-estate"
            },
            {
                name: "Art",
                link: "/home-and-design/art"
            }
        ]
    },
    {
        name: "Travel",
        link: "/travel",
        submenus: [
            {
                name: "Wellness & Spas",
                link: "/travel/wellness-and-spas"
            },
            {
                name: "India",
                link: "/travel/india"
            },
            {
                name: "International",
                link: "/travel/international"
            }
        ]
    },
    {
        name: "Food & Drink",
        link: "/food-and-drink",
        submenus: [
            {
                name: "Gastronomy",
                link: "/food-and-drink/gastronomy"
            },
            {
                name: "Spirits",
                link: "/food-and-drink/spirits"
            }
        ]
    }
];
}}),
"[project]/src/components/common/Menu.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/link.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$gr$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/gr/index.mjs [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$MenuData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/helpers/MenuData.jsx [ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
const Menu = ({ openMenu, setOpenMenu })=>{
    const [openSubMenu, setOpenSubMenu] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        document.body.classList.toggle("overflow-hidden", openMenu);
        return ()=>document.body.classList.remove("overflow-hidden");
    }, [
        openMenu
    ]);
    const handleSubMenuToggle = (index)=>{
        if (openSubMenu === index) {
            setOpenSubMenu(null); // Close submenu if it's already open
        } else {
            setOpenSubMenu(index); // Open the selected submenu and hide others
        }
    };
    // Handle the back button click to reset and show all menu items
    const handleBackButtonClick = ()=>{
        setOpenSubMenu(null); // Reset the submenu and show all menu items
    };
    const onClickHandler = ()=>{
        setOpenMenu(false); // Show
        setOpenSubMenu(null); // Close submenu if it's already open
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        className: "menu-cont",
        style: {
            transform: openMenu ? "translateX(0%)" : "translateX(100%)"
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
            className: "menu-inner",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "manu-inner-block",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "menu-top",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "menu-back flex-all ",
                                    style: {
                                        opacity: openSubMenu !== null ? "1" : "0"
                                    },
                                    onClick: handleBackButtonClick,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["MdOutlineArrowBackIosNew"], {}, void 0, false, {
                                        fileName: "[project]/src/components/common/Menu.jsx",
                                        lineNumber: 54,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Menu.jsx",
                                    lineNumber: 49,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "menu-search-close",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "menu-close flex-all",
                                        onClick: ()=>setOpenMenu(false),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["MdClose"], {}, void 0, false, {
                                            fileName: "[project]/src/components/common/Menu.jsx",
                                            lineNumber: 62,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Menu.jsx",
                                        lineNumber: 58,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Menu.jsx",
                                    lineNumber: 56,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/Menu.jsx",
                            lineNumber: 48,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "menu-main",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "menu-item-wrapper",
                                    children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$MenuData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["menus"].map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            children: openSubMenu === item.name ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "submenu-body d-flex",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: "submenu-head",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            onClick: ()=>setOpenMenu(false),
                                                            href: item.link,
                                                            className: "submenu-title",
                                                            children: item.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/Menu.jsx",
                                                            lineNumber: 74,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/common/Menu.jsx",
                                                        lineNumber: 73,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: "submenu-main-body",
                                                        children: item.submenus.map((submenuItem, submenuIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                className: "submenu-items",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: submenuItem.link,
                                                                    className: "submenu-name",
                                                                    onClick: onClickHandler,
                                                                    children: submenuItem.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/common/Menu.jsx",
                                                                    lineNumber: 88,
                                                                    columnNumber: 31
                                                                }, this)
                                                            }, `submenu-item-${submenuIndex}`, false, {
                                                                fileName: "[project]/src/components/common/Menu.jsx",
                                                                lineNumber: 84,
                                                                columnNumber: 29
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/common/Menu.jsx",
                                                        lineNumber: 82,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/common/Menu.jsx",
                                                lineNumber: 72,
                                                columnNumber: 23
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: `menu-items ${openSubMenu === null || openSubMenu === item.name ? "" : "d-none"}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item?.link,
                                                        onClick: ()=>setOpenMenu(false),
                                                        className: "menu-name",
                                                        children: item.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/common/Menu.jsx",
                                                        lineNumber: 107,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        onClick: ()=>handleSubMenuToggle(item.name),
                                                        className: "menu-arrow",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["MdOutlineArrowForwardIos"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/common/Menu.jsx",
                                                            lineNumber: 118,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/common/Menu.jsx",
                                                        lineNumber: 114,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/common/Menu.jsx",
                                                lineNumber: 100,
                                                columnNumber: 23
                                            }, this)
                                        }, `manu-item-${index}`, false, {
                                            fileName: "[project]/src/components/common/Menu.jsx",
                                            lineNumber: 69,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Menu.jsx",
                                    lineNumber: 67,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "menu-extras",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "menu-ext-wrapper",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "menu-ext",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/about-us",
                                                    rel: "nofollow",
                                                    children: "About us"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/Menu.jsx",
                                                    lineNumber: 128,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Menu.jsx",
                                                lineNumber: 127,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "menu-ext",
                                                rel: "nofollow",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/contact-us",
                                                    rel: "nofollow",
                                                    children: "Contact us"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/Menu.jsx",
                                                    lineNumber: 133,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Menu.jsx",
                                                lineNumber: 132,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/Menu.jsx",
                                        lineNumber: 126,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Menu.jsx",
                                    lineNumber: 125,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/Menu.jsx",
                            lineNumber: 66,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "menu-btm",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "menu-ext",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                        className: "menu-follows-text",
                                        children: "Follow us"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Menu.jsx",
                                        lineNumber: 145,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "menu-follows",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "menu-follows-items",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                target: "_blank",
                                                rel: "noopener noreferrer",
                                                href: "https://www.instagram.com/robbreportindia/",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$gr$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["GrInstagram"], {}, void 0, false, {
                                                    fileName: "[project]/src/components/common/Menu.jsx",
                                                    lineNumber: 153,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Menu.jsx",
                                                lineNumber: 148,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/Menu.jsx",
                                            lineNumber: 147,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Menu.jsx",
                                        lineNumber: 146,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/Menu.jsx",
                                lineNumber: 144,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Menu.jsx",
                            lineNumber: 143,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/Menu.jsx",
                    lineNumber: 47,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/Menu.jsx",
                lineNumber: 46,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/Menu.jsx",
            lineNumber: 45,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/Menu.jsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Menu;
}}),
"[project]/src/utils/GetTheme.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/next-themes [external] (next-themes, esm_import)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
const GetTheme = ()=>{
    const { theme, setTheme } = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__["useTheme"])();
    return {
        theme,
        setTheme
    };
};
const __TURBOPACK__default__export__ = GetTheme;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[externals]/@gsap/react [external] (@gsap/react, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@gsap/react", () => require("@gsap/react"));

module.exports = mod;
}}),
"[externals]/gsap [external] (gsap, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("gsap", () => require("gsap"));

module.exports = mod;
}}),
"[project]/src/components/common/TransitionLoader.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/GetTheme.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$gsap$2f$react__$5b$external$5d$__$2840$gsap$2f$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@gsap/react [external] (@gsap/react, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/gsap [external] (gsap, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
const TransitionLoader = ()=>{
    const { theme, setTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"])();
    const [tl, setTl] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null); // State to hold the GSAP timeline
    // Initialize the GSAP timeline
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const timeline = __TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__["default"].timeline({
            paused: true
        });
        timeline.to(".modes_helpers .a", {
            duration: 0.5,
            scaleY: 1,
            ease: "power3.inOut",
            transformOrigin: "0 0"
        }).to(".modes_helpers .b", {
            duration: 0.5,
            scaleY: 1,
            ease: "power3.inOut",
            transformOrigin: "0 0"
        }, 0.3).set(".modes_helpers .a", {
            scaleY: 0,
            transformOrigin: "100% 100%"
        }, 0.8).to(".mode_toggle span i", 0.5, {
            y: 0,
            autoAlpha: 1,
            ease: "Power3.easeOut"
        }, 0.8).to(".modes_helpers .b", 0.5, {
            scaleY: 0,
            ease: "Power3.easeOut",
            transformOrigin: "100% 100%"
        }, 0.8);
        setTl(timeline);
    }, []);
    // Effect to react to theme changes and play/reverse the timeline
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (tl) {
            if (theme === "dark") {
                tl.reverse();
            } else {
                tl.play();
            }
        }
    }, [
        theme,
        tl
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        className: "modes_helpers",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                className: "a full_bgTransition"
            }, void 0, false, {
                fileName: "[project]/src/components/common/TransitionLoader.jsx",
                lineNumber: 73,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                className: "b full_bgTransition"
            }, void 0, false, {
                fileName: "[project]/src/components/common/TransitionLoader.jsx",
                lineNumber: 74,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/TransitionLoader.jsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = TransitionLoader;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/components/common/SearchButton.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/link.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ci$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/ci/index.mjs [ssr] (ecmascript)");
;
;
;
;
const SearchButton = ({ setVisible })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        onClick: ()=>setVisible(true),
        className: `search_btn light show dark`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
            className: "search-btn",
            "aria-label": "Search Button",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ci$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["CiSearch"], {}, void 0, false, {
                fileName: "[project]/src/components/common/SearchButton.jsx",
                lineNumber: 9,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/SearchButton.jsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/SearchButton.jsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = SearchButton;
}}),
"[project]/src/components/buttons/Button.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/link.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
;
;
;
const Button = ({ href = "#", target = null, onClick = null, className = "", children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
        children: onClick ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
            onClick: onClick,
            className: `button_base black w-inline-block ${className}`,
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/buttons/Button.jsx",
            lineNumber: 14,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
            href: href,
            target: target,
            className: `button_base black w-inline-block ${className}`,
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/buttons/Button.jsx",
            lineNumber: 21,
            columnNumber: 9
        }, this)
    }, void 0, false);
};
const __TURBOPACK__default__export__ = Button;
}}),
"[project]/src/components/buttons/BoxBorderBtn.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BoxBorderBtn": (()=>BoxBorderBtn)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/link.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
;
;
;
const BoxBorderBtn = ({ onClick = null, href = "#", isSearchDrawer, handleClose, children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
        children: onClick ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
            className: "border_btn",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                onClick: onClick,
                className: "border_btn_button",
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/buttons/BoxBorderBtn.jsx",
                lineNumber: 10,
                columnNumber: 25
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/buttons/BoxBorderBtn.jsx",
            lineNumber: 9,
            columnNumber: 21
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
            className: "border_btn",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                href: href,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                    onClick: isSearchDrawer ? handleClose : undefined,
                    className: "border_btn_button",
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/components/buttons/BoxBorderBtn.jsx",
                    lineNumber: 17,
                    columnNumber: 29
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/buttons/BoxBorderBtn.jsx",
                lineNumber: 16,
                columnNumber: 25
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/buttons/BoxBorderBtn.jsx",
            lineNumber: 15,
            columnNumber: 21
        }, this)
    }, void 0, false);
};
}}),
"[project]/src/helpers/ChipData.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "chipData": (()=>chipData)
});
const chipData = [
    {
        name: "Luxury Cars",
        slug: "/result/luxury-cars/1"
    },
    {
        name: "Private Jets",
        slug: "/result/private-jets/1"
    },
    {
        name: "Yachts",
        slug: "/result/yachts/1"
    },
    {
        name: "Timepieces",
        slug: "/result/timepieces/1"
    },
    {
        name: "Fine Dining",
        slug: "/result/fine-dining/1"
    },
    {
        name: "Vintage Cars",
        slug: "/result/vintage-cars/1"
    },
    {
        name: "Whisky",
        slug: "/result/whisky/1"
    },
    {
        name: "Luxury Watches",
        slug: "/result/luxury-watches/1"
    },
    {
        name: "Architecture",
        slug: "/result/architecture/1"
    },
    {
        name: "Interior Design",
        slug: "/result/interior-design/1"
    },
    {
        name: "Jewellery",
        slug: "/result/jewellery/1"
    },
    {
        name: "Electric Cars",
        slug: "/result/electric-cars/1"
    },
    {
        name: "Superbikes",
        slug: "/result/superbikes/1"
    },
    {
        name: "Luxury Hotels",
        slug: "/result/luxury-hotels/1"
    },
    {
        name: "Fashion",
        slug: "/result/fashion/1"
    },
    {
        name: "Art",
        slug: "/result/art/1"
    },
    {
        name: "Gastronomy",
        slug: "/result/gastronomy/1"
    },
    {
        name: "Future",
        slug: "/result/future/1"
    },
    {
        name: "Spirits",
        slug: "/result/spirits/1"
    }
];
}}),
"[externals]/html-react-parser [external] (html-react-parser, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("html-react-parser");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/utils/Constants.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Const": (()=>Const),
    "ProcessAPI": (()=>ProcessAPI)
});
const Const = {
    Token: "token",
    Session: "Session",
    LoggedInRolePermission: "Role",
    User: "User",
    LoggedIn: "LoggedIn",
    LoggedInUser: "LoggedInUser",
    STrue: true,
    SFalse: false,
    Success200: 200,
    Created201: 201,
    Invalid400: 400,
    UnAuth401: 401,
    Forbidden403: 403,
    NotFound404: 404,
    ServerError500: 500,
    BadGateway502: 502,
    ServiceUnavailable503: 503,
    GatewayTimeout504: 504,
    Redirect302: 302,
    Inactive: 0,
    Active: 1,
    Trash: 2,
    Draft: 3,
    Scheduled: 4,
    Limit: 20,
    Offset: 0,
    Brand: "Robb Report India",
    Link: ("TURBOPACK compile-time value", "https://backend.bms-rpsg-media.com"),
    ClientLink: ("TURBOPACK compile-time value", "https://www.robbreportindia.com")
};
const ProcessAPI = async (res)=>{
    if (res.status === Const.Success200 || res.status === Const.Created201) {
        const data = await res.json();
        return data;
    } else if (res.status === Const.Redirect302) {} else if (res.status === Const.Invalid400) {} else if (res.status === Const.UnAuth401) {
        localStorage.clear();
        window.location.href = "/signin";
    } else if (res.status === Const.NotFound404) {
        const data = await res.json();
        return data;
    // return {
    //   notFound: true,
    // };
    } else {
        throw new Error("Some error occurred");
    }
};
}}),
"[project]/src/utils/Util.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "binaryToNumber": (()=>binaryToNumber),
    "capitalizeFirstLetter": (()=>capitalizeFirstLetter),
    "checkPermission": (()=>checkPermission),
    "convertSlugOrTitle": (()=>convertSlugOrTitle),
    "convertToISTISOString": (()=>convertToISTISOString),
    "dateFormateWithTime": (()=>dateFormateWithTime),
    "dateFormateWithTimeShort": (()=>dateFormateWithTimeShort),
    "escapeXml": (()=>escapeXml),
    "extractTextFromDoc": (()=>extractTextFromDoc),
    "extractTwitterId": (()=>extractTwitterId),
    "formatDateAndTime": (()=>formatDateAndTime),
    "formatDateTimeHv": (()=>formatDateTimeHv),
    "formateDateShort": (()=>formateDateShort),
    "ga4FormatDate": (()=>ga4FormatDate),
    "generateSlug": (()=>generateSlug),
    "getAuthorText": (()=>getAuthorText),
    "getEmbedType": (()=>getEmbedType),
    "getTwitterUrl": (()=>getTwitterUrl),
    "hasHtmlTags": (()=>hasHtmlTags),
    "htmlParser": (()=>htmlParser),
    "isValidColor": (()=>isValidColor),
    "permissionCount": (()=>permissionCount),
    "statusLabel": (()=>statusLabel),
    "timeFormate": (()=>timeFormate)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$html$2d$react$2d$parser__$5b$external$5d$__$28$html$2d$react$2d$parser$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/html-react-parser [external] (html-react-parser, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$html$2d$react$2d$parser__$5b$external$5d$__$28$html$2d$react$2d$parser$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$html$2d$react$2d$parser__$5b$external$5d$__$28$html$2d$react$2d$parser$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
const htmlParser = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$externals$5d2f$html$2d$react$2d$parser__$5b$external$5d$__$28$html$2d$react$2d$parser$2c$__esm_import$29$__["default"])(data);
};
const dateFormateWithTime = (dateString)=>{
    const date = new Date(dateString);
    const monthNames = [
        "JANUARY",
        "FEBRUARY",
        "MARCH",
        "APRIL",
        "MAY",
        "JUNE",
        "JULY",
        "AUGUST",
        "SEPTEMBER",
        "OCTOBER",
        "NOVEMBER",
        "DECEMBER"
    ];
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;
    const minutesStr = minutes.toString().padStart(2, "0");
    return `${month} ${day}, ${year} ${hours}:${minutesStr}${ampm}`;
};
const formateDateShort = (dateString)=>{
    if (!dateString) return;
    const date = new Date(dateString);
    const day = String(date.getUTCDate()).padStart(2, "0");
    const month = String(date.getUTCMonth() + 1).padStart(2, "0");
    const year = String(date.getUTCFullYear()).slice(-2);
    return `${day}.${month}.${year}`;
};
const dateFormateWithTimeShort = (dateString)=>{
    if (!dateString) return;
    const date = new Date(dateString);
    const monthNames = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec"
    ];
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;
    const minutesStr = minutes.toString().padStart(2, "0");
    return `${month} ${day}, ${year}`;
};
const timeFormate = (date)=>{
    const dateObj = new Date(date);
    const options = {
        month: "long",
        day: "2-digit",
        year: "numeric",
        hour: "numeric",
        minute: "2-digit",
        hour12: true
    };
    const formattedDate = new Intl.DateTimeFormat("en-US", options).format(dateObj);
    const getTime = formattedDate.split(" at ")[1];
    return getTime;
};
const formatDateAndTime = (isoString)=>{
    const date = new Date(isoString);
    const now = new Date();
    const months = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec"
    ];
    const formattedDate = `${months[date.getUTCMonth()]} ${date.getUTCDate()}, ${date.getUTCFullYear()}`;
    const diffInMilliseconds = now - date;
    const diffInMinutes = Math.floor(diffInMilliseconds / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays >= 10) {
        return formattedDate;
    } else {
        let timeDifference;
        if (diffInDays === 1) {
            timeDifference = `${diffInDays} day ago`;
        } else if (diffInDays > 1) {
            timeDifference = `${diffInDays} days ago`;
        } else if (diffInHours >= 1) {
            timeDifference = `${diffInHours} hours ago`;
        } else {
            timeDifference = `${diffInMinutes} minutes ago`;
        }
        return timeDifference;
    }
};
const formatDateTimeHv = (inputDateTime)=>{
    const inputDate = new Date(inputDateTime);
    const currentDate = new Date();
    const diffTime = Math.abs(currentDate - inputDate);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const isSameDay = inputDate.getDate() === currentDate.getDate() && inputDate.getMonth() === currentDate.getMonth() && inputDate.getFullYear() === currentDate.getFullYear();
    if (isSameDay) {
        // Ensuring consistent 12-hour format (with AM/PM) across client and server
        return inputDate.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true
        });
    }
    if (diffDays <= 2) {
        return `${diffDays} day${diffDays > 1 ? "s" : ""} ago`;
    }
    // Return in "SEP 19, 2024" format for dates older than 2 days
    const options = {
        year: "numeric",
        month: "short",
        day: "numeric"
    };
    return inputDate.toLocaleDateString("en-US", options);
};
const checkPermission = (value, viewIndex)=>{
    if (value && typeof value == "number" && value > 0) {
        const permissions = Number(value).toString(2).split("").reverse().map((item)=>item === "1");
        Object.keys(viewIndex).forEach(function(key, value) {
            if (permissions.length > value) {
                viewIndex[key] = permissions[value];
            } else {
                viewIndex[key] = false;
            }
        });
        return viewIndex;
    } else {
        return false;
    }
};
const binaryToNumber = (value)=>{
    if (value) {
        const binaryToNumber = parseInt(value, 2);
        return binaryToNumber;
    } else {
        return 0;
    }
};
const permissionCount = (value)=>{
    if (value && typeof value == "number" && value > 0) {
        const permissions = Number(value).toString(2).split("");
        const total = permissions.length;
        const count = permissions.filter((item)=>item === "1").length;
        return {
            count,
            total
        };
    }
    return {
        count: 0,
        total: 0
    };
};
const isValidColor = (input)=>{
    try {
        const namedColors = [
            "black",
            "silver",
            "gray",
            "white",
            "maroon",
            "red",
            "purple",
            "fuchsia",
            "green",
            "lime",
            "olive",
            "yellow",
            "navy",
            "blue",
            "teal",
            "aqua"
        ];
        // Case-insensitive match against the list of named colors
        const colorRegex = new RegExp(`^(${namedColors.join("|")})$`, "i");
        // let regex = new RegExp(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/);
        let regex = new RegExp(/^(#?([a-f\d]{3,4}|[a-f\d]{6}|[a-f\d]{8})|rgb\((0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d)\)|rgba\((0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|255|25[0-4]|2[0-4]\d|1\d\d|0?\d?\d),(0|0?\.\d|1(\.0)?)\)|hsl\((0|360|35\d|3[0-4]\d|[12]\d\d|0?\d?\d),(0|100|\d{1,2})%,(0|100|\d{1,2})%\)|hsla\((0|360|35\d|3[0-4]\d|[12]\d\d|0?\d?\d),(0|100|\d{1,2})%,(0|100|\d{1,2})%,(0?\.\d|1(\.0)?)\))$/);
        return regex.test(input) || colorRegex.test(input);
    } catch (error) {
        return false;
    }
};
const ga4FormatDate = (dateString)=>{
    // Extract year, month, and day from the string
    const dateStringParam = dateString.split("-");
    const year = dateStringParam[0];
    const month = parseInt(dateStringParam[1]);
    const day = dateStringParam[2];
    // Define an array for month names
    const monthNames = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December"
    ];
    const date = new Date(year, month - 1, day);
    const monthName = monthNames[date.getMonth()];
    const formattedDay = day.padStart(2, "0");
    return monthName + " " + formattedDay;
};
const generateSlug = (title)=>{
    let slug = title.toString() // Convert to string
    .toLowerCase() // Convert to lowercase
    .trim() // Trim leading/trailing whitespace
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/[^\w-]+/g, "") // Remove all non-word characters
    .replace(/--+/g, "-"); // Replace multiple hyphens with a single hyphen
    // Ensure the slug starts with a slash
    if (!slug.startsWith("/")) {
        slug = `/${slug}`;
    }
    return slug;
};
const statusLabel = (value)=>{
    let label = "";
    if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].Inactive) {
        label = "Unpubilled";
    } else if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].Active) {
        label = "Published";
    } else if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].Trash) {
        label = "Trash";
    } else if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].Draft) {
        label = "Draft";
    } else if (value === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].Scheduled) {
        label = "Scheduled";
    }
    return label;
};
const hasHtmlTags = (str)=>{
    const regex = /<\/?[a-z][\s\S]*>/i;
    return regex.test(str);
};
const getEmbedType = (url)=>{
    const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
    const instagramRegex = /(?:https?:\/\/)?(?:www\.)?instagram\.com\/(?:p|tv|reel)\/([A-Za-z0-9_-]+)/;
    const twitterRegex = /(?:https?:\/\/)?(?:(?:www\.|platform\.)?(?:twitter|x)\.com\/(?:(?:\w+\/status\/[0-9]+)|(?:embed\/Tweet\.html\?id=[0-9]+)))/;
    const facebookPostOrVideoRegex = /(?:https?:\/\/)?(?:www\.)?facebook\.com\/(?:[^\/\n\s]+\/posts\/|(?:video\.php\?v=|watch\/))([0-9]+)/;
    if (youtubeRegex.test(url)) {
        return "youtube";
    } else if (instagramRegex.test(url)) {
        return "instagram";
    } else if (twitterRegex.test(url)) {
        return "twitter";
    } else if (facebookPostOrVideoRegex.test(url)) {
        return "facebook";
    }
};
const extractTwitterId = (embedUrl)=>{
    const match = embedUrl.split("id=")[1];
    return match;
};
const getTwitterUrl = (embedUrl)=>{
    const tweetId = new URL(embedUrl).searchParams.get("id");
    const tweetUrl = `https://twitter.com/i/web/status/${tweetId}`;
    return tweetUrl;
};
const getAuthorText = (prefix = "By", author = [], contributor = [])=>{
    const list = author.length ? author : contributor;
    const name = list[0]?.name || list[0] || "";
    const count = list.length - 1;
    return name ? `${prefix} ${name}${count ? ` +${count} More` : ""}` : "";
};
const extractTextFromDoc = (doc)=>{
    const output = [];
    function extractTextFromContent(contentArray) {
        return contentArray?.map((node)=>{
            if (node.type === "text") return node.text || "";
            if (node.content) return extractTextFromContent(node.content);
            return "";
        }).join("") || "";
    }
    if (Array.isArray(doc.content)) {
        for (const node of doc.content){
            if (node.type === "paragraph" || node.type === "heading") {
                const text = extractTextFromContent(node.content || []);
                if (text.trim()) output.push(text.trim());
            }
        }
    }
    return output.join(" ");
};
const convertToISTISOString = (utcISOString)=>{
    if (!utcISOString) return "";
    const date = new Date(utcISOString);
    const istOffsetMs = 5.5 * 60 * 60 * 1000;
    const istDate = new Date(date.getTime() + istOffsetMs);
    return istDate.toISOString().replace("Z", "+05:30");
};
const escapeXml = (unsafe)=>{
    return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
};
const convertSlugOrTitle = (input = "", toTitle = true)=>{
    if (!input) return "";
    const trimmedText = input.trim();
    if (toTitle) {
        return trimmedText.split("-").join(" ");
    } else {
        return trimmedText.toLowerCase().split(" ").filter(Boolean).join("-");
    }
};
const capitalizeFirstLetter = (str)=>{
    if (!str) return '';
    return str.replace(/\b\w/g, (char)=>char.toUpperCase());
};
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/components/common/SearchDrawer.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io/index.mjs [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/gsap [external] (gsap, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$buttons$2f$Button$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/buttons/Button.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ci$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/ci/index.mjs [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$buttons$2f$BoxBorderBtn$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/buttons/BoxBorderBtn.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/router.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$ChipData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/helpers/ChipData.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Util.jsx [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
const defaultFilter = {
    sortBy: -1,
    dateRange: 0,
    subcategoryIds: [],
    tagIds: [],
    writerIds: []
};
const SearchDrawer = ({ visible, filters = {}, selectedFilter, setVisible, setSelectedFilter })=>{
    const drawerRef = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useRef"])(null);
    const [openAccordions, setOpenAccordions] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    const [currentFilter, setCurrentFilter] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        document.body.classList.toggle("overflow-hidden", visible);
        setCurrentFilter(selectedFilter);
        return ()=>document.body.classList.remove("overflow-hidden");
    }, [
        visible
    ]);
    const toggleAccordion = (key)=>setOpenAccordions({
            [key]: !openAccordions[key]
        });
    const handleClose = ()=>setVisible(false);
    const handleFilter = (value, key, isRadio = false)=>{
        setCurrentFilter((prev)=>{
            let updated = {
                ...prev
            };
            if (isRadio) {
                if (updated[key] === value) return updated;
                updated[key] = value;
            } else {
                const values = new Set(updated[key] || []);
                values.has(value) ? values.delete(value) : values.add(value);
                updated[key] = Array.from(values);
            }
            return updated;
        });
    };
    const handleClearFilter = (key)=>{
        const clearedValue = {
            [key]: defaultFilter[key]
        };
        setCurrentFilter((prev)=>({
                ...prev,
                ...clearedValue
            }));
    };
    const handleSaveFilter = ()=>{
        setSelectedFilter(currentFilter);
        handleClose();
    };
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (!drawerRef.current) return;
        const container = drawerRef.current.querySelector("#list_container");
        const duration = 0.5;
        if (visible) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__["gsap"].set(drawerRef.current, {
                pointerEvents: "auto"
            });
            __TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__["gsap"].timeline().to(drawerRef.current, {
                opacity: 1,
                duration,
                ease: "power2.out"
            }).to(container, {
                right: "0%",
                duration,
                ease: "power2.out",
                delay: -0.3
            });
        } else {
            __TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__["gsap"].timeline().to(container, {
                right: "-100%",
                duration: duration + 0.2,
                ease: "power2.inOut"
            }).to(drawerRef.current, {
                opacity: 0,
                duration,
                ease: "power2.inOut",
                delay: -0.3,
                onComplete: ()=>__TURBOPACK__imported__module__$5b$externals$5d2f$gsap__$5b$external$5d$__$28$gsap$2c$__cjs$29$__["gsap"].set(drawerRef.current, {
                        pointerEvents: "none"
                    })
            });
        }
    }, [
        visible
    ]);
    const [text, setText] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])("");
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const handleSearch = ()=>{
        const queryText = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["convertSlugOrTitle"])(text, false);
        setVisible(false);
        router.push(`/result/${queryText}/1`);
        setText("");
    };
    const inputSearchRef = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useRef"])();
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (inputSearchRef.current) {
            if (visible) {
                inputSearchRef.current.focus();
            } else {
                inputSearchRef.current.blur();
            }
        }
    }, [
        visible
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        id: "drawer_container",
        className: "Search_drawer",
        ref: drawerRef,
        onClick: handleClose,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
            id: "list_container",
            onClick: (e)=>e.stopPropagation(),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "search_drawer filter_title_row",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                        className: "close_icon",
                        onClick: handleClose,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["IoIosClose"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/SearchDrawer.jsx",
                            lineNumber: 141,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/SearchDrawer.jsx",
                        lineNumber: 140,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/common/SearchDrawer.jsx",
                    lineNumber: 139,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "drawer_body drawer_body_search",
                    "data-lenis-prevent": true,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("form", {
                            onSubmit: (e)=>{
                                e.preventDefault();
                                handleSearch();
                            },
                            id: "search_drawer",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                    ref: inputSearchRef,
                                    type: "text",
                                    name: "query",
                                    placeholder: "Search",
                                    value: text || "",
                                    onChange: (e)=>setText(e.target.value)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/SearchDrawer.jsx",
                                    lineNumber: 152,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ci$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["CiSearch"], {
                                    onClick: ()=>handleSearch()
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/SearchDrawer.jsx",
                                    lineNumber: 160,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/SearchDrawer.jsx",
                            lineNumber: 145,
                            columnNumber: 11
                        }, this),
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$ChipData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["chipData"].length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "chipContainer search_chipContainer",
                            children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$ChipData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["chipData"]?.map((data, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$buttons$2f$BoxBorderBtn$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["BoxBorderBtn"], {
                                    isSearchDrawer: true,
                                    handleClose: handleClose,
                                    href: data?.slug || "#",
                                    children: data?.name || ""
                                }, `Search-${index}`, false, {
                                    fileName: "[project]/src/components/common/SearchDrawer.jsx",
                                    lineNumber: 165,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/SearchDrawer.jsx",
                            lineNumber: 163,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/SearchDrawer.jsx",
                    lineNumber: 144,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/SearchDrawer.jsx",
            lineNumber: 138,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/SearchDrawer.jsx",
        lineNumber: 132,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = SearchDrawer;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/components/common/Header.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$MenuData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/helpers/MenuData.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/link.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Menu$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Menu.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$rx$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/rx/index.mjs [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/next-themes [external] (next-themes, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TransitionLoader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/TransitionLoader.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/GetTheme.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SearchButton$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/SearchButton.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SearchDrawer$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/SearchDrawer.jsx [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TransitionLoader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SearchDrawer$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TransitionLoader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SearchDrawer$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Header = ()=>{
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const [openMenu, setOpenMenu] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const [showFilter, setShowFilter] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const { theme, setTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"])();
    const [isHydrated, setIsHydrated] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        setIsHydrated(true); // Ensures that we only render after the first render on the client
    }, []);
    // Render nothing until hydration is complete to prevent mismatch
    if (!isHydrated) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("header", {
                className: "nav _bgCntr",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "nav-content",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                className: "brand",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    src: "/assets/images/logo/RR final logo.png",
                                    width: 1000,
                                    height: 1000,
                                    alt: "Robb Report India"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Header.jsx",
                                    lineNumber: 34,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Header.jsx",
                                lineNumber: 33,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "links nav-items",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("ul", {
                                    className: "menu-linksul",
                                    children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$MenuData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["menus"].map((item, index)=>{
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                            className: "menu-item",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                className: pathname.startsWith(item.link) ? "nav_active" : "",
                                                href: item?.link || "#",
                                                children: item.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Header.jsx",
                                                lineNumber: 46,
                                                columnNumber: 21
                                            }, this)
                                        }, `nav-item-${index}`, false, {
                                            fileName: "[project]/src/components/common/Header.jsx",
                                            lineNumber: 45,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Header.jsx",
                                    lineNumber: 42,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Header.jsx",
                                lineNumber: 41,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SearchButton$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                setVisible: setShowFilter
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Header.jsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "material-icons menu",
                                onClick: ()=>{
                                    setOpenMenu(!openMenu);
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$rx$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["RxHamburgerMenu"], {}, void 0, false, {
                                    fileName: "[project]/src/components/common/Header.jsx",
                                    lineNumber: 77,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Header.jsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Header.jsx",
                        lineNumber: 32,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SearchDrawer$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                        visible: showFilter,
                        setVisible: setShowFilter
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Header.jsx",
                        lineNumber: 80,
                        columnNumber: 10
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Header.jsx",
                lineNumber: 31,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Menu$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                openMenu: openMenu,
                setOpenMenu: setOpenMenu
            }, void 0, false, {
                fileName: "[project]/src/components/common/Header.jsx",
                lineNumber: 85,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = Header;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/components/common/Footer.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/link.js [ssr] (ecmascript)");
;
;
;
;
const Footer = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "footer",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "containerWrapper",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "wrapper_footer",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "footer_links",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "wrapper_footer_links",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "flexbox_footer",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "footer-left-block",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "div-block-2",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "footer-brand",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        src: "/assets/images/logo/RR final logo.png",
                                                        fill: true,
                                                        alt: "Robb Report India"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/common/Footer.jsx",
                                                        lineNumber: 85,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 84,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 83,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/Footer.jsx",
                                            lineNumber: 82,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "footer-right-block",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "div-block-3",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "title_footer",
                                                            children: "Robb Report India"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/Footer.jsx",
                                                            lineNumber: 95,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "links_flex",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/about-us",
                                                                    className: "footer_link w-inline-block ",
                                                                    rel: "nofollow",
                                                                    children: "About Us"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                                    lineNumber: 97,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/contact-us",
                                                                    className: "footer_link w-inline-block ",
                                                                    rel: "nofollow",
                                                                    children: "Contact Us"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                                    lineNumber: 104,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/common/Footer.jsx",
                                                            lineNumber: 96,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 94,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "div-block-3",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "title_footer",
                                                            children: "Support"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/Footer.jsx",
                                                            lineNumber: 114,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "links_flex",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/privacy-policy",
                                                                    className: "footer_link w-inline-block",
                                                                    children: "Privacy Policy"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                                    lineNumber: 116,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/terms-of-use",
                                                                    className: "footer_link w-inline-block",
                                                                    children: "Terms Of Use"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                                    lineNumber: 122,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: "/disclaimer",
                                                                    className: "footer_link w-inline-block",
                                                                    children: "Disclaimer"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                                    lineNumber: 128,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/common/Footer.jsx",
                                                            lineNumber: 115,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 113,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "div-block-3",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "title_footer",
                                                            children: "Follow Us"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/Footer.jsx",
                                                            lineNumber: 137,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "links_flex",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                href: "https://www.instagram.com/robbreportindia/",
                                                                target: "_blank",
                                                                rel: "noopener noreferrer",
                                                                className: "footer_link w-inline-block",
                                                                children: "Instagram"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                                lineNumber: 139,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/Footer.jsx",
                                                            lineNumber: 138,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 136,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/common/Footer.jsx",
                                            lineNumber: 93,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/common/Footer.jsx",
                                    lineNumber: 81,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 80,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Footer.jsx",
                            lineNumber: 79,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Footer.jsx",
                        lineNumber: 10,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/common/Footer.jsx",
                    lineNumber: 9,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/Footer.jsx",
                lineNumber: 8,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "sub-footer",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "containerWrapper",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "wrapper_footer pb-0",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "flexbox_sub-footer",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "sub-footer-img-block",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        src: "/assets/images/logo/RPSG_Group_Logo.webp",
                                        fill: true,
                                        alt: "RP - Sanjiv Goenka Group Logo"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 177,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Footer.jsx",
                                    lineNumber: 176,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                    className: "footer-vr-line"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Footer.jsx",
                                    lineNumber: 183,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                    className: "footer-tag-line",
                                    children: "Robb Report India is published by RP - Sanjiv Goenka Group under license from Robb Report Media, LLC, a subsidiary of Penske Media Corporation."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Footer.jsx",
                                    lineNumber: 184,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                    className: "footer-vr-line"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Footer.jsx",
                                    lineNumber: 189,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "sub-footer-img-block-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        src: "/assets/images/logo/RPSG_Media_Logo.png",
                                        fill: true,
                                        alt: "RPSG - Media Logo"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 191,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/Footer.jsx",
                                    lineNumber: 190,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/Footer.jsx",
                            lineNumber: 175,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Footer.jsx",
                        lineNumber: 174,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/common/Footer.jsx",
                    lineNumber: 173,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/Footer.jsx",
                lineNumber: 172,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = Footer;
}}),
"[project]/src/components/common/SideBtnWrap.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/GetTheme.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dynamic$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dynamic.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io5/index.mjs [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
const BackToTop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dynamic$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/src/components/common/BacktoTop.jsx [ssr] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/src/components/common/BacktoTop.jsx [client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false
});
const SideBtnWrap = ()=>{
    const { theme, setTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$GetTheme$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"])();
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        setMounted(true);
    }, []);
    if (!mounted) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        id: "sideBtn_container",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(BackToTop, {}, void 0, false, {
                fileName: "[project]/src/components/common/SideBtnWrap.jsx",
                lineNumber: 22,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "toggle_cntr",
                onClick: ()=>setTheme(theme === "dark" ? "light" : "dark"),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: `icon ${theme === "dark" ? "scale-1" : "scale-0"}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["IoSunnyOutline"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/SideBtnWrap.jsx",
                            lineNumber: 28,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/SideBtnWrap.jsx",
                        lineNumber: 27,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: `icon ${theme === "dark" ? "scale-0" : "scale-1"}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$ssr$5d$__$28$ecmascript$29$__["IoMoonOutline"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/SideBtnWrap.jsx",
                            lineNumber: 31,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/SideBtnWrap.jsx",
                        lineNumber: 30,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/SideBtnWrap.jsx",
                lineNumber: 23,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/SideBtnWrap.jsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = SideBtnWrap;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/components/layouts/Layout.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>Layout)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Header$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Header.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Footer$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Footer.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SideBtnWrap$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/SideBtnWrap.jsx [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Header$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SideBtnWrap$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Header$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SideBtnWrap$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
function Layout({ menu, children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Header$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/layouts/Layout.jsx",
                lineNumber: 10,
                columnNumber: 7
            }, this),
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Footer$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/layouts/Layout.jsx",
                lineNumber: 12,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$SideBtnWrap$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/layouts/Layout.jsx",
                lineNumber: 13,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/components/common/Loader.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
;
;
const Loader = ({ className = "loader-cont" })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        className: className,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
            className: "MuiBox-root css-0",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                className: "MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-indeterminate css-lrbo11-MuiLinearProgress-root",
                role: "progressbar",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                        className: "MuiLinearProgress-bar1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Loader.jsx",
                        lineNumber: 11,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                        className: "MuiLinearProgress-bar2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Loader.jsx",
                        lineNumber: 12,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Loader.jsx",
                lineNumber: 7,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/Loader.jsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/Loader.jsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Loader;
}}),
"[externals]/@studio-freight/tempus [external] (@studio-freight/tempus, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("@studio-freight/tempus");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[externals]/@studio-freight/lenis [external] (@studio-freight/lenis, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("@studio-freight/lenis");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/components/smoothScroll/SmoothScroller.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>SmoothScroller)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$studio$2d$freight$2f$tempus__$5b$external$5d$__$2840$studio$2d$freight$2f$tempus$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/@studio-freight/tempus [external] (@studio-freight/tempus, esm_import)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$studio$2d$freight$2f$lenis__$5b$external$5d$__$2840$studio$2d$freight$2f$lenis$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/@studio-freight/lenis [external] (@studio-freight/lenis, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f40$studio$2d$freight$2f$tempus__$5b$external$5d$__$2840$studio$2d$freight$2f$tempus$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$externals$5d2f40$studio$2d$freight$2f$lenis__$5b$external$5d$__$2840$studio$2d$freight$2f$lenis$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f40$studio$2d$freight$2f$tempus__$5b$external$5d$__$2840$studio$2d$freight$2f$tempus$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$externals$5d2f40$studio$2d$freight$2f$lenis__$5b$external$5d$__$2840$studio$2d$freight$2f$lenis$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
function SmoothScroller() {
    const lenis = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useRef"])(null);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (lenis.current) lenis.current.scrollTo(0, {
            immediate: true
        });
    }, [
        pathname,
        searchParams,
        lenis
    ]);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useLayoutEffect"])(()=>{
        lenis.current = new __TURBOPACK__imported__module__$5b$externals$5d2f40$studio$2d$freight$2f$lenis__$5b$external$5d$__$2840$studio$2d$freight$2f$lenis$2c$__esm_import$29$__["default"]({
            smoothWheel: true
        });
        const resize = setInterval(()=>{
            lenis.current.resize();
        }, 150);
        function onFrame(time) {
            lenis.current.raf(time);
        }
        const unsubscribe = __TURBOPACK__imported__module__$5b$externals$5d2f40$studio$2d$freight$2f$tempus__$5b$external$5d$__$2840$studio$2d$freight$2f$tempus$2c$__esm_import$29$__["default"].add(onFrame);
        return ()=>{
            unsubscribe();
            clearInterval(resize);
            lenis.current.destroy();
            lenis.current = null;
        };
    }, []);
    return null;
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/pages/_app.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>App)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/router.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/next-themes [external] (next-themes, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layouts$2f$Layout$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layouts/Layout.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TransitionLoader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/TransitionLoader.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Loader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Loader.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$smoothScroll$2f$SmoothScroller$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/smoothScroll/SmoothScroller.jsx [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layouts$2f$Layout$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TransitionLoader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$smoothScroll$2f$SmoothScroller$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layouts$2f$Layout$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TransitionLoader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$smoothScroll$2f$SmoothScroller$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function App({ Component, pageProps }) {
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const handleStart = (url, { shallow })=>{
            if (!shallow) {
                setIsLoading(true);
            }
        };
        const handleStop = ()=>setIsLoading(false);
        router.events.on("routeChangeStart", handleStart);
        router.events.on("routeChangeComplete", handleStop);
        router.events.on("routeChangeError", handleStop);
        return ()=>{
            router.events.off("routeChangeStart", handleStart);
            router.events.off("routeChangeComplete", handleStop);
            router.events.off("routeChangeError", handleStop);
        };
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$next$2d$themes__$5b$external$5d$__$28$next$2d$themes$2c$__esm_import$29$__["ThemeProvider"], {
            defaultTheme: "dark",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TransitionLoader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/pages/_app.jsx",
                    lineNumber: 54,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$smoothScroll$2f$SmoothScroller$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/pages/_app.jsx",
                    lineNumber: 55,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layouts$2f$Layout$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                    children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Loader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/pages/_app.jsx",
                        lineNumber: 57,
                        columnNumber: 28
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(Component, {
                        ...pageProps
                    }, void 0, false, {
                        fileName: "[project]/src/pages/_app.jsx",
                        lineNumber: 57,
                        columnNumber: 41
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/pages/_app.jsx",
                    lineNumber: 56,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/pages/_app.jsx",
            lineNumber: 53,
            columnNumber: 9
        }, this)
    }, void 0, false);
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[externals]/styled-jsx/style.js [external] (styled-jsx/style.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("styled-jsx/style.js", () => require("styled-jsx/style.js"));

module.exports = mod;
}}),
"[externals]/next/head.js [external] (next/head.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/head.js", () => require("next/head.js"));

module.exports = mod;
}}),
"[project]/src/components/seo/WebPageSchema.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
;
const WebPageSchema = ({ name, description, url })=>{
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        name: name,
        description: description,
        speakable: {
            "@type": "SpeakableSpecification",
            xpath: [
                "//title",
                "//meta[@name='description']/@content"
            ]
        },
        url: url
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(schemaData)
        }
    }, void 0, false, {
        fileName: "[project]/src/components/seo/WebPageSchema.jsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = WebPageSchema;
}}),
"[project]/src/components/seo/NewsMediaOrganizationSchema.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
;
;
const NewsMediaOrganizationSchema = ({ name, clientLink, logoUrl, address, contact, sameAs })=>{
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "NewsMediaOrganization",
        name: name,
        url: clientLink,
        logo: {
            "@type": "ImageObject",
            url: logoUrl
        },
        address: {
            "@type": "PostalAddress",
            streetAddress: address?.streetAddress,
            addressLocality: address?.addressLocality,
            addressRegion: address?.addressRegion,
            postalCode: address?.postalCode
        },
        contactPoint: {
            "@type": "ContactPoint",
            telephone: contact?.telephone,
            contactType: contact?.contactType,
            areaServed: contact?.areaServed,
            availableLanguage: contact?.availableLanguage,
            hoursAvailable: {
                opens: contact?.hoursAvailable?.opens,
                closes: contact?.hoursAvailable?.closes
            }
        },
        sameAs: sameAs
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(schemaData)
        }
    }, void 0, false, {
        fileName: "[project]/src/components/seo/NewsMediaOrganizationSchema.jsx",
        lineNumber: 41,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = NewsMediaOrganizationSchema;
}}),
"[project]/src/components/seo/SiteNavigationSchema.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$MenuData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/helpers/MenuData.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [ssr] (ecmascript)");
;
;
;
;
const SiteNavigationSchema = ()=>{
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "SiteNavigationElement",
        name: [],
        url: []
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$helpers$2f$MenuData$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["menus"].forEach((menu)=>{
        schemaData.name.push(menu.name);
        schemaData.url.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink}${menu.link}`);
        if (menu && menu.submenus && menu.submenus.length > 0) {
            menu.submenus.forEach((submenu)=>{
                if (submenu.name !== "All") {
                    schemaData.name.push(submenu.name);
                    schemaData.url.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink}${submenu.link}`);
                }
            });
        }
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
            __html: JSON.stringify(schemaData)
        }
    }, void 0, false, {
        fileName: "[project]/src/components/seo/SiteNavigationSchema.jsx",
        lineNumber: 26,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = SiteNavigationSchema;
}}),
"[project]/src/components/seo/SeoHeader.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/next/head.js [external] (next/head.js, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/router.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$WebPageSchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/WebPageSchema.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$NewsMediaOrganizationSchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/NewsMediaOrganizationSchema.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$SiteNavigationSchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/SiteNavigationSchema.jsx [ssr] (ecmascript)");
;
;
;
;
;
;
;
;
const SeoHeader = ({ meta = {}, type = "website" })=>{
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const defaultImage = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink}/favicon/favicon-192x192.png`;
    const canonical = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink}/${router.asPath?.slice(1)}`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__["default"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("title", {
                children: meta?.title || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "description",
                content: meta?.description || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 16,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "keywords",
                content: meta?.keywords || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            meta?.author && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "author",
                content: meta?.author || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 18,
                columnNumber: 24
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "publisher",
                content: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].Brand
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "robots",
                content: `${meta?.robots}, max-image-preview:large` || "noindex,nofollow, max-image-preview:large"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 20,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                rel: "canonical",
                href: meta?.canonical || canonical
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 27,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "fb:app_id",
                content: "446498535209610"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 29,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:locale",
                content: "en_IN"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 30,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:type",
                content: type
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 31,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:title",
                content: meta?.og?.title || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 32,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:description",
                content: meta?.og?.description || ""
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:url",
                content: canonical
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:site_name",
                content: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].Brand
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:image",
                content: meta?.og?.image || defaultImage
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 36,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:image:width",
                content: "1200"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                property: "og:image:height",
                content: "630"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "twitter:card",
                content: meta?.twitter?.card || "summary_large_image"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 40,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "twitter:title",
                content: meta?.twitter?.title || meta?.title
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 44,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "twitter:description",
                content: meta?.twitter?.description || meta?.description
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 48,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "twitter:site",
                content: "@robbreportindia"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 52,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "twitter:image",
                content: meta?.twitter?.image || defaultImage
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 53,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "twitter:creator",
                content: "@robbreportindia"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 57,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                charSet: "UTF-8"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 58,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                httpEquiv: "Content-Type",
                content: "text/html;charset=UTF-8"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                name: "viewport",
                content: "width=device-width, initial-scale=1.0"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 60,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                rel: "icon",
                type: "image/png",
                sizes: "16x16",
                href: "/favicon/favicon-16x16.png"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 61,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                rel: "icon",
                type: "image/png",
                sizes: "32x32",
                href: "/favicon/favicon-32x32.png"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                rel: "icon",
                type: "image/png",
                sizes: "192x192",
                href: "/favicon/favicon-192x192.png"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 73,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                rel: "apple-touch-icon",
                href: "/favicon/apple-touch-icon.png"
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 79,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("link", {
                rel: "alternate",
                hrefLang: "en-in",
                href: meta?.canonical || canonical
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 80,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$WebPageSchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: meta?.title || "",
                description: meta?.description || "",
                url: meta?.canonical || canonical
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 85,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$NewsMediaOrganizationSchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].Brand,
                clientLink: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink}/`,
                logoUrl: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink}/RR final logo.png`,
                address: {
                    streetAddress: "RPSG Lifestyle Media, Thapar House, 3rd floor, Janpath Lane",
                    addressLocality: "New Delhi",
                    addressRegion: "India",
                    postalCode: "110 001"
                },
                contact: {
                    telephone: "+91–11–23486700",
                    contactType: "Customer Service",
                    areaServed: "IN",
                    availableLanguage: "English",
                    hoursAvailable: {
                        opens: "09:00",
                        closes: "19:00"
                    }
                },
                sameAs: [
                    "https://www.facebook.com/robbreporterindia",
                    "https://www.instagram.com/robbreporterindia/",
                    "https://twitter.com/robbreportindia",
                    "https://www.youtube.com/@robbreportIndia"
                ]
            }, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 90,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$SiteNavigationSchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/components/seo/SeoHeader.jsx",
                lineNumber: 118,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/seo/SeoHeader.jsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = SeoHeader;
}}),
"[project]/src/components/seo/BreadcrumbSchema.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/next/head.js [external] (next/head.js, cjs)");
;
;
;
const BreadcrumbSchema = ({ itemList })=>{
    const breadcrumb = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
            {
                "@type": "ListItem",
                position: 1,
                item: {
                    "@id": __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink,
                    name: "Home"
                }
            }
        ]
    };
    if (itemList && itemList.length > 0) {
        itemList.forEach((item, index)=>{
            breadcrumb.itemListElement.push({
                "@type": "ListItem",
                position: index + 2,
                item: {
                    "@id": __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink + item.slug,
                    name: item.name
                }
            });
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
            type: "application/ld+json",
            dangerouslySetInnerHTML: {
                __html: JSON.stringify(breadcrumb)
            }
        }, void 0, false, {
            fileName: "[project]/src/components/seo/BreadcrumbSchema.jsx",
            lineNumber: 35,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/seo/BreadcrumbSchema.jsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = BreadcrumbSchema;
}}),
"[project]/src/components/seo/ImageGallerySchema.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/next/head.js [external] (next/head.js, cjs)");
;
;
const ImageGallerySchema = ({ title, description, url, datePublished, data })=>{
    const images = data?.map((item)=>({
            "@type": "ImageObject",
            url: item?.image || ""
        }));
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "ImageGallery",
        url: url,
        datePublished: datePublished,
        mainEntityOfPage: {
            "@type": "WebPage",
            "@id": url,
            headline: title,
            description: description
        },
        image: images
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
            type: "application/ld+json",
            dangerouslySetInnerHTML: {
                __html: JSON.stringify(schemaData)
            }
        }, void 0, false, {
            fileName: "[project]/src/components/seo/ImageGallerySchema.jsx",
            lineNumber: 30,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/seo/ImageGallerySchema.jsx",
        lineNumber: 29,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ImageGallerySchema;
}}),
"[project]/src/components/seo/MediaGallerySchema.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/next/head.js [external] (next/head.js, cjs)");
;
;
const MediaGallerySchema = ({ title, description, data })=>{
    const associatedMedia = data?.map((item)=>({
            "@type": "ImageObject",
            name: item?.title || "",
            thumbnailUrl: item?.image || ""
        }));
    const schemaData = {
        "@context": "https://schema.org",
        "@type": "MediaGallery",
        headline: title,
        description: description,
        mainEntityOfPage: {
            "@type": "ImageGallery",
            associatedMedia: associatedMedia
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
            type: "application/ld+json",
            dangerouslySetInnerHTML: {
                __html: JSON.stringify(schemaData)
            }
        }, void 0, false, {
            fileName: "[project]/src/components/seo/MediaGallerySchema.jsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/seo/MediaGallerySchema.jsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = MediaGallerySchema;
}}),
"[project]/src/components/amp/ampCss.js [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ampCSS": (()=>ampCSS),
    "ampNavbarCSS": (()=>ampNavbarCSS),
    "webStoryDetailCSS": (()=>webStoryDetailCSS)
});
const ampCSS = `
.story-top {
    padding: 2rem 0 0;
    position: sticky;
    top: 50px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    z-index: 9;
}
.container {
    padding-right: 20px;
    padding-left: 20px;
}
.story-top h1 {
    font-size: 30px;
    line-height: 1;
    margin-bottom: 0;
    font-weight: 400;
    display: block;
    color: #000;
    font-style: normal;
}
`;
const ampNavbarCSS = `
    .nav-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background: white;
      position: relative;
    }
    .logo {
      font-size: 1.5rem;
      font-weight: bold;
      text-decoration: none;
      color: #000;
    }
    .hamburger {
      display: flex;
      flex-direction: column;
      cursor: pointer;
      padding: 0.5rem;
    }
    .hamburger .line {
      width: 25px;
      height: 3px;
      background-color: #000;
      margin: 3px 0;
      transition: 0.3s;
    }
    .hamburger.is-active .line:nth-child(1) {
      transform: rotate(-45deg) translate(-5px, 6px);
    }
    .hamburger.is-active .line:nth-child(2) {
      opacity: 0;
    }
    .hamburger.is-active .line:nth-child(3) {
      transform: rotate(45deg) translate(-5px, -6px);
    }
    .mob-menu {
      background: white;
      padding: 1rem;
      width: 300px;
    }
    .mob-menu ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .mob-menu li {
      margin: 0.5rem 0;
    }
    .mob-menu a {
      text-decoration: none;
      color: #000;
      font-size: 1rem;
    }
`;
const webStoryDetailCSS = `
				/* AMP Web Stories Font Configuration */
				@font-face {
					font-family: "NeueHaasDisplayBold";
					src: url(/Assets/NeueHaasDisplayBold.ttf);
					font-display: swap;
				}
				@font-face {
					font-family: "Bitter";
					src: url(/Assets/Bitter-VariableFont_wght.ttf);
					font-display: swap;
				}
				
				.brand-logo {
					position: absolute;
					top: 15px;
					left: 15px;
					color: white;
					text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
					z-index: 10;
				}

				.back-button {
					position: absolute;
					top: 20px;
					right: 20px;
					z-index: 10;
				}

				.back-link {
					color: white;
					text-decoration: none;
					background: rgba(0, 0, 0, 0.5);
					padding: 8px 12px;
					border-radius: 20px;
					font-size: 14px;
					text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
					transition: background 0.3s ease;
					border: none;
					cursor: pointer;
					font-family: "Bitter", serif;
				}
        .next-story-preview{
          padding-bottom: 4rem;
        }
        .next-story-preview h2{
          text-align: center;
          font-size: 24px;
          letter-spacing: 1px;
					font-family: "NeueHaasDisplayBold", sans-serif;
					font-weight: 700;
        }
				.back-link:hover {
					background: rgba(0, 0, 0, 0.7);
				}
				.story-content {
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					padding: 15px;
					background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.8) 100%);
				}
				.story-text {
					color: white;
					text-align: left;
				}
				/* Headlines use primary font (NeueHaasDisplayBold) */
				.story-text h1 {
					font-size: 24px;
					font-family: "NeueHaasDisplayBold", sans-serif;
					margin-bottom: 10px;
					font-weight: 700;
          letter-spacing: 1px;
					line-height: 1.1;
				}
				/* Body text uses secondary font (Bitter) */
				.story-text div {
					font-family: "Bitter", serif;
					font-size: 16px;
					line-height: 1.3;
					font-weight: 400;
				}
				.story-text p {
					font-family: "Bitter", serif;
					font-size: 16px;
					line-height: 1.3;
					font-weight: 400;
				}
        .story-text p a{
        color: #fff;
        }
				.story-text small {
					display: block;
					margin-top: 8px;
					opacity: 0.8;
					font-family: "Bitter", serif;
					font-size: 12px;
					font-weight: 300;
				}
                [template=vertical]{
                align-content: end;
                }

        .next-story-preview {
          color: #fff
        }
				/* Force show AMP story navigation buttons on mobile */
				:global(.amphtml-story-button-container) {
					display: block;
					visibility: visible;
					opacity: 1;
				}

				:global(.amphtml-story-button-move) {
					display: block;
					visibility: visible;
					opacity: 1;
				}

				@media (max-width: 768px) {
					:global(.amphtml-story-button-container) {
						display: block;
						visibility: visible;
						opacity: 1;
					}
				}
			
			`;
}}),
"[project]/src/pages/webstories/[category]/[slug]/index.jsx [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>__TURBOPACK__default__export__),
    "getServerSideProps": (()=>getServerSideProps)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$styled$2d$jsx$2f$style$2e$js__$5b$external$5d$__$28$styled$2d$jsx$2f$style$2e$js$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/styled-jsx/style.js [external] (styled-jsx/style.js, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
// import { getWebStories } from "@/pages/api/WebStoriesApi"; // TODO: Uncomment when API is ready
var __TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/next/head.js [external] (next/head.js, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Util.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$SeoHeader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/SeoHeader.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$BreadcrumbSchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/BreadcrumbSchema.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$ImageGallerySchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/ImageGallerySchema.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$MediaGallerySchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/seo/MediaGallerySchema.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/Constants.jsx [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/amp/ampCss.js [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
;
const config = {
    amp: true
};
// DUMMY DATA - TODO: Remove when API is ready
const DUMMY_WEB_STORIES = {
    "luxury-cars-2024": {
        title: "Top 5 Luxury Cars of 2024",
        coverImg: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
        altName: "Luxury Cars 2024",
        timestamp: "2024-01-15T10:00:00Z",
        slides: [
            {
                title: "Ferrari SF90 Stradale",
                description: "<p>The pinnacle of Ferrari engineering, combining hybrid technology with pure performance.</p>",
                image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
                altName: "Ferrari SF90 Stradale",
                contributor: [
                    "Ferrari Press"
                ],
                timestamp: "2024-01-15T10:00:00Z"
            },
            {
                title: "Lamborghini Revuelto",
                description: "<p>The new flagship from Lamborghini featuring a revolutionary V12 hybrid powertrain.</p>",
                image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
                altName: "Lamborghini Revuelto",
                contributor: [
                    "Lamborghini Media"
                ],
                timestamp: "2024-01-15T10:00:00Z"
            },
            {
                title: "Rolls-Royce Spectre",
                description: "<p>The first fully electric Rolls-Royce, maintaining the brand's legendary luxury.</p>",
                image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
                altName: "Rolls-Royce Spectre",
                contributor: [
                    "Rolls-Royce Press"
                ],
                timestamp: "2024-01-15T10:00:00Z"
            },
            {
                title: "Bentley Continental GT Speed",
                description: "<p>The most powerful Continental GT ever, with unmatched grand touring capabilities.</p>",
                image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
                altName: "Bentley Continental GT Speed",
                contributor: [
                    "Bentley Motors"
                ],
                timestamp: "2024-01-15T10:00:00Z"
            },
            {
                title: "McLaren 750S",
                description: "<p>The latest in McLaren's Super Series, delivering track-focused performance for the road.</p>",
                image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=720&h=1280&fit=crop",
                altName: "McLaren 750S",
                contributor: [
                    "McLaren Automotive"
                ],
                timestamp: "2024-01-15T10:00:00Z"
            }
        ]
    },
    "luxury-watches-2024": {
        title: "Exquisite Timepieces of 2024",
        coverImg: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
        altName: "Luxury Watches 2024",
        timestamp: "2024-01-20T14:00:00Z",
        slides: [
            {
                title: "Patek Philippe Nautilus",
                description: "<p>The iconic sports watch that defines luxury horology excellence.</p>",
                image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
                altName: "Patek Philippe Nautilus",
                contributor: [
                    "Patek Philippe"
                ],
                timestamp: "2024-01-20T14:00:00Z"
            },
            {
                title: "Rolex Daytona",
                description: "<p>The legendary chronograph that has become synonymous with racing heritage.</p>",
                image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
                altName: "Rolex Daytona",
                contributor: [
                    "Rolex SA"
                ],
                timestamp: "2024-01-20T14:00:00Z"
            },
            {
                title: "Audemars Piguet Royal Oak",
                description: "<p>The revolutionary design that changed luxury sports watches forever.</p>",
                image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
                altName: "Audemars Piguet Royal Oak",
                contributor: [
                    "Audemars Piguet"
                ],
                timestamp: "2024-01-20T14:00:00Z"
            },
            {
                title: "Vacheron Constantin Overseas",
                description: "<p>Swiss craftsmanship meets contemporary design in this exceptional timepiece.</p>",
                image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
                altName: "Vacheron Constantin Overseas",
                contributor: [
                    "Vacheron Constantin"
                ],
                timestamp: "2024-01-20T14:00:00Z"
            },
            {
                title: "Richard Mille RM 11-03",
                description: "<p>Cutting-edge materials and innovative design define this modern masterpiece.</p>",
                image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=720&h=1280&fit=crop",
                altName: "Richard Mille RM 11-03",
                contributor: [
                    "Richard Mille"
                ],
                timestamp: "2024-01-20T14:00:00Z"
            }
        ]
    },
    "luxury-yachts-2024": {
        title: "Magnificent Yachts of 2024",
        coverImg: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
        altName: "Luxury Yachts 2024",
        timestamp: "2024-01-25T16:00:00Z",
        slides: [
            {
                title: "Azzam Superyacht",
                description: "<p>The world's largest private yacht, a floating palace of unprecedented luxury.</p>",
                image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
                altName: "Azzam Superyacht",
                contributor: [
                    "Lürssen Yachts"
                ],
                timestamp: "2024-01-25T16:00:00Z"
            },
            {
                title: "Eclipse Yacht",
                description: "<p>A masterpiece of naval architecture with unparalleled amenities and security.</p>",
                image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
                altName: "Eclipse Yacht",
                contributor: [
                    "Blohm+Voss"
                ],
                timestamp: "2024-01-25T16:00:00Z"
            },
            {
                title: "Dilbar Superyacht",
                description: "<p>The largest yacht by gross tonnage, featuring extraordinary interior volume.</p>",
                image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
                altName: "Dilbar Superyacht",
                contributor: [
                    "Lürssen Yachts"
                ],
                timestamp: "2024-01-25T16:00:00Z"
            },
            {
                title: "Sailing Yacht A",
                description: "<p>The world's largest sailing yacht, combining traditional sailing with modern luxury.</p>",
                image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
                altName: "Sailing Yacht A",
                contributor: [
                    "Nobiskrug"
                ],
                timestamp: "2024-01-25T16:00:00Z"
            },
            {
                title: "Octopus Yacht",
                description: "<p>An explorer yacht designed for adventure and luxury in the world's most remote locations.</p>",
                image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=720&h=1280&fit=crop",
                altName: "Octopus Yacht",
                contributor: [
                    "Lürssen Yachts"
                ],
                timestamp: "2024-01-25T16:00:00Z"
            }
        ]
    },
    "luxury-real-estate-2024": {
        title: "Extraordinary Properties of 2024",
        coverImg: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
        altName: "Luxury Real Estate 2024",
        timestamp: "2024-02-01T12:00:00Z",
        slides: [
            {
                title: "Penthouse in Manhattan",
                description: "<p>A sky-high sanctuary offering breathtaking views of the New York City skyline.</p>",
                image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
                altName: "Manhattan Penthouse",
                contributor: [
                    "Sotheby's Realty"
                ],
                timestamp: "2024-02-01T12:00:00Z"
            },
            {
                title: "Villa in French Riviera",
                description: "<p>Mediterranean elegance meets modern luxury in this stunning coastal estate.</p>",
                image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
                altName: "French Riviera Villa",
                contributor: [
                    "Christie's Real Estate"
                ],
                timestamp: "2024-02-01T12:00:00Z"
            },
            {
                title: "Estate in Aspen",
                description: "<p>A mountain retreat offering world-class skiing and year-round luxury amenities.</p>",
                image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
                altName: "Aspen Estate",
                contributor: [
                    "Douglas Elliman"
                ],
                timestamp: "2024-02-01T12:00:00Z"
            },
            {
                title: "Mansion in Beverly Hills",
                description: "<p>Hollywood glamour and contemporary design converge in this iconic estate.</p>",
                image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
                altName: "Beverly Hills Mansion",
                contributor: [
                    "The Agency"
                ],
                timestamp: "2024-02-01T12:00:00Z"
            },
            {
                title: "Castle in Scotland",
                description: "<p>Historic grandeur preserved with modern amenities in the Scottish Highlands.</p>",
                image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=720&h=1280&fit=crop",
                altName: "Scottish Castle",
                contributor: [
                    "Savills"
                ],
                timestamp: "2024-02-01T12:00:00Z"
            }
        ]
    },
    "luxury-jets-2024": {
        title: "Private Jets Redefining Luxury",
        coverImg: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop",
        altName: "Luxury Private Jets 2024",
        timestamp: "2024-02-05T18:00:00Z",
        slides: [
            {
                title: "Gulfstream G700",
                description: "<p>The flagship of business aviation, offering unmatched range and luxury.</p>",
                image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop",
                altName: "Gulfstream G700",
                contributor: [
                    "Gulfstream Aerospace"
                ],
                timestamp: "2024-02-05T18:00:00Z"
            },
            {
                title: "Bombardier Global 7500",
                description: "<p>The world's largest and longest-range business jet with four living spaces.</p>",
                image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop",
                altName: "Bombardier Global 7500",
                contributor: [
                    "Bombardier"
                ],
                timestamp: "2024-02-05T18:00:00Z"
            },
            {
                title: "Dassault Falcon 10X",
                description: "<p>French engineering excellence with the tallest and widest cabin in business aviation.</p>",
                image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?w=720&h=1280&fit=crop",
                altName: "Dassault Falcon 10X",
                contributor: [
                    "Dassault Aviation"
                ],
                timestamp: "2024-02-05T18:00:00Z"
            }
        ]
    }
};
const WebStoryDetail = ({ data, nextData, breadcrumbs, meta, pathname })=>{
    // AMP-compliant CSS without i-amphtml- prefixes
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$styled$2d$jsx$2f$style$2e$js__$5b$external$5d$__$28$styled$2d$jsx$2f$style$2e$js$2c$__cjs$29$__["default"], {
                id: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash,
                children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"]
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$next$2f$head$2e$js__$5b$external$5d$__$28$next$2f$head$2e$js$2c$__cjs$29$__["default"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("meta", {
                        name: "amp-to-amp-navigation",
                        content: "AMP-Redirect",
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 271,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
                        async: true,
                        "custom-element": "amp-story",
                        src: "https://cdn.ampproject.org/v0/amp-story-1.0.js",
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 272,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
                        async: true,
                        "custom-element": "amp-story-auto-ads",
                        src: "https://cdn.ampproject.org/v0/amp-story-auto-ads-0.1.js",
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 277,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$SeoHeader$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                        meta: meta,
                        pathname: pathname
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 287,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$BreadcrumbSchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                        itemList: breadcrumbs
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 288,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$ImageGallerySchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                        title: data?.slides?.[0]?.title || "",
                        description: data?.slides?.[0]?.description || "",
                        url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Constants$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["Const"].ClientLink + pathname,
                        datePublished: data?.timestamp || "",
                        data: data?.slides || []
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 289,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$seo$2f$MediaGallerySchema$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                        title: data?.slides?.[0]?.title || "",
                        description: data?.slides?.[0]?.description || "",
                        data: data?.slides || []
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 296,
                        columnNumber: 5
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                lineNumber: 270,
                columnNumber: 4
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story", {
                standalone: "",
                title: data?.title || "",
                publisher: "Robb Report India",
                "publisher-logo-src": "/RR final logo.png",
                "poster-portrait-src": data?.coverImg || "",
                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                children: [
                    data?.slides?.map((slide, index)=>{
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["default"].Fragment, {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-page", {
                                id: `page-${index}`,
                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-grid-layer", {
                                        template: "fill",
                                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-img", {
                                            src: slide?.image || "",
                                            width: "720",
                                            height: "1280",
                                            layout: "fill",
                                            alt: slide?.altName || "",
                                            className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                                        }, void 0, false, {
                                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                            lineNumber: 315,
                                            columnNumber: 10
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                        lineNumber: 314,
                                        columnNumber: 9
                                    }, this),
                                    index === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-grid-layer", {
                                        template: "vertical",
                                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "brand-logo",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-img", {
                                                src: "/RR final logo.png",
                                                width: "200",
                                                height: "80",
                                                layout: "fixed",
                                                alt: "Robb Report India Logo",
                                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                                            }, void 0, false, {
                                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                lineNumber: 327,
                                                columnNumber: 12
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                            lineNumber: 326,
                                            columnNumber: 11
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                        lineNumber: 325,
                                        columnNumber: 10
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-grid-layer", {
                                        template: "vertical",
                                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "story-content",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "story-text",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h1", {
                                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                                    children: slide?.title || ""
                                                }, void 0, false, {
                                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                    lineNumber: 340,
                                                    columnNumber: 11
                                                }, this),
                                                slide?.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    dangerouslySetInnerHTML: {
                                                        __html: slide.description
                                                    },
                                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                                                }, void 0, false, {
                                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                    lineNumber: 342,
                                                    columnNumber: 12
                                                }, this),
                                                slide?.contributor?.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("small", {
                                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                                    children: [
                                                        "Photo Credit: ",
                                                        slide.contributor.join(", ")
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                    lineNumber: 345,
                                                    columnNumber: 12
                                                }, this),
                                                index === 0 && slide?.timestamp && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("small", {
                                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                                    children: [
                                                        "Published: ",
                                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$Util$2e$jsx__$5b$ssr$5d$__$28$ecmascript$29$__["dateFormateWithTimeShort"])(slide.timestamp)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                                    lineNumber: 348,
                                                    columnNumber: 12
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                            lineNumber: 339,
                                            columnNumber: 10
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                        lineNumber: 338,
                                        columnNumber: 9
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                lineNumber: 313,
                                columnNumber: 8
                            }, this)
                        }, index, false, {
                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                            lineNumber: 312,
                            columnNumber: 7
                        }, this);
                    }),
                    nextData?.slug && nextData?.coverImg && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-page", {
                        id: "next-story-preview",
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-grid-layer", {
                                template: "fill",
                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-img", {
                                    src: nextData.coverImg,
                                    width: "720",
                                    height: "1280",
                                    layout: "fill",
                                    alt: nextData.altName || "Next Story",
                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                                }, void 0, false, {
                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                    lineNumber: 361,
                                    columnNumber: 8
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                lineNumber: 360,
                                columnNumber: 7
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-grid-layer", {
                                template: "vertical",
                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "next-story-overlay story-content",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "next-story-preview",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h2", {
                                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                        children: nextData.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                        lineNumber: 373,
                                        columnNumber: 9
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                    lineNumber: 371,
                                    columnNumber: 8
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                lineNumber: 370,
                                columnNumber: 7
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-cta-layer", {
                                className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("a", {
                                    href: nextData.slug,
                                    className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}` + " " + "next-story-btn",
                                    children: "Read Next Story"
                                }, void 0, false, {
                                    fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                    lineNumber: 378,
                                    columnNumber: 8
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                                lineNumber: 377,
                                columnNumber: 7
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 359,
                        columnNumber: 6
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("amp-story-auto-ads", {
                        className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("script", {
                            type: "application/json",
                            dangerouslySetInnerHTML: {
                                __html: JSON.stringify({
                                    "ad-attributes": {
                                        type: "doubleclick",
                                        "data-slot": "/23290324739/RobbReport-AMP-Stories"
                                    }
                                })
                            },
                            className: `jsx-${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$amp$2f$ampCss$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["webStoryDetailCSS"].__hash}`
                        }, void 0, false, {
                            fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                            lineNumber: 386,
                            columnNumber: 6
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                        lineNumber: 385,
                        columnNumber: 5
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/pages/webstories/[category]/[slug]/index.jsx",
                lineNumber: 303,
                columnNumber: 4
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = WebStoryDetail;
WebStoryDetail.config = {
    amp: true
};
async function getServerSideProps(context) {
    const { slug } = context.params;
    // TODO: Replace with actual API call when ready
    // const url = `/${slug}`;
    // try {
    //   const storiesRes = await getWebStories(url);
    //   if (!storiesRes || Object.keys(storiesRes.data).length === 0) {
    //     return { notFound: true };
    //   }
    //   const storyData = storiesRes.data.current.data;
    //   // Add cover slide as first slide
    //   const newObject = {
    //     title: storyData.title,
    //     description: "",
    //     image: storyData.coverImg,
    //     altName: storyData.altName,
    //     sequence: -1,
    //     contributor: [],
    //     timestamp: storyData.timestamp,
    //   };
    //   if (Array.isArray(storyData.slides)) {
    //     storyData.slides.unshift(newObject);
    //   }
    //   return {
    //     props: {
    //       data: storyData ?? {},
    //       previousData: storiesRes.data.previous ?? {},
    //       nextData: storiesRes.data.next ?? {},
    //       breadcrumbs: storiesRes.data.current.breadcrumbs ?? [],
    //       tag: storiesRes.data.current.tag ?? [],
    //       meta: storiesRes.data.current.meta ?? {},
    //       pathname: context.resolvedUrl || context.req.url || "",
    //     },
    //   };
    // } catch (error) {
    //   console.error("Error fetching data:", error.message);
    //   return { notFound: true };
    // }
    // DUMMY DATA IMPLEMENTATION - Remove when API is ready
    try {
        const storyData = DUMMY_WEB_STORIES[slug];
        if (!storyData) {
            return {
                notFound: true
            };
        }
        // Add cover slide as first slide
        const newObject = {
            title: storyData.title,
            description: "",
            image: storyData.coverImg,
            altName: storyData.altName,
            sequence: -1,
            contributor: [],
            timestamp: storyData.timestamp
        };
        const slidesWithCover = [
            newObject,
            ...storyData.slides
        ];
        // Get next story for preview (simple logic for demo)
        const storyKeys = Object.keys(DUMMY_WEB_STORIES);
        const currentIndex = storyKeys.indexOf(slug);
        const nextIndex = (currentIndex + 1) % storyKeys.length;
        const nextStoryKey = storyKeys[nextIndex];
        const nextStory = DUMMY_WEB_STORIES[nextStoryKey];
        return {
            props: {
                data: {
                    ...storyData,
                    slides: slidesWithCover
                },
                nextData: {
                    title: nextStory.title,
                    slug: `/webstories/all/${nextStoryKey}`,
                    coverImg: nextStory.coverImg,
                    altName: nextStory.altName
                },
                breadcrumbs: [
                    {
                        name: "Web Stories",
                        slug: "/webstories"
                    },
                    {
                        name: "All",
                        slug: "/webstories/all"
                    }
                ],
                tag: [
                    "luxury",
                    "lifestyle"
                ],
                meta: {
                    title: storyData.title + " | Robb Report India",
                    description: storyData.slides[0]?.description?.replace(/<[^>]*>/g, "") || storyData.title,
                    keywords: [
                        "luxury",
                        "lifestyle",
                        "robb report"
                    ],
                    robots: "index,follow"
                },
                pathname: context.resolvedUrl || context.req.url || ""
            }
        };
    } catch (error) {
        console.error("Error with dummy data:", error.message);
        return {
            notFound: true
        };
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__96a1424e._.js.map