/* [project]/src/styles/swiperCardSection.css [client] (css) */
.swipperCardsectionlatest {
  position: relative;
  padding: 5rem 0 0;
}

@media screen and (width <= 767px) {
  .swipperCardsectionlatest {
    padding: 2rem 0 0;
  }
}

.latestCardPostHeading {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.editor-picks__secondary-inner-wrapper {
  position: relative;
  overflow: hidden;
}

.editor-picks__secondary-pick {
  text-align: center;
  margin-top: 1.5625rem;
  position: relative;
}

.editor-picks__secondary-pick .featured-image {
  overflow: hidden;
  max-height: 260px !important;
}

.editor-picks__secondary-pick .featured-image img {
  aspect-ratio: 16 / 9;
}

.editor-picks__secondary-pick .entry__category {
  font-size: 11px;
  line-height: 15px;
}

.sectioner--editor-picks .entry {
  color: #000;
}

.editor-picks__secondary-pick .entry__heading {
  font-size: 23px;
  line-height: 29px;
}

.entry__heading {
  transition: opacity .3s ease-in;
}

.arrows_arrow {
  position: relative;
  z-index: 0;
  display: inline-flex;
  width: 45px;
  height: 45px;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1px solid #737373;
  transition: all .3s;
  cursor: pointer;
  overflow: hidden;
  color: #737373;
  background-color: #0000;
}

.arrows_arrow svg {
  position: relative;
  z-index: 1;
  font-size: 16px;
  transition: color .3s;
  opacity: 1 !important;
}

.arrows_arrow:hover {
  border: 2px solid #000;
}

.arrows_arrow:hover .arrows_arrow svg {
  color: #000;
  font-weight: 900;
}

@media only screen and (width >= 70.625rem) {
  .editor-picks__secondary-pick .entry__category {
    font-size: 15px;
    line-height: 22px;
  }
}

/*# sourceMappingURL=src_styles_swiperCardSection_73511378.css.map*/