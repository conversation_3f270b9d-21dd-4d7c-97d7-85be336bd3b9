/* [project]/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css [client] (css) */
.tweet-container-module__CmFQMq__root {
  width: 100%;
  min-width: 250px;
  max-width: 550px;
  overflow: hidden;
  color: var(--tweet-font-color);
  font-family: var(--tweet-font-family);
  font-weight: 400;
  box-sizing: border-box;
  border: var(--tweet-border);
  border-radius: 12px;
  margin: var(--tweet-container-margin);
  background-color: var(--tweet-bg-color);
  transition-property: background-color, box-shadow;
  transition-duration: .2s;
}

.tweet-container-module__CmFQMq__root:hover {
  background-color: var(--tweet-bg-color-hover);
}

.tweet-container-module__CmFQMq__article {
  position: relative;
  box-sizing: inherit;
  padding: .75rem 1rem;
}

/*# sourceMappingURL=node_modules_react-tweet_dist_twitter-theme_tweet-container_module_73511378.css.map*/